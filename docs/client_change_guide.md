# 🔧 客户端适配指南

## 📋 变更概述

**影响接口**: 性能测试上传API  
**变更时间**: 2025-07-30  
**影响级别**: 🔴 破坏性变更（必须适配）

## 🔄 主要变更

### 上传参数变更
| 原参数 | 新参数 | 变更类型 |
|-------|-------|---------|
| `case_log_tos_url` | `case_log_tos_urls` | 字符串 → JSON |
| `perf_data_tos_url` | `perf_data_tos_urls` | 字符串 → JSON |
| `trace_data_tos_url` | ❌ 删除 | - |
| `byteio_data_tos_url` | ❌ 删除 | - |

### 参数格式变更
```json
// 旧格式
{
  "case_log_tos_url": "https://tos.example.com/case.log",
  "perf_data_tos_url": "https://tos.example.com/perf_data.json",
  "trace_data_tos_url": "https://tos.example.com/trace.data",
  "byteio_data_tos_url": "https://tos.example.com/byteio.data"
}

// 新格式
{
  "case_log_tos_urls": {
    "case.log": "https://tos.example.com/case.log"
  },
  "perf_data_tos_urls": {
    "perf_data.json": "https://tos.example.com/perf_data.json"
  }
  // trace_data_tos_url 和 byteio_data_tos_url 参数已删除
}
```

## 🎯 影响的API接口

**接口路径**: `/api/perf_ui_auto/upload_case_run_detail`
- 请求参数格式变更
- 支持多文件上传

## 💻 客户端代码适配

### 1. Python客户端适配

```python
# ❌ 旧代码
def upload_case_run_detail(case_log_url, perf_data_url, trace_data_url, byteio_data_url):
    params = {
        "case_log_tos_url": case_log_url,
        "perf_data_tos_url": perf_data_url,
        "trace_data_tos_url": trace_data_url,  # 已删除
        "byteio_data_tos_url": byteio_data_url,  # 已删除
        # ... 其他参数
    }
    return requests.post("/api/perf_ui_auto/upload_case_run_detail", json=params)

# ✅ 新代码
def upload_case_run_detail(case_log_url=None, perf_data_url=None):
    params = {
        "case_log_tos_urls": {
            "case.log": case_log_url
        } if case_log_url else None,
        "perf_data_tos_urls": {
            "perf_data.json": perf_data_url  
        } if perf_data_url else None,
        # trace_data_tos_url 和 byteio_data_tos_url 参数已删除
        # ... 其他参数
    }
    return requests.post("/api/perf_ui_auto/upload_case_run_detail", json=params)
```

### 2. 多文件上传支持

```python
def upload_case_run_detail_multi_files(case_log_files=None, perf_data_files=None):
    """
    支持多文件上传
    
    Args:
        case_log_files: dict, {"case.log": "url1", "error.log": "url2"}
        perf_data_files: dict, {"perf_data.json": "url1", "metrics.json": "url2"}
    """
    params = {
        "case_log_tos_urls": case_log_files if case_log_files else None,
        "perf_data_tos_urls": perf_data_files if perf_data_files else None,
        # ... 其他参数
    }
    return requests.post("/api/perf_ui_auto/upload_case_run_detail", json=params)

# 使用示例
case_logs = {
    "case.log": "https://tos.example.com/case.log",
    "error.log": "https://tos.example.com/error.log"
}
perf_data = {
    "perf_data.json": "https://tos.example.com/perf_data.json"
}

upload_case_run_detail_multi_files(case_logs, perf_data)
```

### 3. TOS上传适配

```python
import os
from typing import Dict, List, Optional

def upload_files_to_tos_and_get_urls(file_paths: List[str], 
                                   file_type: str = "case_log") -> Optional[Dict[str, str]]:
    """
    上传文件到TOS并返回JSON格式的URL映射
    
    Args:
        file_paths: 文件路径列表
        file_type: "case_log" 或 "perf_data"
    
    Returns:
        dict: {"filename": "tos_url", ...} 或 None
    """
    if not file_paths:
        return None
        
    url_mapping = {}
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"警告: 文件不存在 {file_path}")
            continue
            
        filename = os.path.basename(file_path)
        
        try:
            # 上传到TOS (这里需要根据实际的TOS SDK调用)
            tos_url = upload_to_tos(file_path)
            
            if tos_url:
                url_mapping[filename] = tos_url
                print(f"上传成功: {filename} -> {tos_url}")
            else:
                print(f"上传失败: {filename}")
                
        except Exception as e:
            print(f"上传异常: {filename}, 错误: {e}")
    
    return url_mapping if url_mapping else None

# 完整的上传流程
def upload_perf_test_result(case_log_files: List[str], 
                          perf_data_files: List[str],
                          **other_params):
    """
    完整的性能测试结果上传流程
    """
    # 1. 上传日志文件到TOS
    case_log_urls = upload_files_to_tos_and_get_urls(case_log_files, "case_log")
    
    # 2. 上传性能数据文件到TOS  
    perf_data_urls = upload_files_to_tos_and_get_urls(perf_data_files, "perf_data")
    
    # 3. 调用API上传结果
    params = {
        "case_log_tos_urls": case_log_urls,
        "perf_data_tos_urls": perf_data_urls,
        **other_params
    }
    
    response = requests.post("/api/perf_ui_auto/upload_case_run_detail", json=params)
    return response

# 使用示例
case_logs = ["./logs/case.log", "./logs/error.log"]
perf_data = ["./data/perf_data.json", "./data/metrics.json"]

result = upload_perf_test_result(
    case_log_files=case_logs,
    perf_data_files=perf_data,
    # 其他参数...
    case_id=123,
    run_id=456
)
```

### 4. 向后兼容处理

```python
def upload_case_run_detail_compatible(case_log_url=None, perf_data_url=None, 
                                    case_log_urls=None, perf_data_urls=None,
                                    **other_params):
    """
    兼容新旧格式的上传函数
    
    Args:
        case_log_url: 旧格式，单个日志文件URL
        perf_data_url: 旧格式，单个性能数据文件URL  
        case_log_urls: 新格式，多个日志文件URL字典
        perf_data_urls: 新格式，多个性能数据文件URL字典
    """
    # 处理case_log
    if case_log_urls:
        final_case_log_urls = case_log_urls
    elif case_log_url:
        final_case_log_urls = {"case.log": case_log_url}
    else:
        final_case_log_urls = None
    
    # 处理perf_data
    if perf_data_urls:
        final_perf_data_urls = perf_data_urls
    elif perf_data_url:
        final_perf_data_urls = {"perf_data.json": perf_data_url}
    else:
        final_perf_data_urls = None
    
    params = {
        "case_log_tos_urls": final_case_log_urls,
        "perf_data_tos_urls": final_perf_data_urls,
        **other_params
    }
    
    return requests.post("/api/perf_ui_auto/upload_case_run_detail", json=params)

# 兼容性使用示例
# 旧方式调用
upload_case_run_detail_compatible(
    case_log_url="https://tos.example.com/case.log",
    perf_data_url="https://tos.example.com/perf_data.json"
)

# 新方式调用
upload_case_run_detail_compatible(
    case_log_urls={"case.log": "url1", "error.log": "url2"},
    perf_data_urls={"perf_data.json": "url1"}
)
```

### 5. Java客户端适配

```java
// ✅ Java客户端适配示例
import java.util.HashMap;
import java.util.Map;

public class PerfTestUploader {
    
    // 新格式上传
    public void uploadCaseRunDetail(Map<String, String> caseLogUrls, 
                                  Map<String, String> perfDataUrls) {
        Map<String, Object> params = new HashMap<>();
        params.put("case_log_tos_urls", caseLogUrls);
        params.put("perf_data_tos_urls", perfDataUrls);
        // ... 其他参数
        
        // 发送HTTP请求
        httpClient.post("/api/perf_ui_auto/upload_case_run_detail", params);
    }
    
    // 兼容旧格式
    public void uploadCaseRunDetailCompatible(String caseLogUrl, String perfDataUrl) {
        Map<String, String> caseLogUrls = null;
        Map<String, String> perfDataUrls = null;
        
        if (caseLogUrl != null && !caseLogUrl.isEmpty()) {
            caseLogUrls = new HashMap<>();
            caseLogUrls.put("case.log", caseLogUrl);
        }
        
        if (perfDataUrl != null && !perfDataUrl.isEmpty()) {
            perfDataUrls = new HashMap<>();
            perfDataUrls.put("perf_data.json", perfDataUrl);
        }
        
        uploadCaseRunDetail(caseLogUrls, perfDataUrls);
    }
}
```

## 📝 迁移检查清单

### 必须修改的地方
- [ ] 更新上传接口调用代码
- [ ] 移除 `trace_data_tos_url` 和 `byteio_data_tos_url` 上传逻辑
- [ ] 适配新的JSON格式参数
- [ ] 更新TOS上传后的URL组装逻辑

### 建议优化的地方
- [ ] 支持多文件上传（可选）
- [ ] 添加向后兼容处理
- [ ] 添加文件上传失败重试机制
- [ ] 添加上传进度显示

### 测试检查清单
- [ ] 测试单文件上传
- [ ] 测试多文件上传
- [ ] 测试空文件情况
- [ ] 测试网络异常情况
- [ ] 进行集成测试

## ⚠️ 注意事项

1. **参数验证**: 确保JSON格式正确，key为文件名，value为TOS URL
2. **空值处理**: 如果没有文件，传 `null` 而不是空对象 `{}`
3. **文件名规范**: 建议使用有意义的文件名作为JSON的key
4. **错误处理**: 添加TOS上传失败的错误处理逻辑
5. **性能考虑**: 多文件上传时考虑并发上传以提高效率

## 🆘 常见问题

**Q: 如何处理TOS上传失败的情况？**
A: 建议添加重试机制，并在最终上传API时只包含成功上传的文件

**Q: 文件名可以包含中文吗？**
A: 可以，但建议使用英文文件名避免编码问题

**Q: 是否需要保持旧接口兼容？**
A: 建议在客户端代码中添加兼容性处理，便于平滑迁移

**Q: 多文件上传有数量限制吗？**
A: 建议控制在合理范围内（如10个文件以内），避免请求过大

**Q: 如何测试新格式？**
A: 可以先在测试环境验证新格式的上传和显示功能
