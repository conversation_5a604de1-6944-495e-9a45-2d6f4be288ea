# API 接口变更文档

## 概述

本文档描述了 `perf_ui_auto_case_run_detail` 表相关接口的变更内容，包括字段重命名、类型修改和字段删除。

**变更版本**: v1.1  
**变更日期**: 2025-07-30  
**影响范围**: 性能测试相关接口  

## 变更详情

### 1. 数据库表结构变更

**表名**: `perf_ui_auto_case_run_detail`

| 操作类型 | 原字段名 | 新字段名 | 原类型 | 新类型 | 说明 |
|---------|---------|---------|--------|--------|------|
| 重命名+类型变更 | `case_log_tos_url` | `case_log_tos_urls` | `VARCHAR` | `JSON` | 用例日志文件地址，改为JSON格式 |
| 重命名+类型变更 | `perf_data_tos_url` | `perf_data_tos_urls` | `VARCHAR` | `JSON` | 性能数据文件地址，改为JSON格式 |
| 删除 | `trace_data_tos_url` | - | `VARCHAR` | - | 删除trace数据文件地址字段 |
| 删除 | `byteio_data_tos_url` | - | `VARCHAR` | - | 删除byteio数据文件地址字段 |

### 2. JSON 数据格式说明

#### case_log_tos_urls 格式
```json
{
  "case.log": "https://example.com/path/to/case.log"
}
```
- 当原 `case_log_tos_url` 为空或null时，新字段值为 `null`
- 当原 `case_log_tos_url` 有值时，转换为上述JSON格式

#### perf_data_tos_urls 格式
```json
{
  "perf_data.json": "https://example.com/path/to/perf_data.json"
}
```
- 当原 `perf_data_tos_url` 为空或null时，新字段值为 `null`
- 当原 `perf_data_tos_url` 有值时，转换为上述JSON格式

## Thrift 接口变更

### 影响的结构体

#### UploadPerfCaseRunDetailParams

**文件路径**: `thrifts/perf_ui_auto/perf_task_struct.thrift`

**变更前**:
```thrift
struct UploadPerfCaseRunDetailParams {
    // ... 其他字段
    3: optional string case_log_tos_url,
    4: optional string perf_data_tos_url,
    5: optional string trace_data_tos_url,
    6: optional string byteio_data_tos_url,
    // ... 其他字段
}
```

**变更后**:
```thrift
struct UploadPerfCaseRunDetailParams {
    // ... 其他字段
    3: optional map<string, string> case_log_tos_urls,
    4: optional map<string, string> perf_data_tos_urls,
    // 删除了 trace_data_tos_url 和 byteio_data_tos_url
    // ... 其他字段
}
```

## API 接口变更

### 请求参数变更

所有涉及 `UploadPerfCaseRunDetailParams` 的API接口请求参数都会受到影响：

**变更前请求示例**:
```json
{
  "case_log_tos_url": "https://example.com/case.log",
  "perf_data_tos_url": "https://example.com/perf_data.json",
  "trace_data_tos_url": "https://example.com/trace.data",
  "byteio_data_tos_url": "https://example.com/byteio.data"
}
```

**变更后请求示例**:
```json
{
  "case_log_tos_urls": {
    "case.log": "https://example.com/case.log"
  },
  "perf_data_tos_urls": {
    "perf_data.json": "https://example.com/perf_data.json"
  }
}
```

### 响应数据变更

所有返回 `PerfCaseRunDetail` 相关数据的API响应都会受到影响：

**变更前响应示例**:
```json
{
  "id": 123,
  "case_log_tos_url": "https://example.com/case.log",
  "perf_data_tos_url": "https://example.com/perf_data.json",
  "trace_data_tos_url": "https://example.com/trace.data",
  "byteio_data_tos_url": "https://example.com/byteio.data"
}
```

**变更后响应示例**:
```json
{
  "id": 123,
  "case_log_tos_urls": {
    "case.log": "https://example.com/case.log"
  },
  "perf_data_tos_urls": {
    "perf_data.json": "https://example.com/perf_data.json"
  }
}
```

## 兼容性说明

### 向后兼容性
- ❌ **不兼容**: 此次变更为破坏性变更，不向后兼容
- 🔄 **必须适配**: 所有使用相关接口的客户端都必须进行适配

### 迁移建议

1. **客户端代码适配**:
   - 更新请求参数结构，使用新的JSON格式字段
   - 更新响应数据解析逻辑
   - 移除对已删除字段的引用

2. **数据处理逻辑**:
   - 如果需要获取单个文件URL，从JSON对象中提取对应的值
   - 处理JSON字段为null的情况

3. **测试验证**:
   - 验证新接口的请求和响应格式
   - 确保数据迁移后的功能正常

## 影响的API列表

以下API接口会受到此次变更的影响：

- `POST /api/perf_ui_auto/upload_case_run_detail` - 上传用例运行详情
- `GET /api/perf_ui_auto/case_run_detail/{id}` - 获取用例运行详情
- `GET /api/perf_ui_auto/case_run_details` - 批量获取用例运行详情
- 其他返回 `PerfCaseRunDetail` 数据的相关接口

## 客户端适配代码示例

### Python 客户端适配

**变更前代码**:
```python
# 请求参数构造
params = {
    "case_log_tos_url": "https://example.com/case.log",
    "perf_data_tos_url": "https://example.com/perf_data.json",
    "trace_data_tos_url": "https://example.com/trace.data",
    "byteio_data_tos_url": "https://example.com/byteio.data"
}

# 响应数据处理
case_log_url = response_data.get("case_log_tos_url")
perf_data_url = response_data.get("perf_data_tos_url")
```

**变更后代码**:
```python
# 请求参数构造
params = {
    "case_log_tos_urls": {
        "case.log": "https://example.com/case.log"
    } if case_log_url else None,
    "perf_data_tos_urls": {
        "perf_data.json": "https://example.com/perf_data.json"
    } if perf_data_url else None
}

# 响应数据处理
case_log_urls = response_data.get("case_log_tos_urls")
case_log_url = case_log_urls.get("case.log") if case_log_urls else None

perf_data_urls = response_data.get("perf_data_tos_urls")
perf_data_url = perf_data_urls.get("perf_data.json") if perf_data_urls else None
```

### Java 客户端适配

**变更前代码**:
```java
// 请求参数构造
UploadPerfCaseRunDetailParams params = new UploadPerfCaseRunDetailParams();
params.setCaseLogTosUrl("https://example.com/case.log");
params.setPerfDataTosUrl("https://example.com/perf_data.json");

// 响应数据处理
String caseLogUrl = response.getCaseLogTosUrl();
String perfDataUrl = response.getPerfDataTosUrl();
```

**变更后代码**:
```java
// 请求参数构造
UploadPerfCaseRunDetailParams params = new UploadPerfCaseRunDetailParams();
Map<String, String> caseLogUrls = new HashMap<>();
caseLogUrls.put("case.log", "https://example.com/case.log");
params.setCaseLogTosUrls(caseLogUrls);

Map<String, String> perfDataUrls = new HashMap<>();
perfDataUrls.put("perf_data.json", "https://example.com/perf_data.json");
params.setPerfDataTosUrls(perfDataUrls);

// 响应数据处理
Map<String, String> caseLogUrls = response.getCaseLogTosUrls();
String caseLogUrl = caseLogUrls != null ? caseLogUrls.get("case.log") : null;

Map<String, String> perfDataUrls = response.getPerfDataTosUrls();
String perfDataUrl = perfDataUrls != null ? perfDataUrls.get("perf_data.json") : null;
```

### JavaScript 客户端适配

**变更前代码**:
```javascript
// 请求参数构造
const params = {
  case_log_tos_url: "https://example.com/case.log",
  perf_data_tos_url: "https://example.com/perf_data.json",
  trace_data_tos_url: "https://example.com/trace.data",
  byteio_data_tos_url: "https://example.com/byteio.data"
};

// 响应数据处理
const caseLogUrl = responseData.case_log_tos_url;
const perfDataUrl = responseData.perf_data_tos_url;
```

**变更后代码**:
```javascript
// 请求参数构造
const params = {
  case_log_tos_urls: caseLogUrl ? { "case.log": caseLogUrl } : null,
  perf_data_tos_urls: perfDataUrl ? { "perf_data.json": perfDataUrl } : null
};

// 响应数据处理
const caseLogUrl = responseData.case_log_tos_urls?.["case.log"] || null;
const perfDataUrl = responseData.perf_data_tos_urls?.["perf_data.json"] || null;
```

## 数据迁移时间计划

### 迁移步骤
1. **代码部署**: 2025-07-30 (预计)
2. **数据库迁移**: 2025-07-30 晚间维护窗口
3. **客户端适配截止**: 2025-08-05
4. **旧接口下线**: 2025-08-10

### 重要时间节点
- **2025-07-30**: 新接口上线，旧接口仍可用
- **2025-08-05**: 客户端必须完成适配
- **2025-08-10**: 旧接口正式下线

## 常见问题 FAQ

### Q1: 如何处理 null 值？
A: 当原字段为空时，新的JSON字段值为 `null`，客户端需要进行null值检查。

### Q2: 是否支持多个文件URL？
A: 目前JSON格式中只包含一个文件URL，但为未来扩展预留了空间。

### Q3: 旧接口何时完全不可用？
A: 旧接口将在 2025-08-10 正式下线，请务必在此之前完成适配。

## 联系方式

如有疑问或需要技术支持，请联系：
- 开发团队：[开发团队联系方式]
- 技术文档：[文档链接]

---

**注意**: 请在升级前仔细阅读本文档，并在测试环境中充分验证适配效果。
