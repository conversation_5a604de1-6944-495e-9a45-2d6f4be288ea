# 🚨 重要接口变更通知

## 📋 变更概述

**影响接口**: 性能测试相关API  
**变更时间**: 2025-07-30  
**影响级别**: 🔴 破坏性变更（必须适配）

## 🔄 主要变更

### 字段变更
| 原字段 | 新字段 | 变更类型 |
|-------|-------|---------|
| `case_log_tos_url` | `case_log_tos_urls` | 字符串 → JSON |
| `perf_data_tos_url` | `perf_data_tos_urls` | 字符串 → JSON |
| `trace_data_tos_url` | ❌ 删除 | 完全移除 |
| `byteio_data_tos_url` | ❌ 删除 | 完全移除 |

### 数据格式变更

**变更前**:
```json
{
  "case_log_tos_url": "https://example.com/case.log",
  "perf_data_tos_url": "https://example.com/perf_data.json"
}
```

**变更后**:
```json
{
  "case_log_tos_urls": {
    "case.log": "https://example.com/case.log"
  },
  "perf_data_tos_urls": {
    "perf_data.json": "https://example.com/perf_data.json"
  }
}
```

## ⚠️ 重要提醒

1. **不向后兼容**: 此次变更为破坏性变更
2. **必须适配**: 所有客户端都需要更新代码
3. **适配截止**: 2025-08-05
4. **旧接口下线**: 2025-08-10

## 🛠️ 快速适配指南

### Python 示例
```python
# 旧代码
case_log_url = data.get("case_log_tos_url")

# 新代码  
case_log_urls = data.get("case_log_tos_urls")
case_log_url = case_log_urls.get("case.log") if case_log_urls else None
```

### JavaScript 示例
```javascript
// 旧代码
const caseLogUrl = data.case_log_tos_url;

// 新代码
const caseLogUrl = data.case_log_tos_urls?.["case.log"] || null;
```

### Java 示例
```java
// 旧代码
String caseLogUrl = response.getCaseLogTosUrl();

// 新代码
Map<String, String> caseLogUrls = response.getCaseLogTosUrls();
String caseLogUrl = caseLogUrls != null ? caseLogUrls.get("case.log") : null;
```

## 📅 时间计划

- **2025-07-30**: 新接口上线
- **2025-08-05**: 客户端适配截止
- **2025-08-10**: 旧接口下线

## 📞 技术支持

如需帮助，请联系开发团队。

---

**详细文档**: 请查看 `docs/api_interface_changes_v1.1.md` 获取完整的变更文档和适配指南。
