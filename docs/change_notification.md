# 🚨 重要接口变更通知

## 📋 变更概述

**影响接口**: 性能测试相关API  
**变更时间**: 2025-07-30  
**影响级别**: 🔴 破坏性变更（必须适配）  
**影响范围**: 前端、客户端

## 🔄 主要变更

### 数据库表变更
**表名**: `perf_ui_auto_case_run_detail`

| 原字段 | 新字段 | 变更类型 | 数据格式 |
|-------|-------|---------|----------|
| `case_log_tos_url` | `case_log_tos_urls` | String → JSON | `{"case.log": "url"}` |
| `perf_data_tos_url` | `perf_data_tos_urls` | String → JSON | `{"perf_data.json": "url"}` |
| `trace_data_tos_url` | ❌ 删除 | - | - |
| `byteio_data_tos_url` | ❌ 删除 | - | - |

### API接口变更
1. **获取性能数据接口**: `/api/perf_ui_auto/data/*`
   - 响应字段格式变更

2. **上传性能测试结果接口**: `/api/perf_ui_auto/upload_case_run_detail`
   - 请求参数格式变更

## 📊 数据格式对比

### 旧格式
```json
{
  "case_log_tos_url": "https://tos.example.com/case.log",
  "perf_data_tos_url": "https://tos.example.com/perf_data.json",
  "trace_data_tos_url": "https://tos.example.com/trace.data",
  "byteio_data_tos_url": "https://tos.example.com/byteio.data"
}
```

### 新格式
```json
{
  "case_log_tos_urls": {
    "case.log": "https://tos.example.com/case.log",
    "error.log": "https://tos.example.com/error.log"
  },
  "perf_data_tos_urls": {
    "perf_data.json": "https://tos.example.com/perf_data.json",
    "metrics.json": "https://tos.example.com/metrics.json"
  }
}
```

## 🎯 变更优势

1. **多文件支持**: 新格式天然支持多个文件的上传和显示
2. **更好的扩展性**: JSON格式便于后续添加文件元信息
3. **语义化**: 文件名作为key，更加直观
4. **简化架构**: 删除不再使用的trace和byteio字段

## 📅 迁移时间表

| 阶段 | 时间 | 内容 |
|-----|------|------|
| 准备阶段 | 2025-07-30 | 发布变更通知，提供适配指南 |
| 代码适配 | 待定 | 前端和客户端代码适配 |
| 数据库迁移 | 待定 | 执行数据库结构变更 |
| 部署上线 | 待定 | 新版本代码部署 |
| 验证阶段 | 待定 | 功能验证和监控 |

## 📖 适配指南

### 🎨 前端适配
详细指南请查看: [前端适配指南](./frontend_change_guide.md)

**核心要点**:
- 更新显示逻辑，支持多文件链接显示
- 修改表格列配置
- 添加兼容性处理
- 移除已删除字段的相关代码

### 🔧 客户端适配  
详细指南请查看: [客户端适配指南](./client_change_guide.md)

**核心要点**:
- 更新上传接口参数格式
- 支持多文件上传
- 适配TOS上传逻辑
- 添加向后兼容处理

## 🛠️ 数据库迁移

### 迁移脚本
```sql
-- 执行脚本位置
scripts/migrate_perf_ui_auto_case_run_detail.sql
```

### 迁移步骤
1. **备份数据库** (必须)
2. **执行迁移脚本**
3. **验证数据完整性**
4. **部署新版本代码**

### 迁移注意事项
- 在业务低峰期执行
- 迁移过程中可能出现短暂的数据不一致
- 准备回滚方案

## ⚠️ 风险评估

### 高风险项
- 数据库迁移失败
- 前端显示异常
- 客户端上传失败

### 风险缓解措施
- 充分的测试验证
- 分阶段部署
- 实时监控告警
- 快速回滚机制

## 📞 联系方式

### 技术支持
- **后端开发**: [后端负责人]
- **前端开发**: [前端负责人]  
- **测试**: [测试负责人]
- **运维**: [运维负责人]

### 紧急联系
- **值班电话**: [值班电话]
- **群聊**: [技术支持群]

## 📋 检查清单

### 前端团队
- [ ] 阅读前端适配指南
- [ ] 更新相关组件和页面
- [ ] 进行功能测试
- [ ] 准备发布计划

### 客户端团队  
- [ ] 阅读客户端适配指南
- [ ] 更新上传逻辑
- [ ] 进行集成测试
- [ ] 更新相关文档

### 测试团队
- [ ] 制定测试计划
- [ ] 准备测试数据
- [ ] 执行功能测试
- [ ] 验证兼容性

### 运维团队
- [ ] 准备数据库迁移
- [ ] 配置监控告警
- [ ] 准备回滚方案
- [ ] 制定发布计划

## 🔍 FAQ

**Q: 这次变更是否向后兼容？**
A: 这是破坏性变更，但我们提供了兼容性处理方案

**Q: 数据库迁移会丢失数据吗？**
A: 不会，迁移脚本会将现有数据转换为新格式

**Q: 如果迁移出现问题怎么办？**
A: 我们准备了完整的回滚方案，可以快速恢复

**Q: 新格式支持多少个文件？**
A: 理论上没有限制，但建议控制在合理范围内

**Q: 什么时候必须完成适配？**
A: 在数据库迁移执行前必须完成代码适配

---

**重要提醒**: 请各团队务必在规定时间内完成适配工作，确保系统平稳过渡！
