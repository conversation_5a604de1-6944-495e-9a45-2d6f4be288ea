# 🎨 前端适配指南

## 📋 变更概述

**影响接口**: 性能测试相关API  
**变更时间**: 2025-07-30  
**影响级别**: 🔴 破坏性变更（必须适配）

## 🔄 主要变更

### 字段变更
| 原字段 | 新字段 | 变更类型 |
|-------|-------|---------|
| `case_log_tos_url` | `case_log_tos_urls` | 字符串 → JSON |
| `perf_data_tos_url` | `perf_data_tos_urls` | 字符串 → JSON |
| `trace_data_tos_url` | ❌ 删除 | - |
| `byteio_data_tos_url` | ❌ 删除 | - |

### 数据格式变更
```json
// 旧格式
{
  "case_log_tos_url": "https://example.com/case.log",
  "perf_data_tos_url": "https://example.com/perf_data.json",
  "trace_data_tos_url": "https://example.com/trace.data",
  "byteio_data_tos_url": "https://example.com/byteio.data"
}

// 新格式
{
  "case_log_tos_urls": {
    "case.log": "https://example.com/case.log"
  },
  "perf_data_tos_urls": {
    "perf_data.json": "https://example.com/perf_data.json"
  }
  // trace_data_tos_url 和 byteio_data_tos_url 字段已删除
}
```

## 🎯 影响的API接口

1. **获取性能数据接口**
   - `/api/perf_ui_auto/data/*`
   - 响应字段格式变更

2. **上传性能测试结果接口**
   - `/api/perf_ui_auto/upload_case_run_detail`
   - 请求参数格式变更

## 💻 前端代码适配

### 1. 显示逻辑适配

```javascript
// ❌ 旧代码
function displayLogUrl(data) {
  if (data.case_log_tos_url) {
    return `<a href="${data.case_log_tos_url}">查看日志</a>`;
  }
  return '无日志';
}

// ✅ 新代码 - 适配JSON格式
function displayLogUrls(data) {
  if (data.case_log_tos_urls) {
    const urls = Object.entries(data.case_log_tos_urls);
    return urls.map(([filename, url]) => 
      `<a href="${url}" target="_blank">${filename}</a>`
    ).join(' | ');
  }
  return '无日志';
}

// 性能数据显示
function displayPerfDataUrls(data) {
  if (data.perf_data_tos_urls) {
    const urls = Object.entries(data.perf_data_tos_urls);
    return urls.map(([filename, url]) => 
      `<a href="${url}" target="_blank">${filename}</a>`
    ).join(' | ');
  }
  return '无性能数据';
}
```

### 2. React组件适配

```jsx
// ❌ 旧组件
const LogUrlDisplay = ({ data }) => {
  return (
    <div>
      {data.case_log_tos_url ? (
        <a href={data.case_log_tos_url}>查看日志</a>
      ) : (
        '无日志'
      )}
    </div>
  );
};

// ✅ 新组件
const LogUrlsDisplay = ({ data }) => {
  if (!data.case_log_tos_urls) {
    return <span>无日志</span>;
  }

  return (
    <div>
      {Object.entries(data.case_log_tos_urls).map(([filename, url]) => (
        <a 
          key={filename} 
          href={url} 
          target="_blank" 
          style={{ marginRight: 8 }}
        >
          {filename}
        </a>
      ))}
    </div>
  );
};

const PerfDataUrlsDisplay = ({ data }) => {
  if (!data.perf_data_tos_urls) {
    return <span>无性能数据</span>;
  }

  return (
    <div>
      {Object.entries(data.perf_data_tos_urls).map(([filename, url]) => (
        <a 
          key={filename} 
          href={url} 
          target="_blank" 
          style={{ marginRight: 8 }}
        >
          {filename}
        </a>
      ))}
    </div>
  );
};
```

### 3. 表格列配置适配

```javascript
// ❌ 旧配置
const columns = [
  {
    title: '用例日志',
    dataIndex: 'case_log_tos_url',
    render: (url) => url ? <a href={url}>查看日志</a> : '无'
  },
  {
    title: '性能数据',
    dataIndex: 'perf_data_tos_url', 
    render: (url) => url ? <a href={url}>查看数据</a> : '无'
  },
  {
    title: 'Trace数据',
    dataIndex: 'trace_data_tos_url',
    render: (url) => url ? <a href={url}>查看Trace</a> : '无'
  }
];

// ✅ 新配置
const columns = [
  {
    title: '用例日志',
    dataIndex: 'case_log_tos_urls',
    render: (urls) => {
      if (!urls) return '无';
      return Object.entries(urls).map(([filename, url]) => (
        <a key={filename} href={url} target="_blank" style={{marginRight: 8}}>
          {filename}
        </a>
      ));
    }
  },
  {
    title: '性能数据',
    dataIndex: 'perf_data_tos_urls',
    render: (urls) => {
      if (!urls) return '无';
      return Object.entries(urls).map(([filename, url]) => (
        <a key={filename} href={url} target="_blank" style={{marginRight: 8}}>
          {filename}
        </a>
      ));
    }
  }
  // trace_data_tos_url 列已删除
];
```

### 4. 兼容性处理

```javascript
// 兼容新旧格式的工具函数
function getLogUrls(data) {
  // 新格式
  if (data.case_log_tos_urls) {
    return data.case_log_tos_urls;
  }
  // 兼容旧格式
  if (data.case_log_tos_url) {
    return { "case.log": data.case_log_tos_url };
  }
  return null;
}

function getPerfDataUrls(data) {
  // 新格式
  if (data.perf_data_tos_urls) {
    return data.perf_data_tos_urls;
  }
  // 兼容旧格式
  if (data.perf_data_tos_url) {
    return { "perf_data.json": data.perf_data_tos_url };
  }
  return null;
}

// 使用示例
const LogUrlsCompatible = ({ data }) => {
  const urls = getLogUrls(data);
  
  if (!urls) return <span>无日志</span>;
  
  return (
    <div>
      {Object.entries(urls).map(([filename, url]) => (
        <a key={filename} href={url} target="_blank" style={{marginRight: 8}}>
          {filename}
        </a>
      ))}
    </div>
  );
};
```

## 📝 迁移检查清单

### 必须修改的地方
- [ ] 更新所有显示 `case_log_tos_url` 的组件
- [ ] 更新所有显示 `perf_data_tos_url` 的组件  
- [ ] 移除 `trace_data_tos_url` 和 `byteio_data_tos_url` 相关显示
- [ ] 更新表格列配置
- [ ] 更新详情页面显示逻辑
- [ ] 更新搜索和筛选逻辑（如果有）

### 建议优化的地方
- [ ] 添加多文件链接显示支持
- [ ] 优化文件名显示（去掉扩展名等）
- [ ] 添加文件大小显示（如果API提供）
- [ ] 添加下载按钮或右键菜单

### 测试检查清单
- [ ] 测试新数据格式显示正常
- [ ] 测试旧数据格式兼容性（如果需要）
- [ ] 测试空值情况处理
- [ ] 测试多文件情况显示
- [ ] 测试链接可正常打开

## ⚠️ 注意事项

1. **链接打开方式**: 建议使用 `target="_blank"` 在新窗口打开文件链接
2. **文件名显示**: JSON的key就是文件名，可以直接显示
3. **空值处理**: 新格式中空值为 `null`，不是空字符串
4. **多文件支持**: 新格式天然支持多文件，前端需要适配显示多个链接
5. **样式调整**: 多个链接需要适当的间距和样式

## 🆘 常见问题

**Q: 如何处理多个文件的显示？**
A: 使用 `Object.entries()` 遍历JSON对象，每个文件显示为一个链接

**Q: 需要兼容旧格式吗？**
A: 建议添加兼容性处理，以防数据库迁移期间出现混合格式

**Q: 删除的字段如何处理？**
A: 直接移除相关显示代码，不需要特殊处理

**Q: 如何测试新格式？**
A: 可以先用mock数据测试，确保显示逻辑正确后再对接真实API
