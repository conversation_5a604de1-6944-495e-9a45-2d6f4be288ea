# Thrift 接口变更文档

## 变更概述

**文件**: `thrifts/perf_ui_auto/perf_task_struct.thrift`  
**结构体**: `UploadPerfCaseRunDetailParams`  
**变更时间**: 2025-01-30  

## 变更详情

### 变更前
```thrift
struct UploadPerfCaseRunDetailParams {
    1: optional i64 id,
    2: optional string case_log_tos_url,
    3: optional string perf_data_tos_url,
    4: optional string trace_data_tos_url,
    5: optional string byteio_data_tos_url,
    // ... 其他字段
}
```

### 变更后
```thrift
struct UploadPerfCaseRunDetailParams {
    1: optional i64 id,
    2: optional map<string, string> case_log_tos_urls,
    3: optional map<string, string> perf_data_tos_urls,
    // trace_data_tos_url 和 byteio_data_tos_url 字段已删除
    // ... 其他字段
}
```

## 字段映射

| 原字段 | 新字段 | Thrift类型变更 | 数据格式 |
|--------|--------|----------------|----------|
| `case_log_tos_url` | `case_log_tos_urls` | `string` → `map<string, string>` | `{"case.log": "url"}` |
| `perf_data_tos_url` | `perf_data_tos_urls` | `string` → `map<string, string>` | `{"perf_data.json": "url"}` |
| `trace_data_tos_url` | ❌ 删除 | - | - |
| `byteio_data_tos_url` | ❌ 删除 | - | - |

## 客户端代码生成

### 重新生成客户端代码

各语言客户端需要重新生成Thrift客户端代码：

```bash
# Python
thrift --gen py thrifts/perf_ui_auto/perf_task_struct.thrift

# Java
thrift --gen java thrifts/perf_ui_auto/perf_task_struct.thrift

# Go
thrift --gen go thrifts/perf_ui_auto/perf_task_struct.thrift

# C++
thrift --gen cpp thrifts/perf_ui_auto/perf_task_struct.thrift
```

## 客户端适配示例

### Python 客户端
```python
# 变更前
params = UploadPerfCaseRunDetailParams()
params.case_log_tos_url = "https://tos.example.com/logs/case.log"
params.perf_data_tos_url = "https://tos.example.com/data/perf.json"

# 变更后
params = UploadPerfCaseRunDetailParams()
params.case_log_tos_urls = {"case.log": "https://tos.example.com/logs/case.log"}
params.perf_data_tos_urls = {"perf_data.json": "https://tos.example.com/data/perf.json"}
```

### Java 客户端
```java
// 变更前
UploadPerfCaseRunDetailParams params = new UploadPerfCaseRunDetailParams();
params.setCaseLogTosUrl("https://tos.example.com/logs/case.log");
params.setPerfDataTosUrl("https://tos.example.com/data/perf.json");

// 变更后
UploadPerfCaseRunDetailParams params = new UploadPerfCaseRunDetailParams();
Map<String, String> caseLogUrls = new HashMap<>();
caseLogUrls.put("case.log", "https://tos.example.com/logs/case.log");
params.setCaseLogTosUrls(caseLogUrls);

Map<String, String> perfDataUrls = new HashMap<>();
perfDataUrls.put("perf_data.json", "https://tos.example.com/data/perf.json");
params.setPerfDataTosUrls(perfDataUrls);
```

### Go 客户端
```go
// 变更前
params := &UploadPerfCaseRunDetailParams{
    CaseLogTosUrl:  StringPtr("https://tos.example.com/logs/case.log"),
    PerfDataTosUrl: StringPtr("https://tos.example.com/data/perf.json"),
}

// 变更后
params := &UploadPerfCaseRunDetailParams{
    CaseLogTosUrls: map[string]string{
        "case.log": "https://tos.example.com/logs/case.log",
    },
    PerfDataTosUrls: map[string]string{
        "perf_data.json": "https://tos.example.com/data/perf.json",
    },
}
```

## 注意事项

1. **重新编译**: 所有使用此Thrift接口的客户端都需要重新编译
2. **版本兼容**: 新旧版本不兼容，需要同步更新
3. **字段删除**: `trace_data_tos_url` 和 `byteio_data_tos_url` 字段已完全删除
4. **空值处理**: 新的map字段可能为空，需要做好空值检查

## 测试建议

1. **单元测试**: 更新相关的单元测试用例
2. **集成测试**: 验证Thrift接口调用正常
3. **兼容性测试**: 确保新旧版本切换顺利

---

**重要提醒**: 请确保所有相关团队同步更新Thrift客户端代码！
