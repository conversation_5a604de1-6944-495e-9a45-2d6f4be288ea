import json
import traceback
import re

from dals.db.libra_experiment.libra_experiment_review_db import *
from defines.lark_robot_define import LARK_EMAIL_SUFFIX
from defines.libra_experiment_define import *
from handlers.libra_experiment import libra_experiment_review_handler
from schemas.inner_model.libra_experiment_review_inner_model import LibraExperimentFieldAndRule
from schemas.request.libra_experiment.libra_experiment_review_req import *
from schemas.response.libra_experiment.libra_experiment_review_res import *
from utils.common.response import *
# from utils.common.log import custom_logger
from proj_settings import settings
from utils.openapi.lark_robot_api import larkRobot
from proj_settings import deploy_cluster_env, DeployCluster
from utils.openapi.meego_api_new import meego


# async def add_experiment(params: AddLibraExperimentParams):
#     item = libra_experiment_review_db.insert_experiment(params)
#     if not item:
#         return item,'insert fail'
#     data = libra_experiment_review_handler.handle_insert_experiment_data(item=item)
#     # 触发异步方法获取meego测试报告信息、检查实验字段
#     await handle_experiment_check(data)
#     return data, 'success'


def handle_experiment_check(data:LibraExperiment):
    try:
        byte_log.info(f"handle_experiment_check start")
        # 获取meego测试报告信息
        experiment_meego_info = json.loads(data.experiment_meego_info.replace("'", "\"")) if data.experiment_meego_info else data.experiment_meego_info
        meego_test_report = get_meego_test_report(experiment_meego_info, data.id, data.business_id)
        byte_log.info(f"handle_experiment_check experiment_meego_info={experiment_meego_info} meego_test_report={meego_test_report}")

        # 检查实验字段
        check_experiment_by_rule(LibraExperimentAndTestReport(**(data.to_json()),
                                                              test_report_url=meego_test_report.test_report_url if meego_test_report else None,
                                                              test_report_is_pass=meego_test_report.test_report_is_pass if meego_test_report else None,
                                                              test_report_tester=meego_test_report.test_report_tester if meego_test_report else None))
        byte_log.info("handle_experiment_check done")
    except Exception as e:
        byte_log.exception(e)
        byte_log.error(f"handle_experiment_check error,{e}")

def get_meego_test_report(experiment_meego_info, libra_experiment_id, business_id):
    meego_test_report = None
    meego_test_report_json = {}
    if "meego_array" in experiment_meego_info and len(experiment_meego_info["meego_array"])>0 and "meego_project" in experiment_meego_info["meego_array"][0] and "meego_story" in experiment_meego_info["meego_array"][0]:
        project_key = experiment_meego_info["meego_array"][0]["meego_project"]
        work_item_id = experiment_meego_info["meego_array"][0]["meego_story"]
        # 获取meego节点详情
        response_workflow = meego.get_feature_workflow_detail(project_key, work_item_id)
        if response_workflow and "workflow_nodes" in response_workflow:
            # 根据业务id取不同meego节点信息
            meego_info_map = MEEGO_INFO_MAP[business_id] if business_id in MEEGO_INFO_MAP else None
            if not meego_info_map:
                return meego_test_report
            for node_item in response_workflow["workflow_nodes"]:
                # 测试节点信息
                if "name" in node_item and node_item["name"] == meego_info_map["MEEGO_TEST_NODE_NAME"]:
                    # 组装所需字段
                    meego_test_report_json["uniq_libra_experiment_id"] = libra_experiment_id
                    if "status" in node_item and node_item["status"] == meego_info_map["MEEGO_NODE_FINISH_STATUS"]:
                        meego_test_report_json["test_node_status_is_finish"] = True
                        if "node_schedule" in node_item and "owners" in node_item["node_schedule"] and len(node_item["node_schedule"]["owners"])>0:
                            tester_list = []
                            for tester in node_item["node_schedule"]["owners"]:
                                if meego.is_user_key(tester):
                                    response_user_details = meego.get_user_details_by_user_keys(user_key_list=[tester])
                                    if response_user_details and len(response_user_details)>0 and "email" in response_user_details[0]:
                                        tester_list.append(response_user_details[0]["email"][:response_user_details[0]["email"].index('@')])
                                else:
                                    tester_list.append(tester)
                            meego_test_report_json["test_report_tester"] = json.dumps(tester_list)
                        for field in node_item["fields"]:
                            if field["field_key"] == meego_info_map["MEEGO_TEST_REPORT_URL_FILED_KEY"]:
                                meego_test_report_json["test_report_url"] = field["field_value"]
                            elif field["field_key"] == meego_info_map["MEEGO_TEST_IS_PASS_FILED_KEY"]:
                                meego_test_report_json["test_report_is_pass"] = True if field["field_value"]["label"] == meego_info_map["MEEGO_TEST_IS_PASS_FILED_VALUE"] else False
                    # 插入数据库
                    try:
                        meego_test_report = libra_experiment_review_db.insert_experiment_test_report(meego_test_report_json)
                    except Exception as e:
                        byte_log.error("get_meego_test_report libra_experiment_review_db.insert_experiment_test_report fail")
                    break
    return meego_test_report

# 根据数据类型将期望值str转为指定数据类型
def get_value_by_type(value_str, value_type, operator_type, check_way):
    if operator_type in [OPERATOR_TYPE_SET_SUBSET, OPERATOR_TYPE_SET_EQUAL, OPERATOR_TYPE_SET_PARENT_SET]:
        return json.loads(value_str) if value_str else None
    elif operator_type == OPERATOR_TYPE_IGNORE_CASE:
        return int(value_str) if value_str else None
    elif operator_type == OPERATOR_TYPE_REVIEWER_HAS_X_RD:
        return int(value_str) if value_str else None
    elif operator_type == OPERATOR_TYPE_IS_NONE:
        return int(value_str) if value_str else None
    elif check_way == CHECK_NUM:
        return int(value_str) if value_str else None
    elif value_type == FIELD_TYPE_INT:
        return int(value_str) if value_str else None
    elif value_type == FIELD_TYPE_STRING:
        return value_str
    elif value_type == FIELD_TYPE_FLOAT:
        return float(value_str) if value_str else None
    elif value_type == FIELD_TYPE_LIST or value_type == FIELD_TYPE_MAP:
        return json.loads(value_str) if value_str else None
    elif value_type == FIELD_TYPE_BOOL:
        return bool(value_str) if value_str else None
    else:
        return None

def combine_check_msg(check_result_msg, is_must, is_pass):
    check_type = CHECK_TYPE_STR_MUST if is_must else CHECK_TYPE_STR_NOT_MUST
    check_pass = CHECK_RESULT_STR_SKIP if is_pass is None else CHECK_RESULT_STR_PASS if is_pass is True else CHECK_RESULT_STR_FAIL
    return f"{check_pass}{check_type}{check_result_msg}"
def add_result_to_check_msg(check_result_msg, is_pass):
    if is_pass:
        check_result_msg = CHECK_RESULT_STR_PASS + check_result_msg
    else:
        check_result_msg = CHECK_RESULT_STR_FAIL + check_result_msg
    return check_result_msg

# TODO：现有检查规则可以优化简化

def has_at_least_1_item_in_list(actual, expect_list: list):
    "实际值是否包含期望列表中的至少一个元素"
    if not actual or not expect_list:
        return False
    
    if isinstance(actual, dict):
        return actual in expect_list
    
    if isinstance(actual, list):
        return any(item in actual for item in expect_list)
    
    return False
        

def check_value(expect_field_value, actual_field_value, rule, experiment_qa_reviewer):
    check_result = None
    check_result_map = {}
    rule_str = ''
    if rule.is_check_value == CHECK_VALUE: # 检查值
        if rule.operator_type == OPERATOR_TYPE_EQUAL:  # 等于
            rule_str = "值等于 *%s* " % expect_field_value
            check_result = (actual_field_value == expect_field_value)
        elif rule.operator_type == OPERATOR_TYPE_GT:  # 大于
            rule_str = "值大于 *%s* " % expect_field_value
            check_result = (actual_field_value > expect_field_value)
        elif rule.operator_type == OPERATOR_TYPE_GE:  # 大于等于
            rule_str = "值大于等于 *%s* " % expect_field_value
            check_result = (actual_field_value >= expect_field_value)
        elif rule.operator_type == OPERATOR_TYPE_LT:  # 小于
            rule_str = "值小于 *%s* " % expect_field_value
            check_result = (actual_field_value < expect_field_value)
        elif rule.operator_type == OPERATOR_TYPE_LQ:  # 小于等于
            rule_str = "值小于等于 *%s* " % expect_field_value
            check_result = (actual_field_value <= expect_field_value)
        elif rule.operator_type == OPERATOR_TYPE_REGEX and rule.field_type == FIELD_TYPE_STRING:
            rule_str = "符合正则表达式 \"%s\" " % expect_field_value
            check_result = bool(re.match(expect_field_value, actual_field_value))
        elif rule.operator_type == OPERATOR_TYPE_SET_SUBSET:  # 是某个列表的子集
            if rule.field_type != FIELD_TYPE_LIST:
                actual_field_value = [actual_field_value]
            rule_str = "是列表 *%s* 的子集" % expect_field_value
            check_result = (set(actual_field_value).issubset(set(expect_field_value)))
        elif rule.operator_type == OPERATOR_TYPE_SET_EQUAL and rule.field_type == FIELD_TYPE_LIST:  # 与某个列表完全匹配
            rule_str = "与列表 *%s* 相同" % expect_field_value
            check_result = (set(actual_field_value).issubset(set(expect_field_value)) and set(expect_field_value).issubset(set(actual_field_value)))
        elif rule.operator_type == OPERATOR_TYPE_SET_PARENT_SET and rule.field_type == FIELD_TYPE_LIST:  # 是某个列表的父集
            rule_str = "包含 *%s* " % ('、'.join([str(item) for item in expect_field_value]))
            check_result = (set(expect_field_value).issubset(set(actual_field_value)))
        elif rule.operator_type == OPERATOR_TYPE_IS_NONE:  # 是否为空
            rule_str = " *%s* " % ("为空" if expect_field_value == IS_NONE else "非空")
            check_result = (expect_field_value == IS_NONE and actual_field_value is None) or (expect_field_value == IS_NOT_NONE and actual_field_value is not None)
        elif rule.operator_type == OPERATOR_TYPE_SET_HAS_ONE_AT_LEAST:  # 至少包含其一
            rule_str = " 至少包含其一 *%s* " % expect_field_value
            check_result = has_at_least_1_item_in_list(actual_field_value, expect_field_value)
    elif rule.is_check_value == CHECK_NUM and rule.field_type == FIELD_TYPE_LIST:  # 检查值个数
        expect_field_value_len = expect_field_value
        actual_field_value_len = len(actual_field_value)
        if rule.operator_type == OPERATOR_TYPE_EQUAL:
            rule_str = "值个数等于 *%s* " % expect_field_value_len
            check_result = (actual_field_value_len == expect_field_value_len)
        elif rule.operator_type == OPERATOR_TYPE_GT:
            rule_str = "值个数大于 *%s* " % expect_field_value_len
            check_result = (actual_field_value_len > expect_field_value_len)
        elif rule.operator_type == OPERATOR_TYPE_GE:
            rule_str = "值个数大于等于 *%s* " % expect_field_value_len
            check_result = (actual_field_value_len >= expect_field_value_len)
        elif rule.operator_type == OPERATOR_TYPE_LT:
            rule_str = "值个数小于 *%s* " % expect_field_value_len
            check_result = (actual_field_value_len < expect_field_value_len)
        elif rule.operator_type == OPERATOR_TYPE_LQ:
            rule_str = "值个数小于等于 *%s* " % expect_field_value_len
            check_result = (actual_field_value_len <= expect_field_value_len)
        actual_field_value = actual_field_value_len
    elif rule.is_check_value == CHECK_RULE:  # 检查特定的规则
        if rule.operator_type == OPERATOR_TYPE_IGNORE_CASE:  # 忽略大小写
            actual_ignore_case = actual_field_value[0]["transformer"] == IGNORE_CASE_LOWER if len(actual_field_value)>0 and "transformer" in actual_field_value[0] else None
            rule_str = " *%s* " % ("忽略大小写（值非空时检查）" if expect_field_value == IS_IGNORE_CASE else "不忽略大小写（值非空时检查）")
            check_result = ((expect_field_value == IS_IGNORE_CASE and actual_ignore_case is True) or (expect_field_value == IS_NOT_IGNORE_CASE and actual_ignore_case is False) or (len(actual_field_value) == 0))
            actual_field_value = "空" if len(actual_field_value) == 0 else "忽略大小写" if actual_ignore_case else "未忽略大小写"
        elif rule.operator_type == OPERATOR_TYPE_REVIEWER_HAS_X_RD:  # 评审人里至少有n个是RD
            actual_rd_review_num = len(actual_field_value)-len(experiment_qa_reviewer)
            rule_str = "至少有 *%s* 个评审人是RD" % expect_field_value
            check_result = (actual_rd_review_num >= expect_field_value)
        elif rule.operator_type == OPERATOR_TYPE_EXPERIMENT_GROUP_KEY_IN_LIST:  # 实验组的key要在特定取值内
            check_result = True
            actual_field_value_key_set = set()
            for item in actual_field_value:
                if item["type"] == EXPERIMENT_TYPE_EXPERIMENTAL_GROUP:
                    actual_field_value_key_list = list(item["config"].keys())
                    actual_field_value_key_set.update(actual_field_value_key_list)
                    check_result = check_result and (set(actual_field_value_key_list).issubset(set(expect_field_value)))
                    # if not check_result:
                    #     break
            actual_field_value = list(actual_field_value_key_set)
            rule_str = "实验组的key要在特定取值内 *%s*" % (expect_field_value if len(str(expect_field_value)) <= VALUE_LENGTH_LIMIT else '（太长不展示）')
    check_result_map["value"] = actual_field_value
    # check_result_map["checkWay"] = rule.is_check_value
    check_result_map["ruleStr"] = rule_str
    check_result_map["result"] = check_result
    check_result_map["msg"] = combine_check_msg("字段 *%s* ，期望%s，实际为: *%s* " % (rule.field_name, rule_str, (actual_field_value if (len(str(actual_field_value)) <= VALUE_LENGTH_LIMIT) else '（太长不展示）')), rule.is_must, check_result)
    return check_result_map

# 由于某些原因跳过检查
def skip_check(data:LibraExperimentAndTestReport,rule:LibraExperimentFieldAndRule):
    # 服务端实验不检查预期版本
    if data.experiment_type == EXPERIMENT_TYPE_SERVICE and rule.field_key == FIELD_TYPE_VERSION_CODE:
        return True
    return False

# 检查实验字段
def check_experiment_by_rule(data:LibraExperimentAndTestReport):
    byte_log.info(f"check_experiment_by_rule start")
    rule_tuple_list = libra_experiment_review_db.find_latest_rule_by_business_id(data.business_id)
    check_result = True
    check_result_map = {}
    for rule_tuple in rule_tuple_list:
        rule = LibraExperimentFieldAndRule(*rule_tuple)
        # print(rule.field_name)
        # 由于某些原因跳过检查
        if skip_check(data, rule):
            continue
        # 取待检查字段
        actual_field_value = getattr(data, rule.field_key)
        # 取期望字段
        expect_field_value = get_value_by_type(rule.expect_value, rule.field_type, rule.operator_type, rule.is_check_value)
        # 检查规则
        field_check_map = check_value(expect_field_value, actual_field_value, rule, data.experiment_qa_reviewer)
        check_result = False if (field_check_map["result"] is False and rule.is_must) else check_result
        field_check_map["id"] = rule.id
        field_check_map["field_name"] = rule.field_name
        check_result_map[rule.id] = field_check_map
    byte_log.info(f"check_experiment_by_rule check_result_map={check_result_map}")
    # 检查结果入库
    review_result = AddLibraExperimentReviewResultParams(libra_experiment_id=data.id, is_pass=check_result)
    review_result = libra_experiment_review_db.insert_experiment_review_result(review_result)
    for key in check_result_map:
        result_detail = AddLibraExperimentReviewResultRuleDetailParams(libra_experiment_review_result_id=review_result.id,
                                                                       libra_experiment_field_review_rule_id=check_result_map[key]["id"],
                                                                       is_pass=check_result_map[key]["result"],
                                                                       message=check_result_map[key]["msg"])
        libra_experiment_review_db.insert_experiment_review_result_rule_detail(result_detail)

    # 检查结果发送飞书卡片
    send_result_msg_to_lark_card(data, check_result, check_result_map)


def get_qa_reminder_list(data):
    qa_reminder_list = data.experiment_qa_reviewer
    # 测试环境通知的人，替换
    if deploy_cluster_env not in [DeployCluster.CN_ONLINE]:
        qa_reminder_list = LARK_CARD_TEST_REMINDER_LIST
    return qa_reminder_list
def get_rd_reminder_list(data):
    rd_reminder_list = [data.experiment_creator]
    # 测试环境通知的人，替换
    if deploy_cluster_env not in [DeployCluster.CN_ONLINE]:
        rd_reminder_list = LARK_CARD_TEST_REMINDER_LIST
    return rd_reminder_list

def set_result_detail_msg_pass(check_result_map):
    temp_map = {}
    msg_pass = ''
    for key in check_result_map:
        if check_result_map[key]["result"] is True:
            if check_result_map[key]["field_name"] not in temp_map:
                temp_map[check_result_map[key]["field_name"]] = {
                    "ruleStr": [check_result_map[key]["ruleStr"]],
                    "value": check_result_map[key]["value"]
                }
            else:
                temp_map[check_result_map[key]["field_name"]]["ruleStr"].append(check_result_map[key]["ruleStr"])
    for key in temp_map:
        value = temp_map[key]["value"]
        value_msg = str(value) if (len(str(value)) <= VALUE_LENGTH_LIMIT) else '（太长不展示）'
        msg_pass = msg_pass + "- %s： %s ， 实际值：*%s*" %(key, '；'.join(temp_map[key]["ruleStr"]), value_msg) + '\n'
    return msg_pass


def send_result_msg_to_lark_card(data: LibraExperimentAndTestReport, check_result, check_result_map):
    qa_reminder_list = get_qa_reminder_list(data)
    rd_reminder_list = get_rd_reminder_list(data)
    reminder_list = [qa_reminder_list, rd_reminder_list]
    byte_log.info(f"send_result_msg_to_lark_card reminder_list={reminder_list}")

    variable_item = {
        "experiment_name": data.experiment_name,
        "experiment_reviewer": ', '.join([item for item in data.experiment_reviewer]),
        "experiment_creator": data.experiment_creator,
        # "result_detail_msg_pass": '\n'.join(
        #     ["- %s： %s " % (check_result_map[key]["field_name"], check_result_map[key]["ruleStr"])
        #      for key in check_result_map
        #      if check_result_map[key]["result"] is True]),
        "result_detail_msg_pass": set_result_detail_msg_pass(check_result_map),
        "result_detail_msg_fail": '\n'.join([check_result_map[key]["msg"]
                                             for key in check_result_map
                                             if check_result_map[key]["result"] is False]),
        "experiment_libra_url": data.experiment_libra_url,
        "result_color": LARK_CARD_CONST_LIST[int(check_result)]["result_color"],
        "result_str": LARK_CARD_CONST_LIST[int(check_result)]["result_str"]
    }
    if any(element in settings.VOD_REVIEWER_EMAIL_LIST for element in data.experiment_reviewer) and int(check_result)==0:
        variable_item["operation_msg"] = LARK_CARD_OPERATION_MSG_LIST[0][
                                             int(check_result)] % data.experiment_libra_url

        byte_log.info(
            f"send_result_msg_to_lark_card vod...")
        result = larkRobot.post_robot_card_libra_vod_group_message(
                                                   template_id=settings.LARK_LIBRA_REVIEW_RESULT_CARD_ID,
                                                   template_variable=variable_item, retry_time=3)
        if not result:
            byte_log.error(f"send_result_msg_to_lark_card fail!")
    if any(element in settings.LIVE_REVIEWER_EMAIL_LIST for element in data.experiment_reviewer) and int(check_result) == 0:
        variable_item["operation_msg"] = LARK_CARD_OPERATION_MSG_LIST[0][int(check_result)] % data.experiment_libra_url
        byte_log.info(f"send_result_msg_to_lark_card live...")
        result = larkRobot.post_robot_card_libra_live_group_message(
            template_id=settings.LARK_LIBRA_REVIEW_RESULT_CARD_ID,
            template_variable=variable_item, retry_time=3)
        if not result:
            byte_log.error(f"send_result_msg_to_lark_card fail!")
    for index in range(len(reminder_list)):
        for item in reminder_list[index]:
            try:
                user_info = larkRobot.post_open_id(emails=[item + LARK_EMAIL_SUFFIX])[0]
                byte_log.info(f"send_result_msg_to_lark_card user=%s" % user_info)
                open_id_item = user_info['user_id']
            except Exception as e:
                error_stack = traceback.format_exc()
                byte_log.error(f"send_result_msg_to_lark_card reminder={item} get open id fail: {error_stack}")
                continue
            variable_item["user_id"] = open_id_item
            variable_item["operation_msg"] = LARK_CARD_OPERATION_MSG_LIST[index][int(check_result)] % data.experiment_libra_url
            byte_log.info(f"send_result_msg_to_lark_card reminder={item} open_id_item={open_id_item} variable_item={variable_item}")
            result = larkRobot.post_robot_card_message(open_id=open_id_item, template_id=settings.LARK_LIBRA_REVIEW_RESULT_CARD_ID,
                                               template_variable=variable_item, retry_time=3)
            if not result:
                byte_log.error(f"send_result_msg_to_lark_card reminder={item} send lark msg fail!")



if __name__ == '__main__':
    experiment = libra_experiment_review_db.find_experiment_and_test_report_by_id(14)
    check_experiment_by_rule(experiment)

    experiment_meego_info = {
        "meego_array": [
            {
                "meego_project_key": "63f8248a4fb69c181cb0b14b",
                "meego_project": "bytertc_oc",
                "meego_story": "5341802322",
                "meego_libra": "7407335782824951810"
            }
        ]
    }
    get_meego_test_report(experiment_meego_info, libra_experiment_id=14)
