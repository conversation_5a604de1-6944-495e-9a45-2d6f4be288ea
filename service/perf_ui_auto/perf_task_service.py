from defines import perf_ui_auto_define
from defines.lark_robot_define import LARK_EMAIL_SUFFIX
from models.perf_ui_auto.perf_task_model import PerfUiAutoTask, PerfUiAutoSubTask
from proj_settings import settings,deploy_cluster_env, DeployCluster
from utils.common.bytelog import byte_log
from utils.openapi.lark_robot_api import larkRobot

def find_delete_list(sub_task_in_db_list, update_model_list):
    ids1 = {item.id for item in sub_task_in_db_list}
    ids2 = {item['id'] for item in update_model_list}
    result_list = []
    # 找出仅在list1中出现的id
    only_in_list1 = ids1 - ids2
    for item in sub_task_in_db_list:
        if item.id in only_in_list1:
            result_list.append(item)
    return result_list

def get_reminder_list(task_owner):
    reminder_list = [task_owner]
    # 测试环境通知的人，替换
    if deploy_cluster_env not in [DeployCluster.CN_ONLINE]:
        if task_owner not in perf_ui_auto_define.LARK_CARD_TEST_REMINDER_LIST:
            reminder_list = []
    return reminder_list


def send_lark_card(task_item: PerfUiAutoTask, sub_task_items: list[PerfUiAutoSubTask]):
    byte_log.info(f"send_lark_card start")

    # 组装数据
    sub_task_info = ''
    for index, sub_task_item in enumerate(sub_task_items):
        sub_task_info += perf_ui_auto_define.SUB_TASK_INFO_TEMPLATE % (
            index+1,
            sub_task_item.name,
            perf_ui_auto_define.TASK_STATUS_MAP[sub_task_item.status],
            f'{sub_task_item.start_time} - {sub_task_item.end_time}'
        )
    variable_item = {
        "task_name": task_item.name,
        "task_time": f'{task_item.start_time} - {task_item.end_time}',
        "task_status_color": perf_ui_auto_define.TASK_STATUS_COLOR_MAP[task_item.status],
        "task_url": f'{settings.FRONT_END_HOST}/perf-ui-auto/task/detail?task_id={task_item.id}',
        "task_result": perf_ui_auto_define.TASK_STATUS_MAP[task_item.status],
        "sub_task_info": sub_task_info,
    }
    byte_log.info(f"check_task_status_send_lark_card variable_item={variable_item}")
    # 发送飞书消息
    reminder_list = get_reminder_list(task_item.owner)
    byte_log.info(f"check_task_status_send_lark_card reminder_list={reminder_list}")
    for item in reminder_list:
        try:
            open_id_item = larkRobot.post_open_id(emails=[item + LARK_EMAIL_SUFFIX])[0]['user_id']
        except Exception as e:
            byte_log.error(f"check_task_status_send_lark_card reminder={item} get open id fail")
            continue
        variable_item["user_id"] = open_id_item
        byte_log.info(f"check_task_status_send_lark_card reminder={item} open_id_item={open_id_item} variable_item={variable_item}")
        result = larkRobot.post_robot_card_message(open_id=open_id_item,
                                                   template_id=settings.LARK_PERF_TASK_STATUS_CARD_ID,
                                                   template_variable=variable_item, retry_time=3)
        if not result:
            byte_log.error(f"check_task_status_send_lark_card reminder={item} send lark msg fail!")
    return

