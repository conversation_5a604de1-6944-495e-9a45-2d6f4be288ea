if [ "$(uname)" = "Linux" ]; then
    # 获取项目根目录
    PROJECT_ROOT=$(pwd)

    # 设置 PYTHONPATH
    export PYTHONPATH=$PROJECT_ROOT

    # 更新包管理器
    echo "Updating package manager..."
    apt-get update

    # 安装基础开发工具
    echo "Installing development tools..."
    if ! command -v gcc &> /dev/null; then
        echo "Installing GCC..."
        apt-get -y remove gcc
        apt-get -y install gcc
    else
        echo "GCC already installed"
    fi

    # 安装 Python 开发环境
    echo "Installing Python development environment..."
    if ! dpkg -l | grep -q python-dev; then
        echo "Installing python-dev..."
        apt-get -y install python-dev
    else
        echo "python-dev already installed"
    fi

    # 安装 MySQL 客户端开发库
    echo "Installing MySQL client development library..."
    if ! dpkg -l | grep -q libmysqlclient-dev; then
        echo "Installing libmysqlclient-dev..."
        apt-get -y install libmysqlclient-dev
    else
        echo "libmysqlclient-dev already installed"
    fi

    # 安装 uvicorn
    echo "Installing uvicorn..."
    if ! command -v uvicorn &> /dev/null; then
        echo "Installing uvicorn..."
        apt-get -y install uvicorn
    else
        echo "uvicorn already installed"
    fi

    # 安装 git
    echo "Installing git..."
    if ! command -v git &> /dev/null; then
        echo "Installing git..."
        apt-get -y install git
    else
        echo "git already installed"
    fi

    # 检查并创建虚拟环境
    if [ ! -d "$PROJECT_ROOT/.venv" ]; then
        echo "Creating virtual environment..."
        python3 -m venv .venv
    fi

    # 激活虚拟环境
    echo "Activating virtual environment..."
    source .venv/bin/activate

    # 更新 pip
    echo "Updating pip..."
    python -m pip install --upgrade pip

    # 安装依赖包
    echo "Installing requirements..."
    pip install -r requirements.txt -i https://bytedpypi.byted.org/simple/ -i https://shoots-pypi.bytedance.net/simple/

    python app.py

elif [ "$(uname)" = "Darwin" ]; then
    # 获取项目根目录
    PROJECT_ROOT=$(pwd)

    # 设置 PYTHONPATH
    export PYTHONPATH=$PROJECT_ROOT

    # 检查并安装 Homebrew
    if ! command -v brew &> /dev/null; then
        echo "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://gitee.com/ineo6/homebrew-install/raw/master/install.sh)"
    else
        echo "Updating Homebrew..."
        brew update
        echo "Homebrew already installed"
    fi


    # 检查并安装 Python 3.9
    if ! command -v python3.9 &> /dev/null; then
        echo "Installing Python 3.9..."
        brew install python@3.9
    else
        echo "Python 3.9 already installed"
    fi

    # 检查并创建虚拟环境
    if [ ! -d "$PROJECT_ROOT/.venv" ]; then
        echo "Creating virtual environment..."
        python3.9 -m venv .venv
    fi

    # 激活虚拟环境
    echo "Activating virtual environment..."
    source .venv/bin/activate

    # 更新 pip
    echo "Updating pip..."
    python3.9 -m pip install --upgrade pip

    # 安装依赖包
    echo "Installing requirements..."
    pip install -r requirements.txt -i https://bytedpypi.byted.org/simple/ -i https://shoots-pypi.bytedance.net/simple/

    python3.9 app.py
fi