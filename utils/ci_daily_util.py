import json

from configs import ci_daily_conf
from dals.db.test_manager.ci_daily_db import ci_daily_db
from defines.ci_daily_define import MODULE_QA_MAP, CI, Daily
from models.ci_daily_model import MrTask, TestTask, TestPlatform, TestCaseDetail
from utils.common import file_util
from utils.common import time_util
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod
from utils.common.request_util import request_base
from utils.openapi.bits_api import bits
from utils.openapi.vpass_api import vpass


class Ci_Daily:
    def __init__(self, pipeline_type: int, days: int):
        """
        @param pipeline_type:
        @param days:
        """
        self.pipeline_type = pipeline_type
        self.time_range = time_util.get_daily_time_range(days) if pipeline_type else time_util.get_ci_time_range(
            days)
        byte_log.info(f"初始化CI/Daily数据收集, pipeline_type: {pipeline_type}, days: {days}")

    def get_daily_pipeline_ids_by_time_range(self):
        """
        通过时间区间获取daily的流水线ID列表
        @return:
        """
        byte_log.info("开始获取daily流水线ID列表")

        # 定义两个不同的测试任务过滤器
        filters = [
            "{\"template_id\": 1, \"name\": \"VideoEngineMT_功能自动化测试\", \"status\": [800]}",
            "{\"template_id\": 1, \"name\": \"VideoEngine MTV2_功能自动化测试\", \"status\": [800]}"
        ]

        all_pipeline_ids = []

        # 对每个过滤器分别调用接口
        for filter_str in filters:
            byte_log.info(f"使用过滤器获取任务列表: {filter_str}")
            current = 1
            page_size = 10
            pipeline_ids = []

            while True:
                res = vpass.get_task_list(current=current, pageSize=page_size, filter=filter_str)
                if not res:
                    byte_log.error("获取任务列表失败")
                    break
                if not res["response"]["total"]:
                    break
                responses = res["response"]["response"]
                if not responses:
                    break

                for response in responses:
                    finished = response["finished"]
                    if not finished:
                        continue
                    created_at = response["created_at"]
                    if self.time_range[0] < created_at < self.time_range[1]:
                        extra = json.loads(response["extra"])
                        if "RunPipelineID" not in extra:
                            continue
                        pipeline_id = extra["RunPipelineID"]
                        pipeline_ids.append(int(pipeline_id))

                current += 1
                if len(responses) < page_size:
                    break
                if responses[-1]["created_at"] < self.time_range[0]:
                    break

            byte_log.info(f"当前过滤器获取到{len(pipeline_ids)}个流水线ID")
            all_pipeline_ids.extend(pipeline_ids)

        # 去重处理
        all_pipeline_ids = list(set(all_pipeline_ids))
        byte_log.info(f"总共获取到{len(all_pipeline_ids)}个唯一流水线ID")
        return all_pipeline_ids

    def get_ci_mr_data_by_time_range(self):
        """
        通过时间区间获取CI MR数据列表
        @return:
        """
        byte_log.info("开始获取CI MR数据列表")
        mr_datas = []
        mr_ids = []
        last_id = 0
        
        # 获取已存在的 uniq_mr_id 列表
        _, existed_mr_ids = ci_daily_db.query([MrTask.uniq_mr_id.distinct()])
        existed_mr_ids = [i[0] for i in existed_mr_ids] if existed_mr_ids else []
        byte_log.info(f"已存在{len(existed_mr_ids)}个MR记录")
        
        while True:
            res = bits.get_mr_list(last_id=last_id)
            datas = res["data"]
            for data in datas:
                new_mr_data = {}
                mr_create_time = data["create_time"]
                if self.time_range[0] < mr_create_time < self.time_range[1]:
                    # 检查 uniq_mr_id 是否已存在
                    if data["id"] in existed_mr_ids:
                        mr_ids.append(data["id"])
                        continue
                        
                    new_mr_data["uniq_mr_id"] = data["id"]
                    mr_ids.append(data["id"])
                    new_mr_data["mr_state"] = data["state"]
                    new_mr_data["mr_create_time"] = time_util.timestamp_convert_time(data["create_time"])
                    mr_datas.append(new_mr_data)
            last_id = datas[-1]["id"]
            if datas[-1]["create_time"] < self.time_range[0]:
                break
                
        if mr_datas:
            byte_log.info(f"新增{len(mr_datas)}个MR记录")
            mr_task_items = [MrTask(**data) for data in mr_datas]
            # 批量插入新记录
            ci_daily_db.add_more(mr_task_items)
            # 获取所有相关的 mr_task_ids（包括新插入和已存在的）
            filters = [MrTask.uniq_mr_id.in_([data["uniq_mr_id"] for data in mr_datas])]
            _, results = ci_daily_db.query([MrTask.id], filters=filters)
            mr_task_ids = [item[0] for item in results] if results else []
            return mr_task_ids, mr_ids
        byte_log.info("无新增MR记录")
        return [], mr_ids

    def get_old_mr_opened_data(self):
        """
        获取之前opened的mr数据
        @return:
        """
        byte_log.info("开始获取历史opened状态的MR数据")
        mr_task_ids_list = []
        mr_ids_list = []
        filters = [MrTask.mr_state == "opened"]
        _, results = ci_daily_db.query([MrTask.id, MrTask.uniq_mr_id, MrTask.mr_state], filters=filters)
        if not results:
            byte_log.info("无历史opened状态的MR数据")
            return [], []
        old_mr_task_ids, old_mr_ids, old_mr_states = list(zip(*results))
        byte_log.info(f"找到{len(old_mr_ids)}条历史opened状态的MR记录")
        for old_mr_task_id, old_mr_id, old_mr_state in zip(old_mr_task_ids, old_mr_ids, old_mr_states):
            res = bits.get_mr_info(old_mr_id)
            data = res["data"] if res else {}
            if not data:
                continue
            if data["state"] == old_mr_state:
                mr_task_ids_list.append(old_mr_task_id)
                mr_ids_list.append(old_mr_id)
            else:
                byte_log.info(f"MR {old_mr_id} 状态已从opened变更为 {data['state']}")
                ci_daily_db.update_one(ci_daily_db.query(MrTask, filters=[MrTask.id == old_mr_task_id],
                                                         return_first=True), {"mr_state": data["state"]})
        return mr_task_ids_list, mr_ids_list

    def get_ci_mr_pipeline_list_data(self):
        """
        获取mr下的流水线列表数据
        @return:
        """
        byte_log.info("开始获取MR关联的流水线列表")
        mr_task_ids_list = []
        pipelines_list = []
        mr_task_ids, mr_ids = self.get_ci_mr_data_by_time_range()
        old_mr_task_ids, old_mr_ids = self.get_old_mr_opened_data()
        new_mr_task_ids = mr_task_ids + old_mr_task_ids
        new_mr_ids = mr_ids + old_mr_ids
        for mr_task_id, mr_id in zip(new_mr_task_ids, new_mr_ids):
            res = bits.get_pipeline_list(mr_id)
            datas = res["data"] if res else []
            if not datas:
                continue
            for data in datas:
                mr_task_ids_list.append(mr_id)
                pipelines_list.append(data["gitlab_pipeline_id"])
        byte_log.info(f"获取到{len(pipelines_list)}条流水线记录")
        return mr_task_ids_list, pipelines_list

    def get_pipeline_detail_data(self):
        """
        获取流水线详情数据
        @return:
        """
        byte_log.info("开始获取流水线详情数据")
        pipeline_info_datas = []
        pipelines_jobs_ids = []
        ci_job_names = ["RTCGlobal_CI_Android_WatchCat_Api_Test", "RTCGlobal_CI_iOS_WatchCat_Api_Test",
                        "RTCGlobal_CI_Linux_WatchCat_Api_Test"]
        daily_job_names = ["Android_RTCGlobal_ApiTestJob", "iOS_RTCGlobal_ApiTestJob",
                           "Windows_RTCGlobal_ApiTestJob", "Linux_RTCGlobal_ApiTestJob",
                           "Mac_RTCGlobal_ApiTestJob"]
        if not self.pipeline_type:
            mr_task_ids, pipeline_ids = self.get_ci_mr_pipeline_list_data()
        else:
            pipeline_ids = self.get_daily_pipeline_ids_by_time_range()
            mr_task_ids = [None for _ in pipeline_ids]
        _, existed_pipeline_ids = ci_daily_db.query([TestTask.pipeline_id.distinct()])
        byte_log.info(f"已存在{len(existed_pipeline_ids)}条流水线记录")
        for mr_task_id, pipeline_id in zip(mr_task_ids, pipeline_ids):
            if pipeline_id in [i[0] for i in existed_pipeline_ids]:
                continue
            pipeline_detail_res = bits.get_pipeline_detail(pipeline_id)
            pipeline_detail_data = pipeline_detail_res["data"] if pipeline_detail_res else None
            if not pipeline_detail_data or pipeline_detail_data["status"] in ["running"]:
                continue
            env = pipeline_detail_data["env"] if "env" in pipeline_detail_data else None
            branch = env["WORKFLOW_REPO_BRANCH"]
            if self.pipeline_type and branch != "main" and "release" not in branch:
                continue
            if ("created" not in pipeline_detail_data) or ("stopped" not in pipeline_detail_data):
                continue
            new_pipeline_info = {
                "idx_mr_task_id": mr_task_id,
                "pipeline_id": pipeline_id,
                "branch": branch,
                "pipeline_type": "Daily" if self.pipeline_type else "CI",
                "pipeline_user": env["WORKFLOW_PIPELINE_USER"],
                "pipeline_create_time": time_util.timestamp_convert_time(pipeline_detail_data["created"]),
                "pipeline_finish_time": time_util.timestamp_convert_time(pipeline_detail_data["stopped"]),
                "task_url": env["WORKFLOW_PIPELINE_URL"]
            }
            pipeline_info_datas.append(new_pipeline_info)
            jobs = pipeline_detail_data["jobs"] if "jobs" in pipeline_detail_data else None
            pipelines_jobs_ids.append(
                [job["jobId"] for job in jobs if job["name"] in set(ci_job_names + daily_job_names)])
        byte_log.info(f"新增{len(pipeline_info_datas)}条流水线记录")
        test_task_items = [TestTask(**data) for data in pipeline_info_datas]
        ci_daily_db.add_more(test_task_items)
        test_task_ids = [item.id for item in test_task_items]
        return test_task_ids, pipelines_jobs_ids

    def get_job_detail_data(self):
        """
        获取job详情数据
        @return:
        """
        byte_log.info("开始获取job详情数据")
        job_info_datas = []
        data_log_urls = []
        job_ids_list = []
        test_task_ids, pipelines_jobs_ids = self.get_pipeline_detail_data()
        new_test_task_ids = []
        new_jobs_ids = []
        for test_task_id, job_ids in zip(test_task_ids, pipelines_jobs_ids):
            for job_id in job_ids:
                new_test_task_ids.append(test_task_id)
                new_jobs_ids.append(job_id)
                while True:
                    job_detail_info = bits.get_job_detail(job_id)
                    data = job_detail_info["data"] if "data" in job_detail_info else None
                    if not data:
                        break
                    parent_id = data["parentId"]
                    if not parent_id:
                        break
                    job_id = parent_id
                    new_test_task_ids.append(test_task_id)
                    new_jobs_ids.append(parent_id)
        for test_task_id, job_id in zip(new_test_task_ids, new_jobs_ids):
            job_detail_info = bits.get_job_detail(job_id)
            job_detail_data = job_detail_info["data"] if "data" in job_detail_info else None
            if not job_detail_data["jobResult"]:
                continue
            job_result = json.loads(job_detail_data["jobResult"])
            platform = job_result["platform"]
            new_job = {
                "idx_test_task_id": test_task_id,
                "job_id": job_id, "platform": platform,
                "watchcat_demo_url": job_result[f"{platform}_demo_url"],
                "case_log_url": job_result[
                    f"{platform}OutputZipUrl"] if f"{platform}OutputZipUrl" in job_result else None,
                "start_time": time_util.timestamp_convert_time(job_detail_data["started"]),
                "end_time": time_util.timestamp_convert_time(job_detail_data["stopped"]),
                "job_url": job_detail_data["jobURL"]
            }
            data_log_url = job_result[f"{platform}DataLogUrl"] if f"{platform}DataLogUrl" in job_result else None
            if not data_log_url:
                continue
            data_log_res = request_base.send_request(method=RequestMethod.GET.value,
                                                     url=data_log_url) if data_log_url else None
            data_log_res_json = data_log_res.json() if data_log_res.status_code == 200 else {}
            new_job["total_count"] = data_log_res_json["total"]
            new_job["pass_count"] = data_log_res_json["pass_count"]
            new_job["fail_count"] = data_log_res_json["total"] - data_log_res_json["pass_count"]
            if not new_job["fail_count"]:
                continue
            data_log_urls.append(data_log_url)
            job_ids_list.append(job_id)
            job_info_datas.append(new_job)
        byte_log.info(f"新增{len(job_info_datas)}条job记录")
        test_platform_items = [TestPlatform(**data) for data in job_info_datas]
        ci_daily_db.add_more(test_platform_items)
        test_platform_ids = [item.id for item in test_platform_items]
        return test_platform_ids, job_ids_list, data_log_urls

    def get_test_case_detail(self):
        """
        获取测试用例详情
        @return:
        """
        byte_log.info("开始获取测试用例详情数据")
        cases_detail = []
        test_platform_ids, job_ids, data_log_urls = self.get_job_detail_data()
        auto_attribution_list = file_util.yaml_reader(ci_daily_conf, "auto_attribution")
        for job_id, data_log_url in zip(job_ids, data_log_urls):
            data_log_res = request_base.send_request(method=RequestMethod.GET.value,
                                                     url=data_log_url) if data_log_url else None
            data_log_res_json = data_log_res.json() if data_log_res.status_code == 200 else {}
            fail_list = data_log_res_json["fail_list"] + data_log_res_json["dont_get_device_list"]
            if "warning_list" in data_log_res_json:
                fail_list += data_log_res_json["warning_list"]
            if not fail_list:
                continue
            for fail_case in fail_list:
                result_code = fail_case["error_code"]
                new_case_detail = {
                    "idx_test_task_platform_id": test_platform_ids[job_ids.index(job_id)],
                    "case_id": fail_case["case_id"],
                    "result_code": result_code,
                    "result_message": fail_case["error_message"],
                    "module": next((key for key, val in MODULE_QA_MAP.items() if fail_case["qa_owner"] in val), -1),
                    "qa_owner": fail_case["qa_owner"],
                    "case_log_url": "",
                    "watchcat_log_url": "",
                    "start_time": time_util.timestamp_convert_time(fail_case["start_time"] / 1000),
                    "end_time": time_util.timestamp_convert_time(fail_case["end_time"] / 1000),
                    "cost_time": int((fail_case["end_time"] - fail_case["start_time"]) / 1000),
                    "expect_assert": str(fail_case["except_result"]),
                    "actual_assert": str(fail_case["actual_result"]),
                    "device_info_list": str(fail_case["user_info_list"]),
                    "fail_reason_str": "",
                    "fail_reason_type": 0,
                    "improvement_measure_str": "",
                    "is_closed": 0,
                    "meego_bug_url": "",
                    "attribution_type": 0
                }
                for auto_attribution in auto_attribution_list:
                    if result_code == auto_attribution["result_code"]:
                        new_case_detail["fail_reason_type"] = auto_attribution["fail_reason_type"]
                        new_case_detail["fail_reason_str"] = auto_attribution["fail_reason_str"]
                        new_case_detail["improvement_measure_str"] = auto_attribution["improvement_measure_str"]
                        new_case_detail["attribution_type"] = 1
                if new_case_detail not in cases_detail:
                    cases_detail.append(new_case_detail)
        byte_log.info(f"新增{len(cases_detail)}条测试用例详情记录")
        test_case_detail_items = [TestCaseDetail(**data) for data in cases_detail]
        ci_daily_db.add_more(test_case_detail_items)
        return cases_detail

    def del_repeat_data(self):
        """
        删除重复数据
        @return:
        """
        pass


if __name__ == "__main__":
    days = 1
    Ci_Daily(pipeline_type=Daily, days=days).get_test_case_detail()
    # Ci_Daily(pipeline_type=CI, days=days).get_test_case_detail()
