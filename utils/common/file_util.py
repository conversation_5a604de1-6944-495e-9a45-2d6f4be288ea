import fnmatch
import os
import shutil
import zipfile

import yaml

from utils.common.bytelog import byte_log
# from utils.common.log import custom_logger
from utils.common.other_util import generate_temp_unique_id


def create_proj_dirs(folder_path):
    """
    创建文件夹
    :param folder_path:
    :return:
    """
    if os.path.exists(folder_path):
        return False
    # 创建文件夹
    os.makedirs(folder_path)
    return True


def get_files_with_specific_extension(folder_path, extensions: list):
    """
    获取文件夹下一种或多种格式的所有文件（所有子文件夹）
    @param folder_path:
    @param extensions:
    @return:
    """
    # 定义后缀名文件格式列表
    file_list = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            for extension in extensions:
                if file.endswith(extension):
                    file_list.append(os.path.join(root, file))
        if not extensions:
            for d in dirs:
                items = os.listdir(os.path.join(root, d))
                if any(os.path.isdir(os.path.join(root, d, item)) for item in items):
                    continue
                file_list.append(os.path.join(root, d))
    file_list.sort()
    return file_list


def clean_file(folder_path, patterns: list = None):
    """
    清除文件（所有子文件夹）
    @param folder_path:
    @param patterns:
    @return:
    """
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            for pattern in patterns:
                if fnmatch.fnmatch(file, pattern):
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        byte_log.debug(f"已删除: {file_path}")
                    except Exception as e:
                        byte_log.debug(f"删除 {file_path} 时出错: {e}")


def yaml_reader(yaml_path, key):
    """
    yaml文件读取
    @param yaml_path:
    @param key:
    @return:
    """
    with open(yaml_path, "r") as file:
        data = yaml.safe_load(file)
        return data.get(key)


def upload_zip_file(zip_file):
    """
    上传zip文件
    @param zip_file:
    @return:
    """
    if not zip_file.filename.endswith('.zip'):
        return None
    # 创建一个临时目录来存储上传的压缩文件
    temp_save_id = generate_temp_unique_id()
    temp_file_save_dir = f"temp/{temp_save_id}"
    os.makedirs(temp_file_save_dir, exist_ok=True)

    # 保存上传的压缩文件
    file_path = os.path.join(temp_file_save_dir, zip_file.filename)
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(zip_file.file, buffer)

    # 解压压缩文件
    os.makedirs(temp_file_save_dir, exist_ok=True)

    with zipfile.ZipFile(file_path, 'r') as zip_ref:
        zip_ref.extractall(temp_file_save_dir)

    folder_path = os.path.join(os.getcwd(), temp_file_save_dir)

    return folder_path


def upload_file(file):
    # 创建一个临时目录来存储上传的文件
    temp_save_id = generate_temp_unique_id()
    temp_file_save_dir = f"temp/{temp_save_id}"
    os.makedirs(temp_file_save_dir, exist_ok=True)

    file_path = os.path.join(os.getcwd(), temp_file_save_dir, file.name)
    return file_path


if __name__ == "__main__":
    print(get_files_with_specific_extension(
        "/Users/<USER>/global_rtc_test_platform/backend/temp/088f36dc-145d-41ae-bbc8-369effd5a070/android", []))
