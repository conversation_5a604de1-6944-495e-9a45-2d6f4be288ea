'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-02 16:03:03
FilePath: /global_rtc_test_platform/utils/common/response.py
Description: 
'''
from typing import TypeVar, Generic, Union

from pydantic import BaseModel

T = TypeVar("T")  # 泛型 T ：https://docs.pydantic.dev/usage/models/#generic-models


class HttpResponse(BaseModel, Generic[T]):
    """http统一响应"""
    code: int  # 响应码
    msg: str  # 响应信息
    data: Union[T, list[T], None]


def response_success(data, msg: str = "success", code: int = 200) -> HttpResponse[T]:
    """响应成功"""
    return HttpResponse(code=code, msg=msg, data=data)


def response_fail(msg: str, code: int = -1, data=None) -> HttpResponse[T]:
    """响应失败"""
    return HttpResponse(
        code=code,
        msg=msg,
        data=data
    )
