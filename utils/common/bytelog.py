import bytedlogger
#用于生成logid，使用pip install bytedlogid进行安装，建议使用最新版本
import logid
import logging
import time

#使用bytedlogger的内置方法初始化logger配置
#bytedlogger.config_default()的详细参数和配置参考bytedlogger,其他自定义配置均可参考默认配置进行改动
#线下测试场景，如果日志上报量较小，可以使用bytedlogger.config_default(use_background=False)
def init_logger():
    bytedlogger.config_default(use_background=False)

def generate_log_id():
    return bytes(logid.generate_v2(), encoding='utf-8')

class ByteLog:

    log_id = generate_log_id()
    def __init__(self):
        init_logger()
    def set_log_id(self, refresh=False):
        if refresh is True:
            # 使用bytedlogger的内置方法设置logid，logid.generate_v2()可生成符合规范的logid
            # 也可使用bytedance.context设置logid
            self.log_id = generate_log_id()
            # logging.info(f"Test Log {time.time()}")
        bytedlogger.thread_storage[b'_logid'] = self.log_id


    def debug(self, msg, refresh=False):
        self.set_log_id(refresh)
        logging.debug(msg)

    def info(self, msg, refresh=False):
        self.set_log_id(refresh)
        logging.info(msg)

    def warning(self, msg, refresh=False):
        self.set_log_id(refresh)
        logging.warning(msg)

    def error(self, msg, refresh=False):
        self.set_log_id(refresh)
        logging.error(msg)

    def exception(self, e, refresh=False):
        self.set_log_id(refresh)
        logging.exception(e)


byte_log = ByteLog()
