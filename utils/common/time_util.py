import datetime
import time


def get_current_timestamp():
    """
    获取当前时间戳
    :return:
    """
    current_timestamp = time.time()
    return current_timestamp


def get_ago_time_and_timestamp(days=0, seconds=0, microseconds=0, milliseconds=0, minutes=0, hours=0, weeks=0):
    """
    获取之前的时间戳
    :return:
    """
    # 获取当前时间戳
    now_time = time.time()
    # 将当前时间戳转换为 datetime 对象
    now = datetime.datetime.fromtimestamp(now_time)
    # 计算一周前的时间
    ago_time = now - datetime.timedelta(days=days, seconds=seconds, microseconds=microseconds,
                                        milliseconds=milliseconds, minutes=minutes, hours=hours, weeks=weeks)
    # 将计算后的时间转换为时间戳
    ago_timestamp = time.mktime(ago_time.timetuple())
    return ago_time, ago_timestamp


def get_current_date():
    """
    获取当前日期
    :return:
    """
    current_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())).split(" ")[0]
    return current_date


def get_current_time():
    """
    获取当前时间
    :return:
    """
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
    return current_time


def dt_str_convert_dt(dt_str):
    """
    字符串转成datetime
    @param dt_str:
    @return:
    """
    dt = datetime.datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
    return dt


def time_convert_timestamp(time_str):
    """
    时间转化为时间戳
    :param time_str:
    :return:
    """
    time_tuple = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    timestamp = int(time.mktime(time_tuple))
    return timestamp


def timestamp_convert_time(timestamp):
    """
    时间戳转换为时间
    :param timestamp:
    :return:
    """
    # 使用 datetime 模块将时间戳转换为 datetime 对象
    datetime_obj = datetime.datetime.fromtimestamp(timestamp)
    # 将 datetime 对象格式化为字符串，以便可读的格式显示
    formatted_time = datetime_obj.strftime("%Y-%m-%d %H:%M:%S")
    return formatted_time


def get_ci_time_range(days):
    """
    获取ci数据的时间区间
    @param days:
    @return:
    """
    # 获取当前时间
    now = datetime.datetime.now()
    end_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    before_time = end_time - datetime.timedelta(days=days)
    # 获取时间戳（整数）
    before_timestamp = int(before_time.timestamp())
    end_timestamp = int(end_time.timestamp())
    return before_timestamp, end_timestamp


def get_daily_time_range(days):
    """
    获取daily数据的时间区间
    @return:
    """
    # 获取当前时间
    now = datetime.datetime.now()
    end_time = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
    before_time = end_time - datetime.timedelta(days=days)
    # 获取时间戳（整数）
    before_timestamp = int(before_time.timestamp())
    end_timestamp = int(end_time.timestamp())
    return before_timestamp, end_timestamp


if __name__ == "__main__":
    # get_ci_time_range(1)
    print(get_current_timestamp())
