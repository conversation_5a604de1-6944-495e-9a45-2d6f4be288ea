from enum import Enum

import requests

from utils.common.bytelog import byte_log


class RequestMethod(Enum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    OPTIONS = "OPTIONS"
    HEAD = "HEAD"


class RequestBase:
    def __init__(self):
        pass

    def send_request(self, method, url, headers=None, data=None, json=None, params=None, cookies=None, proxies=None,
                     auth=None, timeout=None, verify=None):
        """
        发送 HTTP 请求的方法。

        参数:
            url (str): 请求的 URL 地址。
            method (str): 请求的方法，例如 GET、POST、PUT、DELETE 等。
            headers (dict): 请求的头部信息。
            data (dict): 请求的数据（非 JSON 格式）。
            json (dict): 请求的数据（JSON 格式）。
            params (dict): 请求的参数。
            cookies (dict): 请求的 cookies。
            proxies (dict): 请求的代理服务器。
            auth (tuple): 请求的认证信息，格式为 (username, password)。
            timeout (float): 请求的超时时间（秒）。
            verify (bool): 是否验证 SSL 证书。

        返回:
            requests.Response: 包含了服务器响应的对象。
        """
        try:
            response = requests.request(method, url, headers=headers, data=data, json=json, params=params,
                                        cookies=cookies,
                                        proxies=proxies, auth=auth, timeout=timeout, verify=verify)

            # byte_log.info(
            #     f"request success. url: {url}, status_code:{response.status_code}, response: {response.text}, method: {method}, headers: {headers}, data: {data}, json: {json}, params: {params}, cookies: {cookies}, proxies: {proxies}, auth: {auth}, timeout: {timeout}, verify: {verify}")
            return response
        except Exception as e:
            byte_log.error(f"request fail. exception message: {e}")


request_base = RequestBase()
