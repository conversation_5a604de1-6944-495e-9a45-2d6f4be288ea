# https://bits.bytedance.net/devops_open/open_api/list
from proj_settings import settings
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod, request_base
# from utils.common.log import custom_logger


class Bits:
    def __init__(self):
        self.method = RequestMethod.GET.value
        self.url = ""
        self.params = {}
        self.headers = {
            "Authorization": settings.BITS_TOKEN
        }

    def get_mr_list(self, **kwargs):
        """
        获取MR列表
        获取mr列表数据，接口参数与前端请求参数对其， 具体怎么用看平台上的 MR 列表请求传递的参数
        @return:
        """
        self.url = settings.BITS_DOMAIN + "/openapi/merge_request/list"
        self.params = {
            "group_name": "rtcglobal",
            "last_id": 0,
            "order": "desc",
            "source": "all",
            "sort": "mr_create_time",
            "wip": -1,
            "conflicted": -1,
            "review_state": "all"
        }
        for param_key, default_value in self.params.items():
            self.params[param_key] = kwargs[param_key] if param_key in kwargs else default_value
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"bits get_mr_list success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"bits get_mr_list fail. status_code: {res.status_code}")

    def get_mr_info(self, mr_id: int):
        """
        获取MR详情
        @param mr_id:
        @return:
        """
        self.url = settings.BITS_DOMAIN + "/openapi/merge_request/info"
        self.params = {
            "mr_id": mr_id
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"bits get_mr_info success. mr_id: {mr_id}")
            return res.json()
        else:
            byte_log.debug(f"bits get_mr_info fail. status_code: {res.status_code}")

    def get_pipeline_list(self, mr_id: int):
        """
        这里返回的是单独的mr下的所有pipeline数据，如果你的mr是多仓或者多宿主需要先调用GetMrRelation获取所有的mr, 在调用此接口。
        接口中的pipeline_type表示pipeline的类型 0=gitlab, 1=jenkins, 2=viper, 3=bits。目前pipeline详情只支持bits pipeline的数据查询
        @param mr_id:
        @return:
        """
        self.url = settings.BITS_DOMAIN + "/openapi/merge_request/pipeline/list"
        self.params = {
            "mr_id": mr_id
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"bits get_pipeline_list success. mr_id: {mr_id}")
            return res.json()
        else:
            byte_log.debug(f"bits get_pipeline_list fail. status_code: {res.status_code}")

    def get_pipeline_detail(self, pipeline_id: int):
        """
        获取pipeline详情(目前pipeline详情只支持bits pipeline的数据查询， 请求前先确定pipeline类型是否是bits)
        @param pipeline_id:
        @return:
        """
        self.url = settings.BITS_DOMAIN + "/openapi/workflow/pipeline/get"
        self.params = {
            "pipelineId": pipeline_id
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"bits get_pipeline_detail success. pipeline_id: {pipeline_id}")
            return res.json()
        else:
            byte_log.debug(f"bits get_pipeline_detail fail. status_code: {res.status_code}")

    def get_job_detail(self, job_id: int):
        """
        获取pipeline下job的详情
        @param job_id:
        @return:
        """
        self.url = settings.BITS_DOMAIN + "/openapi/workflow/job/get"
        self.params = {
            "jobId": job_id
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"bits get_pipeline_detail success. job_id: {job_id}")
            return res.json()
        else:
            byte_log.debug(f"bits get_pipeline_detail fail. status_code: {res.status_code}")


bits = Bits()

if __name__ == "__main__":
    data = {}
    # print(bits.get_mr_list(**data))
    # print(bits.get_pipeline_list(6697735))
    # print(bits.get_pipeline_detail(69954103))
    # print(bits.get_auto_test_log_data(274632983))
    # print(bits.get_job_detail(302716757))
    print(bits.get_mr_info(6776665))
