import time
import json
from proj_settings import settings
from utils.common.bytelog import byte_log
# from utils.common.log import custom_logger
from utils.common.request_util import RequestMethod, request_base


class LarkRobot:
    def __init__(self):
        self.method = RequestMethod.GET.value
        self.url = ''
        self.params = {}
        self.token = settings.LARK_TOKEN
        self.token_tenant = settings.LARK_TOKEN_TENANT

    def post_tenant_access_token(self, **kwargs):
        """
        获取认证token，并用其为self.token赋值
        @return: 返回token或者None
        """
        method = RequestMethod.POST.value
        url = settings.LARK_DOMAIN + '/open-apis/auth/v3/tenant_access_token/internal'
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        body = {
            'app_id': settings.LARK_APP_ID,
            'app_secret': settings.LARK_APP_SERCERT
        }
        byte_log.debug(f'lark post_tenant_access_token request: url = [{url}], body = {body}')
        res = request_base.send_request(method=method, url=url, json=body, headers=headers)
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.debug(f'lark post_tenant_access_token success. res: {res.json()}')
            self.token = 'Bearer %s' %(res.json()['tenant_access_token'])
            return res.json()['tenant_access_token']
        else:
            byte_log.debug(f'lark get_pipeline_detail fail. status_code: {res.status_code}, res: {res.json()}')
        return None

    def post_open_id(self, user_id_type='open_id', mobiles=[], emails=[], retry_time=3):
        """
        根据用户的手机号or邮箱获取用户id
        @param user_id_type: 获取字段类型，默认'open_id'，还可以取值'union_id'、'user_id'
        @param mobiles: 手机号list
        @param emails: 邮箱list
        @param retry_time: 获取失败的重试次数
        @return: 返回user_list或者None
        """
        method = RequestMethod.POST.value
        url = settings.LARK_DOMAIN + '/open-apis/contact/v3/users/batch_get_id'
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': self.token
        }
        params ={
            'user_id_type': user_id_type
        }
        body = {
            "mobiles": mobiles,
            "emails": emails
        }
        byte_log.info(f'lark post_open_id request: url = [{url}],headers = {headers},params = {params}, body = {body}')
        res = request_base.send_request(method=method, url=url, params=params, json=body, headers=headers)
        byte_log.info(f'lark post_open_id response: status_code: {res.status_code}, res: {res.json()}')
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.info(f'lark post_open_id success.')
            return res.json()['data']['user_list']
        elif res.status_code == 400 and res.json()['code'] == 99991663:
            byte_log.info(f'lark token expired')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.post_tenant_access_token()
                return self.post_open_id(user_id_type, mobiles, emails, retry_time-1)
        else:
            byte_log.error(f'lark post_open_id fail.')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to post_open_id again...')
                return self.post_open_id(user_id_type, mobiles, emails, retry_time-1)
        return None
    def post_robot_card_libra_live_group_message(self,template_id,template_variable,retry_time=3):
        """
        发送飞书消息,群发vod_group_message
        @param text: 消息的文本内容
        @param retry_time: 消息的文本内容
        @return: 是否发送成功
        """
        method = RequestMethod.POST.value
        url_group = settings.LARK_DOMAIN + '/open-apis/im/v1/messages?receive_id_type=chat_id'

        headers_tenant = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': self.token
        }

        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable
            }
        }

        body_tenant_vod = {
            'receive_id': 'oc_04026c95f2e7a08ebb7d1023338aa3b2',
            'msg_type': 'interactive',
            'content': json.dumps(content, ensure_ascii=False)

        }
        body_json = json.dumps(body_tenant_vod, ensure_ascii=False)
        byte_log.info(
            f'lark post_robot_card_libra_live_group_message request: url = [{url_group}],headers = {headers_tenant}, body = {body_json}')
        res = request_base.send_request(method=method, url=url_group, json=body_tenant_vod, headers=headers_tenant)
        byte_log.info(f'lark post_robot_card_libra_live_group_message response: status_code: {res.status_code}, res: {res.json()}')
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.info(f'lark post_robot_card_libra_live_group_message success.')
            return True
        elif res.status_code == 400 and res.json()['code'] == 99991663:
            byte_log.info(f'lark token expired')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.post_tenant_access_token()
                return self.post_robot_card_libra_live_group_message(template_id, template_variable, retry_time - 1)
        else:
            byte_log.error(f'lark post_robot_card_libra_vod_group_message fail.')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to post_robot_card_libra_vod_group_message again...')
                return self.post_robot_card_libra_live_group_message(template_id, template_variable, retry_time - 1)
        return False
    def post_robot_card_libra_vod_group_message(self,template_id,template_variable,retry_time=3):
        """
        发送飞书消息,群发vod_group_message
        @param text: 消息的文本内容
        @param retry_time: 消息的文本内容
        @return: 是否发送成功
        """
        method = RequestMethod.POST.value
        url_group = settings.LARK_DOMAIN + '/open-apis/im/v1/messages?receive_id_type=chat_id'

        headers_tenant = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': self.token
        }

        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable
            }
        }

        body_tenant_vod = {
            'receive_id': 'oc_6a4d716f1d2c723c56021b8b522efe27',
            'msg_type': 'interactive',
            'content': json.dumps(content, ensure_ascii=False)

        }
        body_json = json.dumps(body_tenant_vod, ensure_ascii=False)
        byte_log.info(
            f'lark post_robot_card_libra_vod_group_message request: url = [{url_group}],headers = {headers_tenant}, body = {body_json}')
        res = request_base.send_request(method=method, url=url_group, json=body_tenant_vod, headers=headers_tenant)
        byte_log.info(f'lark post_robot_card_libra_vod_group_message response: status_code: {res.status_code}, res: {res.json()}')
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.info(f'lark post_robot_card_libra_vod_group_message success.')
            return True
        elif res.status_code == 400 and res.json()['code'] == 99991663:
            byte_log.info(f'lark token expired')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.post_tenant_access_token()
                return self.post_robot_card_libra_vod_group_message(template_id, template_variable, retry_time - 1)
        else:
            byte_log.error(f'lark post_robot_card_libra_vod_group_message fail.')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to post_robot_card_libra_vod_group_message again...')
                return self.post_robot_card_libra_vod_group_message(template_id, template_variable, retry_time - 1)
        return False
    def post_robot_card_message(self, open_id, template_id, template_variable, retry_time=3):
        """
        发送飞书消息
        @param open_id: 接收消息的用户的open_id，获取方式见post_open_id方法
        @param text: 消息的文本内容
        @param retry_time: 消息的文本内容
        @return: 是否发送成功
        """
        method = RequestMethod.POST.value
        url = settings.LARK_DOMAIN + '/open-apis/im/v1/messages'
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': self.token
        }
        params = {
            'receive_id_type': 'open_id'
        }
        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable
            }
        }
        body = {
            'receive_id': open_id,
            'msg_type': 'interactive',
            'content': json.dumps(content, ensure_ascii=False)

        }
        body_json = json.dumps(body, ensure_ascii=False)
        byte_log.info(f'lark post_robot_card_message request: url = [{url}],headers = {headers},params = {params}, body = {body_json}')
        res = request_base.send_request(method=method, url=url,params=params, json=body, headers=headers)
        byte_log.info(f'lark post_robot_card_message response: status_code: {res.status_code}, res: {res.json()}')
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.info(f'lark post_robot_card_message success.')
            return True
        elif res.status_code == 400 and res.json()['code'] == 99991663:
            byte_log.info(f'lark token expired')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.post_tenant_access_token()
                return self.post_robot_card_message(open_id, template_id, template_variable, retry_time-1)
        else:
            byte_log.error(f'lark post_robot_card_message fail.')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to post_robot_card_message again...')
                return self.post_robot_card_message(open_id, template_id, template_variable, retry_time - 1)
        return False

    def batch_post_robot_card_message(self, open_ids, template_id, template_variable, retry_time=3):
        """
        批量发送飞书消息
        @param open_ids: 接收消息的用户的open_ids，list，获取方式见post_open_id方法
        @param text: 消息的文本内容
        @param retry_time: 消息的文本内容
        @return: 是否发送成功
        """
        for open_id in open_ids:
            self.post_robot_card_message(open_id, template_id, template_variable, retry_time)

    def post_robot_text_message(self, open_id, text, retry_time=3):
        """
        发送飞书消息
        @param open_id: 接收消息的用户的open_id，获取方式见post_open_id方法
        @param text: 消息的文本内容
        @param retry_time: 消息的文本内容
        @return: 是否发送成功
        """
        method = RequestMethod.POST.value
        url = settings.LARK_DOMAIN + '/open-apis/im/v1/messages'
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': self.token
        }
        params = {
            'receive_id_type': 'open_id'
        }
        body = {
            'receive_id': open_id,
            'msg_type': 'text',
            'content': '{"text":"%s"}' % text,

        }
        byte_log.debug(f'lark post_robot_text_message request: url = [{url}],headers = {headers},params = {params}, body = {body}')
        res = request_base.send_request(method=method, url=url,params=params, json=body, headers=headers)
        byte_log.debug(f'lark post_robot_text_message response: status_code: {res.status_code}, res: {res.json()}')
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.debug(f'lark post_robot_text_message success.')
            return True
        elif res.status_code == 400 and res.json()['code'] == 99991663:
            byte_log.debug(f'lark token expired')
            if retry_time > 0:
                time.sleep(1)
                byte_log.debug(f'try to fetch token...')
                self.post_tenant_access_token()
                return self.post_robot_text_message(open_id, text, retry_time-1)
        else:
            byte_log.error(f'lark post_robot_text_message fail.')
            if retry_time > 0:
                time.sleep(1)
                byte_log.debug(f'try to post_robot_text_message again...')
                return self.post_robot_text_message(open_id, text, retry_time - 1)
        return False

    def get_user_email_by_open_id(self, open_id, retry_time=3):
        """
        根据用户的open_id获取用户邮箱信息
        @param retry_time: 获取失败的重试次数
        @return: 返回user_list或者None
        """
        method = RequestMethod.GET.value
        url = settings.LARK_DOMAIN + f'/open-apis/contact/v3/users/{open_id}'
        headers = {
            'Authorization': self.token
        }
        params = {
            'user_id_type': 'open_id',
            'department_id_type': 'open_department_id'
        }
        byte_log.info(
            f'lark get_user_email_by_open_id request: url = [{url}],headers = {headers}')
        res = request_base.send_request(method=method, url=url, headers=headers)
        byte_log.info(f'lark get_user_email_by_open_id response: status_code: {res.status_code}, res: {res.json()}')
        if res.status_code == 200 and res.json()['code'] == 0:
            byte_log.info(f'lark get_user_email_by_open_id success.')
            return res.json()['data']['user']['email']
        elif res.status_code == 400 and res.json()['code'] == 99991663:
            byte_log.info(f'lark token expired')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.post_tenant_access_token()
                return self.get_user_email_by_open_id(open_id, retry_time - 1)
        else:
            byte_log.error(f'lark get_user_email_by_open_id fail.')
            if retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to get_user_email_by_open_id again...')
                return self.get_user_email_by_open_id(open_id, retry_time - 1)
        return None


larkRobot = LarkRobot()

if __name__ == '__main__':
    template_variable= {
        "todo_checklist":[
            {
                "desc": "创建接口变更内容文档",
                "update_info": {
                    "comment": "",
                    "id": 1
                }
            },
            {
                "desc": "创建催bug机器人",
                "update_info": {
                    "comment": "",
                    "id": 2
                }
            },
            {
                "desc": "创建测试计划文档、meego Epic、meego测试需求，并在版本群/QA群周知",
                "update_info": {
                    "comment": "",
                    "id": 3
                }
            }
        ],
        "version": "6.15.0",
        "date": "2024/6/17"
    }
    # print(larkRobot.post_tenant_access_token())
    open_id = larkRobot.post_open_id(emails=['<EMAIL>'])[0]['user_id']
    print(open_id)
    if open_id:
        print(larkRobot.post_robot_card_message(open_id=open_id, template_id=settings.LARK_CHECKLIST_CARD_ID, template_variable=template_variable, retry_time=3))
        # print(larkRobot.post_robot_text_message(open_id=open_id, text='context1'))

    # # get_user_email_by_open_id
    # open_id = 'ou_ba5d0b4dc028c3ce77ae40a228bba0fc'
    # result = larkRobot.get_user_email_by_open_id(open_id)
    # print(result)

