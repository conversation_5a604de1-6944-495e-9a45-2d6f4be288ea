from proj_settings import settings
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod, request_base

class Libra:
    def __init__(self):
        self.app_id = settings.LIBRA_APP_ID
        self.app_secret = settings.LIBRA_APP_SECRET
        self.headers = {
            "X-Real-Operator": settings.OPERATOR_NAME,
            "Content-Type": "application/json; charset=utf-8"
        }
        
        # 尝试获取authorization，如果失败则使用空字符串
        try:
            self.authorization = self.get_authorization()
            if not self.authorization:
                byte_log.warning("Failed to get Libra authorization during initialization, will retry on next API call")
        except Exception as e:
            byte_log.error(f"Exception during Libra initialization: {str(e)}")
            self.authorization = None

    def get_authorization(self):
        """
        获取libra的authorization
        @return: authorization
        """
        try:
            data = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }
            
            # 增加重试次数和超时设置
            max_retries = 3
            timeout_seconds = 10
            
            for attempt in range(max_retries):
                try:
                    res = request_base.send_request(
                        url=settings.LIBRA_DOMAIN + "/dataopen/open-apis/v1/authorization",
                        method=RequestMethod.POST.value,
                        json=data,
                        headers=self.headers,
                        timeout=timeout_seconds
                    )
                    
                    if res.status_code == 200:
                        result = res.json()
                        token = result.get("data", {}).get("access_token")
                        if token:
                            self.headers["Authorization"] = token
                            byte_log.debug(f"libra get_authorization success. token: {token}")
                            return token
                        else:
                            byte_log.error(f"libra get_authorization fail. response: {result}")
                    else:
                        byte_log.error(f"libra get_authorization fail. status_code: {res.status_code}")
                    
                    # 如果不是最后一次尝试，则等待一段时间再重试
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)  # 等待1秒后重试
                        byte_log.info(f"Retrying authorization, attempt {attempt + 2}/{max_retries}")
                
                except Exception as e:
                    byte_log.error(f"Exception during authorization attempt {attempt + 1}: {str(e)}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)
            
            return None
        except Exception as e:
            byte_log.error(f"Fatal error in get_authorization: {str(e)}")
            return None

    def view_flight_by_vid(self, vid):
        """
        根据vid查询实验信息
        @param vid: 实验vid
        @return: 实验信息
        """
        # 如果没有authorization，尝试重新获取
        if not self.authorization:
            self.authorization = self.get_authorization()
            
        url = settings.LIBRA_DOMAIN + "/dataopen/open-apis/libra/openapi/v1/open/flight/view_by_vid/"
        params = {"vid": vid}
        res = request_base.send_request(url=url, method=RequestMethod.GET.value, params=params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"libra view_flight_by_vid success. vid: {vid}")
            return res.json()
        else:
            byte_log.error(f"libra view_flight_by_vid fail. status_code: {res.status_code}, vid: {vid}")
            return None

libra = Libra()

if __name__ == "__main__":
    vid = 73242334
    result = libra.view_flight_by_vid(vid)
    print(result)
