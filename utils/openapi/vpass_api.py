from proj_settings import settings
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod, request_base


class Vpass:
    def __init__(self):
        self.url = ""
        self.method = RequestMethod.GET.value
        self.params = {}
        self.headers = {
            "x-vpaas-operator-name": settings.OPERATOR_NAME,
            "x-vpaas-operator-email": settings.OPERATOR_EMAIL,
            "project": "rtcglobal",
        }

    def get_task_list(self, **kwargs):
        self.url = settings.VPASS_DOMAIN + "/api/caseplatform/v1/task/list"
        self.params = {
            "current": 1,
            "pageSize": 10,
            "filter": "{}"
        }
        for param_key, default_value in self.params.items():
            self.params[param_key] = kwargs[param_key] if param_key in kwargs else default_value
        res = request_base.send_request(url=self.url, method=self.method, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"vpass get_task_list success. params: {self.params}")
            return res.json()
        else:
            byte_log.error(f"vpass get_task_list fail. status_code: {res.status_code}")


vpass = Vpass()

if __name__ == "__main__":
    print(vpass.get_task_list())
