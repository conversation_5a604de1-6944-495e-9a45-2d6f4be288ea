import proj_settings
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod, request_base
# from utils.common.log import custom_logger


class GamePerf:
    def __init__(self):
        self.method = RequestMethod.GET.value
        self.url = ""
        self.params = {}
        self.headers = {}
        self.ws_message = None

    def get_all_devices(self):
        """
        所有已连接设备的信息
        @return: 返回当前所有已连接设备的信息
        {"getAllDevices": True, "data": [{"udid": "PQY0221927004066", "type": "ANDROID",
        "connectType": "USB", "baseInfo": {"deviceName": "NOH-AN00", "ipAddr": "", "port": "", "isIOS17": False},
        "deviceName": "NOH-AN00", "deviceInfo": {}}, {"udid": "PQY0221927004066", "type": "ANDROID", "connectType":
        "NETWORK", "baseInfo": {"deviceName": "NOH-AN00", "ipAddr": "", "port": "", "isIOS17": False}, "deviceName":
        "NOH-AN00"}, {"udid": "JC9DVWY9P3", "type": "MACOS", "connectType": "USB", "baseInfo": {"deviceName":
        "贺加贝Mac", "ipAddr": "", "port": "", "isIOS17": False}, "deviceName": "贺加贝Mac"}]}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/getAllDevices"
        print(self.url)
        self.params = {}
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf get_all_devices success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf get_all_devices fail. status_code: {res.status_code}")

    def connect_device(self, udid: str, connect_type: str):
        """
        连接设备
        @param udid: 通过获取设备接口拿到的设备的udid值
        @param connect_type: USB/NETWORK
        @return: 返回所连接设备的基础信息
        {"connectDevice": True, "data": {"CPUInfo": "kirin9000",
        "DeviceSystem": "Android 12", "OpenGL": "OpenGL ES 3.2 v1.r34p0-01eac0.a1b116bd871d46ef040e8feef9ed691e",
        "DeviceBrand": "HUAWEI", "CPUArch": "arm64-v8a", "Vulkan": "1.1.191", "SerialNumber": "PQY0221927004066",
        "GPUInfo": "Mali-G78", "CPUMaxFreq": "2045000, 2045000, 2045000, 2045000, 2544000, 2544000, 2544000,
        3130000", "DeviceName": "NOH-AN00", "CPUCoreNum": "8", "Ram": "7.3137054 GB", "Resolution": "1344x2772"}}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/connectDevice"
        self.params = {
            "udid": udid,
            "connectType": connect_type
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf connect_device success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf connect_device fail. status_code: {res.status_code}")

    def get_all_apps(self):
        """
        获取应用(注意：必须在连接设备之后进行操作)
        @return: 获取当前已连接设备的所有应用,其中 type 用来标识用户应用，系统应用以及系统进程。
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/getAllApps"
        self.params = {}
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf get_all_apps success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf get_all_apps fail. status_code: {res.status_code}")

    def regain_all_apps(self):
        """
        刷新应用列表(注意：必须在连接设备之后进行操作)
        @return: 获取当前已连接设备的所有应用,其中 type 用来标识用户应用，系统应用以及系统进程。
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/regainAllApps"
        self.params = {}
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf regain_all_apps success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf regain_all_apps fail. status_code: {res.status_code}")

    def set_scene(self, scene_name: str):
        """
        场景标记
        @param scene_name: 场景名
        @return: 返回是否成功或者错误信息
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/setScene"
        self.params = {
            "sceneName": scene_name
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf set_scene success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf set_scene fail. status_code: {res.status_code}")

    def start_record(self):
        """
        开始记录
        @return: 返回是否成功或者错误信息
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/startRecord"
        self.params = {}
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf start_record success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf start_record fail. status_code: {res.status_code}")

    def stop_record(self, file_export: int, file_upload: int):
        """
        停止记录
        @param file_export: 是否导出，取值为0,1，用来标识是否导出
        @param file_upload: 是否上传，取值为0,1，用来标识是否上传
        @return: 返回是否成功或者错误信息
        {"stopRecord":true,"data":{"fileExport":true,"fileUpload":true, url:url, path:path}}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/stopRecord"
        self.params = {
            "fileExport": file_export,
            "fileUpload": file_upload
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf stop_record success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf stop_record fail. status_code: {res.status_code}")

    def sub_scene_start(self, sub_scene_name: str):
        """
        子场景开始
        @param sub_scene_name: 子场景命名
        @return: 返回是否成功或者错误信息
        {"SubSceneStart":true}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/SubSceneStart"
        self.params = {
            "subSceneName": sub_scene_name,
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf sub_scene_start success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf sub_scene_start fail. status_code: {res.status_code}")

    def sub_scene_end(self):
        """
        子场景结束
        @return: 返回是否成功或者错误信息
        {"SubSceneEnd":true}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/SubSceneEnd"
        self.params = {}
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf sub_scene_end success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf sub_scene_end fail. status_code: {res.status_code}")

    def rename_sub_scene(self, start_time, sub_scene_name):
        """
        子场景重命名
        @param start_time: 子场景开始时间，相对开始测试时间的秒数，与每条数据附带的对应时间对应
        @param sub_scene_name: 新命名
        @return: 返回是否成功或者错误信息
        {"renameSubScene":true}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/renameSubScene"
        self.params = {
            "startTime": start_time,
            "subSceneName": sub_scene_name,
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf rename_sub_scene success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf rename_sub_scene fail. status_code: {res.status_code}")

    def remove_sub_scene(self, start_time):
        """
        子场景删除
        @param start_time: 子场景开始时间，相对开始测试时间的秒数，与每条数据附带的对应时间对应
        @return: 返回是否成功或者错误信息
        {"removeSubScene":true}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/removeSubScene"
        self.params = {
            "startTime": start_time,
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf remove_sub_scene success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf remove_sub_scene fail. status_code: {res.status_code}")

    def get_screen_shot_once(self):
        """
        单次截图
        @return: 返回是否成功以及相应图片路径或者错误信息
        {"getScreenShotOnce":true,"data":"/Users/<USER>/Desktop/GamePerf/tmp/tmp3/screenshots/3.png"}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/getScreenShotOnce"
        self.params = {}
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf get_screen_shot_once success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf get_screen_shot_once fail. status_code: {res.status_code}")

    def upload_data(self, json_path):
        """
        上传数据
        @param json_path: json数据的路径
        @return: 返回是否成功或者错误信息
        {"uploadFile":true}
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/uploadData"
        self.params = {
            "jsonPath": json_path
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf upload_data success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf upload_data fail. status_code: {res.status_code}")

    def share_report(self, scene_id, invalid_time):
        """
        数据分享
        @param scene_id: 通过上传报告时所得(注意：分享的报告必须自身拥有权限，否则无法分享)
        @param invalid_time: 表示分享过期时间，单位是【天】
        @return:
        """
        self.url = proj_settings.settings.GAME_PERF_DOMAIN + "/shareReport"
        self.params = {
            "sceneId": scene_id,
            "invalidTime": invalid_time,
        }
        res = request_base.send_request(method=self.method, url=self.url, params=self.params, headers=self.headers)
        if res.status_code == 200:
            byte_log.debug(f"game_perf share_report success. params: {self.params}")
            return res.json()
        else:
            byte_log.debug(f"game_perf share_report fail. status_code: {res.status_code}")


game_perf = GamePerf()

if __name__ == "__main__":
    print(game_perf.get_all_devices())
    # print(game_perf.connect_device("602ad3df0407", "NETWORK"))
    # print(game_perf.get_all_apps())
    # print(game_perf.set_scene("TikTok第一次测试api"))
