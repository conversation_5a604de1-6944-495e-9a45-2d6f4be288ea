from proj_settings import settings
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod, request_base
# from utils.common.log import custom_logger


class VQoSTrace:
    def __init__(self):
        self.method = RequestMethod.POST.value
        self.url = ""
        self.params = {}
        self.json = {}
        self.headers = {
            "specialData": "true"
        }

    def get_trace(self, namespace: str, start: int, end: int, offset: int, limit: int,
                  query_filter: list, query_string: str):
        """
        视频架构VQoS Trace 查询示例
        注意
        Trace接口对外提供的查询QPS非常有限，使用前务必与@孙佳豪 沟通使用目的与QPS。
        QPS请保持小于等于1
        查询时请务必带上Owner字段
        请不要使用  event_key:*xxxx  类似的模糊查询
        @param namespace:
        @param start:
        @param end:
        @param offset:
        @param limit:
        @param query_filter:
        @param query_string:
        @return:
        """
        self.url = settings.TRACE_DOMAIN + "/live_trace/v1/mget"
        self.json = {
            "Namespace": namespace,
            "Start": start,
            "End": end,
            "Offset": offset,
            "Limit": limit,
            "Filter": query_filter,
            "QueryString": query_string,
            "Owner": settings.OPERATOR_NAME,
        }
        res = request_base.send_request(method=self.method, url=self.url, headers=self.headers, json=self.json)
        byte_log.debug(f"vqos_trace get_trace success. data: {self.json}")
        return res.json()


vqos_trace = VQoSTrace()

if __name__ == '__main__':
    live_trace_data = vqos_trace.get_trace(
        namespace="live-trace",
        start=1723548346000,
        end=1723548712000,
        offset=0,
        limit=200,
        query_filter=[],
        query_string="user_id: 7327133310998938642 AND rtc_channel_id: ?*"
    )
    live_result_list = live_trace_data["Result"]["List"]
    rtc_channel_id = live_result_list[0]["rtc_channel_id"]
    print(rtc_channel_id)