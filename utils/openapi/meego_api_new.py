# https://meego.larkoffice.com/openapp/help/category/7077868019283787778
import json
import re
import time

from defines.meego_api_define import *
from proj_settings import settings
from utils.common.bytelog import byte_log
from utils.common.request_util import RequestMethod, request_base


class Meego:
    def __init__(self):
        """

        @param _project_key:
        @param _bytedance_email:
        """
        self.plugin_id = settings.MEEGO_PLUGIN_ID
        self.plugin_secret = settings.MEEGO_PLUGIN_SECRET
        self.domain = settings.MEEGO_DOMAIN
        self.plugin_token = None
        self.user_key = settings.MEEGO_MY_USER_KEY
        self.headers = {
            "Content-Type": "application/json",
            "X-USER-KEY": self.user_key
        }
        self.get_plugin_token()

    def get_plugin_token(self, token_type=TOKEN_TYPE_FORMAL):
        """
        获取plugin_token
        @param token_type: 0为plugin_token，1为虚拟plugin_token
        @return:
        """
        url = self.domain + "/open_api/authen/plugin_token"
        method = RequestMethod.POST.value
        data = json.dumps(
            {
                "plugin_id": self.plugin_id,
                "plugin_secret": self.plugin_secret,
                "type": token_type
            }
        )
        response = request_base.send_request(url=url, method=method, headers=self.headers,
                                             data=data).json()
        if "token" in str(response):
            byte_log.info("get_plugin_token response success, response=%s" % response)
            self.plugin_token = response["data"]["token"]
            self.headers["X-Plugin-Token"] = self.plugin_token
        else:
            byte_log.error("get_plugin_token response fail, response=%s" % response)


    def is_user_key(self, str):
        """
            判断str是否是user_key的格式，user_key格式示例 7310035728690495489
            @param str: 待确认的string
        """
        result = bool(re.fullmatch(r'\d+', str)) # 全部是数字
        return result
    def get_user_details_by_user_keys(self, user_key_list, retry_time=3):
        """
            获取用户信息详情
            @param user_key_list: 用户key的list，如: ["7310035728690495489"]
            @return: list
        """
        url = f"{self.domain}/open_api/user/query"
        method = RequestMethod.POST.value
        data = json.dumps(
            {
                "user_keys": user_key_list
            }
        )
        response = request_base.send_request(url=url, method=method, headers=self.headers,
                                             data=data).json()
        print(response)
        if response["err_code"] != 0 or not ("data" in response):
            byte_log.error("get_user_details_by_user_keys response fail, err_code=%d, err_msg=%s" %(response["err_code"],response["err_msg"]))
            if response["err_code"] in ERR_CODE_LIST_TOKEN_ERR and retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.get_plugin_token()
                byte_log.warning(f'retry get_user_details_by_user_keys...')
                return self.get_user_details_by_user_keys(user_key_list, retry_time - 1)
            return None
        else:
            byte_log.info("get_user_details_by_user_keys response success, response=%s" % response)
            return response["data"]

    def get_feature_workflow_detail(self, project_key, work_item_id, retry_time=3):
        """
            获取需求工作项详情
            @param project_key: 项目的key，如bytertc_oc
            @param work_item_id: 工作项id
            @return:
        """
        url = f"{self.domain}/open_api/{project_key}/work_item/story/{work_item_id}/workflow/query"
        method = RequestMethod.POST.value
        data = json.dumps(
            {
                "flow_type": 0  # 工作流类型，0:节点流，1:状态流
            }
        )
        response = request_base.send_request(url=url, method=method, headers=self.headers,
                                             data=data).json()
        print(response)
        if response["err_code"] != 0 or not ("data" in response):
            byte_log.error("get_feature_workflow_detail response fail, err_code=%d, err_msg=%s" %(response["err_code"],response["err_msg"]))
            if response["err_code"] in ERR_CODE_LIST_TOKEN_ERR and retry_time > 0:
                time.sleep(1)
                byte_log.warning(f'try to fetch token...')
                self.get_plugin_token()
                byte_log.warning(f'retry get_feature_workflow_detail...')
                return self.get_feature_workflow_detail(project_key, work_item_id, retry_time - 1)
            return None
        else:
            byte_log.info("get_feature_workflow_detail response success, response=%s" % response)
            return response["data"]

meego = Meego()


if __name__ == "__main__":
    project_key = "bytertc_oc"
    work_item_id = 5341819919
    meego.get_feature_workflow_detail(project_key, work_item_id)

