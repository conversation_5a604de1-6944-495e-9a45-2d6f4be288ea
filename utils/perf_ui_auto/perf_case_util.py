import importlib.util
import inspect
import os
import subprocess

from defines.perf_ui_auto_define import PERF_CASE_REPO_URL
from utils.common.bytelog import byte_log
from proj_settings import settings


class PerfCaseUtil:
    def __init__(self):
        pass

    def pull_from_git(self, repo_url: str, branch: str = 'master', target_dir: str = '.') -> None:
        """
        从指定的Git仓库拉取代码。
        """
        repo_name = repo_url.split('/')[-1].split('.git')[0]
        repo_path = os.path.join(target_dir, repo_name)
        if os.path.exists(repo_path):
            self.update_from_git(repo_path, branch)
        else:
            self.clone_from_git(repo_url, branch, target_dir)

    def clone_from_git(self, repo_url: str, branch: str = 'master', target_dir: str = '.') -> None:
        """
        从指定的Git仓库克隆代码。

        :param repo_url: Git仓库的URL
        :param branch: 要拉取的分支，默认为'master'
        :param target_dir: 拉取代码的目标目录，默认为当前目录
        """
        if not os.path.exists(target_dir) or not os.path.isdir(target_dir):
            byte_log.error(f"目标目录 {target_dir} 不存在或不是一个有效的目录")
            return

        cmd = ['git', 'clone', '-b', branch, repo_url]
        try:
            os.chdir(target_dir)
            result = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            if result.returncode != 0:
                byte_log.error(f"拉取代码失败: {result.stderr}")
            byte_log.info(f"成功从 {repo_url} 拉取代码到 {target_dir}")
        except subprocess.CalledProcessError as e:
            if "403" in e.stderr:
                byte_log.error(
                    f"拉取代码失败: 远程仓库禁止从PC上克隆代码，请使用Cloud IDE进行开发。详情见文档: {e.stderr}")
            else:
                byte_log.error(f"拉取代码失败: {e.stderr}")
        finally:
            os.chdir(os.getcwd())  # 切换回原来的目录

    def update_from_git(self, repo_path: str, branch: str = 'master') -> None:
        """
        更新指定目录中的Git仓库代码。

        :param repo_path: 本地Git仓库的路径
        :param branch: 要更新的分支，默认为'master'
        """
        try:
            # 切换到仓库目录
            os.chdir(repo_path)
            # 拉取最新代码
            cmd_fetch = ['git', 'fetch']
            cmd_checkout = ['git', 'checkout', branch]
            cmd_pull = ['git', 'pull']
            fetch_result = subprocess.run(cmd_fetch, shell=False, capture_output=True, text=True, check=True)
            checkout_result = subprocess.run(cmd_checkout, shell=False, capture_output=True, text=True, check=True)
            pull_result = subprocess.run(cmd_pull, shell=False, capture_output=True, text=True, check=True)
            if fetch_result.returncode != 0:
                byte_log.error(f"拉取最新代码失败: {fetch_result.stderr}")
            if checkout_result.returncode != 0:
                byte_log.error(f"切换分支失败: {checkout_result.stderr}")
            if pull_result.returncode != 0:
                byte_log.error(f"拉取最新提交失败: {pull_result.stderr}")
            byte_log.info(f"成功更新 {repo_path} 中的代码")
        except subprocess.CalledProcessError as e:
            byte_log.error(f"更新代码失败: {e.stderr}")

    def fetch_cases_from_directory(self, dir_path: str) -> list:
        """
        从指定的目录中拉取性能测试用例。

        :param dir_path: 包含测试用例的目录路径
        :return: 测试用例列表
        """
        cases = []
        try:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                for root, dirs, files in os.walk(dir_path):
                    # 排除 __pycache__ 目录
                    dirs[:] = [d for d in dirs if d != '__pycache__']
                    for file in files:
                        if file.endswith('.py') and file != '__init__.py':
                            module_path = os.path.join(root, file)
                            module_name = os.path.splitext(file)[0]
                            spec = importlib.util.spec_from_file_location(module_name, module_path)
                            module = importlib.util.module_from_spec(spec)
                            spec.loader.exec_module(module)

                            for name, obj in inspect.getmembers(module, inspect.isclass):
                                if (inspect.isclass(obj) and
                                        # issubclass(obj, TTAndroidPerfAppBase) and
                                        hasattr(obj, 'run_test') and
                                        hasattr(obj, '__wrapped__') and
                                        getattr(obj.__wrapped__, '__name__', None) == 'DataDriven'):
                                    cases.append((module_path, name))
                                    byte_log.debug(f"找到测试用例: {module_path} 中的 {name}")

                byte_log.info(f"从 {dir_path} 目录中找到 {len(cases)} 个测试用例")
            else:
                byte_log.error(f"目录 {dir_path} 不存在或不是一个有效的目录")
        except ValueError as e:
            byte_log.error(f"目录 {dir_path} 无效: {e}")
        except Exception as e:
            byte_log.error(f"加载测试用例时发生错误: {e}")
        return cases
            
if __name__ == '__main__':
    perf_case_util = PerfCaseUtil()
    target_dir = os.path.join(settings.BASEDIR, 'temp')
    # perf_case_util.pull_from_git(PERF_CASE_REPO_URL, target_dir=target_dir)
    dir_path = os.path.join(target_dir, 'global_business_perf', 'business')
    perf_case_util.fetch_cases_from_directory(dir_path)