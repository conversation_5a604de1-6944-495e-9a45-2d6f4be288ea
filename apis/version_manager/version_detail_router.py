from fastapi import APIRouter
from typing import Any, Optional
from dals.db.version_manager.version_manager_detail_db import version_manager_detail_db
from defines.lark_robot_define import LARK_EMAIL_SUFFIX
from handlers.version_manager import version_manager_detail_handler
from schemas.request.version_manager.version_manager_detail_req import *
from schemas.request.lark_rebot_card_callback_req import *
from schemas.response.version_manager.version_manager_detail_res import *
from utils.common.bytelog import byte_log
# from utils.common.log import custom_logger
from utils.common.response import *
from proj_settings import settings
from utils.openapi.lark_robot_api import larkRobot
from proj_settings import deploy_cluster_env, DeployCluster
from utils.common import time_util

router = APIRouter()

@router.get("/version_detail", response_model=HttpResponse[VersionDetailRes], summary="获取版本详情")
async def get_version_detail(version_id: int):
    item = version_manager_detail_db.get_version_detail_by_id(version_id=version_id)
    stage_list = version_manager_detail_db.get_stages_by_version_id(version_id=version_id)
    current_stage = version_manager_detail_db.get_current_stage_by_version_id(version_id=version_id)
    data = version_manager_detail_handler.get_detail_data(item=item, stage_list=stage_list, current_stage=current_stage)
    return response_success(data=data)

@router.get("/version_checklist", response_model=HttpResponse[VersionChecklistDetailListRes], summary="获取版本checklist")
async def get_version_checklist(version_id: int, stage: int):
    _, items = version_manager_detail_db.get_version_checklist_by_version_id_and_stage(version_id=version_id, stage=stage)
    data = version_manager_detail_handler.get_version_checklist_data(items=items)
    return response_success(data=data)

@router.put("/update_version_checklist", response_model=HttpResponse[Optional[VersionChecklistDetailItemRes]], summary="更新版本checklist")
async def update_version_checklist(params: UpdateVersionChecklistParams):
    code, msg, item = version_manager_detail_db.update_version_checklist_by_version_checklist_id(params)
    if not item:
        return response_fail(code=code, msg=msg)
    data = version_manager_detail_handler.update_version_checklist(item=item)
    return response_success(data=data)

@router.post("/checklist_card_callback", response_model=Any, summary="用户点击消息提醒卡片按钮的回调")
async def checklist_card_callback(params: CallbackParams):
    byte_log.info(f'checklist_card_callback params = {params}')
    if params.challenge is not None:
        res = version_manager_detail_handler.test_callback(params)
        return res
    else:
        # 读出params里的用户的open id，获取其email
        email = larkRobot.get_user_email_by_open_id(params.event.operator.open_id)
        if email is None:
            return version_manager_detail_handler.card_callback("error", "没有找到对应的用户email", "user email not found")
        else:
            email_prefix = email[0:email.find("@")] if email.find("@") > -1 else email
            updateParamsDict = {
                "id":params.event.action.value.get('id'),
                "is_finished":True,
                "comment":params.event.action.value.get('comment'),
                "finish_time":time_util.get_current_time(),
                "operator":email_prefix
            }
            updateParams = UpdateVersionChecklistParams(**updateParamsDict)
            code, msg, item = version_manager_detail_db.update_version_checklist_by_version_checklist_id(updateParams)
            if not item:
                return version_manager_detail_handler.card_callback("error", msg, msg)
            else:
                return version_manager_detail_handler.card_callback("success", "更新成功", "update success")


def get_todo_version_checlist_scheduler_task():
    results = version_manager_detail_db.get_todo_checklist_before_today()
    todo_checklist_list = version_manager_detail_handler.get_todo_checklist_before_today(results)
    todo_checklist_card_variable_map = version_manager_detail_handler.get_todo_checklist_card_variable_map(todo_checklist_list)

    # 额外需要通知的人的openid
    extra_reminder_open_id_list = [item['user_id'] for item in larkRobot.post_open_id(emails=settings.VERSION_CHECKLIST_EXTRA_REMINDER_EMAIL_LIST)]
    # 测试环境通知的人的openid
    developer_reminder_open_id_list = [item['user_id'] for item in larkRobot.post_open_id(emails=settings.VERSION_CHECKLIST_DEVELOPER_REMINDER_EMAIL_LIST)]

    # 发送提醒
    for reminder,reminder_variable_item in todo_checklist_card_variable_map.items():
        reminder_email = reminder + LARK_EMAIL_SUFFIX
        byte_log.info(f"reminder_email={reminder_email}")
        reminder_open_id = larkRobot.post_open_id(emails=[reminder_email])[0]['user_id']
        reminder_email_list = [reminder_email]
        reminder_open_id_list = [reminder_open_id]

        # 额外需要通知的人，追加
        reminder_email_list.extend(settings.VERSION_CHECKLIST_EXTRA_REMINDER_EMAIL_LIST)
        reminder_open_id_list.extend(extra_reminder_open_id_list)
        # 测试环境通知的人，替换
        if deploy_cluster_env not in [DeployCluster.CN_ONLINE]:
            reminder_email_list = settings.VERSION_CHECKLIST_DEVELOPER_REMINDER_EMAIL_LIST
            reminder_open_id_list = developer_reminder_open_id_list

        byte_log.info(f"reminder_email_list={reminder_email_list}")
        byte_log.info(f"reminder_open_id_list={reminder_open_id_list}")
        if reminder_open_id:
            for version,variable_item in reminder_variable_item.items():
                variable_item["user_open_id"] = reminder_open_id
                # 测试环境通知的信息
                if deploy_cluster_env not in [DeployCluster.CN_ONLINE]:
                    variable_item["date"] = variable_item["date"] + ' 【boe】'
                byte_log.info("get_todo_version_checlist_scheduler_task version=%s variable_item=%s " %(version,variable_item))
                for open_id_item in reminder_open_id_list:
                    byte_log.info("get_todo_version_checlist_scheduler_task open_id_item=%s" % open_id_item)
                    result = larkRobot.post_robot_card_message(open_id=open_id_item, template_id=settings.LARK_CHECKLIST_CARD_ID,
                                                        template_variable=variable_item, retry_time=3)
                    if not result:
                        byte_log.error(
                            "get_todo_version_checlist_scheduler_task reminder=%s version=%s send lark msg fail" % (reminder_email,version))
        else:
            byte_log.error("get_todo_version_checlist_scheduler_task reminder=%s get open_id fail, cannot send lark msg" % reminder_email)


if __name__ == '__main__':
    get_todo_version_checlist_scheduler_task()