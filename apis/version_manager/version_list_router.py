from fastapi import APIRouter

from dals.db.version_manager.version_manager_list_db import version_manager_list_db
from handlers.version_manager import version_manager_list_handler
from schemas.response.version_manager.version_manager_list_res import *
from utils.common.depends import PageQuery
from utils.common.response import *

router = APIRouter()


@router.get("/version_list", response_model=HttpResponse[VersionListRes], summary="获取版本列表")
async def get_version_list(page: PageQuery):
    total, items = version_manager_list_db.get_list(page=page)
    data = version_manager_list_handler.get_list_data(page=page, total=total, items=items)
    return response_success(data=data)