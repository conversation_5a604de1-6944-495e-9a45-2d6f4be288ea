from fastapi import BackgroundTasks,APIRouter

from dals.db.libra_experiment.libra_experiment_review_db import *
from dals.db.libra_experiment.libra_experiment_field_review_rule_db import *
from handlers.libra_experiment import libra_experiment_review_handler
from schemas.request.libra_experiment.libra_experiment_review_req import *
from schemas.response.libra_experiment.libra_experiment_review_res import *
from service.libra_experiment import libra_experiment_review_service
from utils.common.bytelog import byte_log
from utils.common.response import *


router = APIRouter()


@router.put("/add_experiment", response_model=HttpResponse[Union[LibraExperimentRes,None]], summary="添加实验数据")
async def add_experiment(params: AddLibraExperimentParams, background_tasks: BackgroundTasks):
    # byte_log.info("add_experiment")
    item = libra_experiment_review_db.insert_experiment(params)
    if not item:
        return response_fail(msg='insert fail')
    byte_log.info("add_experiment insert success")
    data = libra_experiment_review_handler.handle_insert_experiment_data(item=item)
    # 触发异步方法获取meego测试报告信息、检查实验字段
    background_tasks.add_task(libra_experiment_review_service.handle_experiment_check, data)
    byte_log.info("add_experiment handle data success")
    return response_success(data=data)

@router.get("/find_latest_check_rule_by_business_id", response_model=HttpResponse[LibraExperimentFieldAndRuleRes], summary="根据业务id获取最新正在启用的检查规则")
async def find_latest_check_rule_by_business_id(business_id: int):
    items = libra_experiment_review_db.find_latest_rule_by_business_id(business_id)
    data = libra_experiment_review_handler.handle_find_latest_rule_by_business_id(items=items)
    return response_success(data=data)


@router.post("/add_rule", response_model=HttpResponse[Union[str, None]], summary="添加实验检查规则")
async def add_rule(params: AddLibraExperimentRuleParams):
    try:
        libra_rule_db.add_rule(params)
        return response_success(data="添加规则成功")
    except Exception as e:
        return response_fail(msg=str(e))


@router.put("/update_rule", response_model=HttpResponse[str], summary="更新实验检查规则")
async def update_client(params: UpdateLibraExperimentRuleParams):
    try:
        libra_rule_db.update_rule(params)
        return response_success(data="更新规则成功")
    except Exception as e:
        return response_fail(msg=str(e))