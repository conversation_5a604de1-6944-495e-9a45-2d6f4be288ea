from fastapi import APIRouter

from dals.db.business.business_db import business_db
from handlers.business import business_handler
from schemas.request.business.business_req import BusinessAddReq, BusinessUpdateReq
from schemas.response.business.business_res import BusinessFetchAllRes, BusinessAddRes, BusinessUpdateRes
from utils.common.response import *
router = APIRouter()


@router.get("/fetch_all", response_model=HttpResponse[BusinessFetchAllRes], summary="获取所有业务列表")
async def fetch_all():
    total, items = business_db.fetch_all()
    data = business_handler.handle_fetch_all_data(total=total, items=items)
    return response_success(data=data)

@router.post("/add", response_model=HttpResponse[BusinessAddRes], summary="新增业务")
async def add_business(req: BusinessAddReq):
    business_id = business_db.add_business(
        business_name=req.business_name,
        business_dir=req.business_dir
    )
    data = business_handler.handle_add_business(business_id=business_id)
    if not data:
        return response_fail(msg="新增业务失败")
    return response_success(data=data)

@router.put("/update", response_model=HttpResponse[BusinessUpdateRes], summary="更新业务信息")
async def update_business(req: BusinessUpdateReq):
    business = business_db.update_business(
        business_id=req.id,
        business_name=req.business_name,
        business_dir=req.business_dir
    )
    data = business_handler.handle_update_business(business=business)
    if not data:
        return response_fail(msg="更新业务失败")
    return response_success(data=data)






