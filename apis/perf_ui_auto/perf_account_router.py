from typing import Any, List, Optional
from fastapi import APIRouter, Query, Depends
from dals.db.perf_ui_auto.perf_account_db import perf_account_db
from handlers.perf_ui_auto import perf_account_handler
from schemas.request.perf_ui_auto.perf_account_req import AddAccountParams, UpdateAccountParams, GetAccountListRequest
from utils.common.response import HttpResponse, response_success, response_fail
from schemas.response.perf_ui_auto.perf_account_res import PerfAccountItem, PerfAccountListResponse

router = APIRouter(
    prefix="/perf_account"
)

# 账号信息查询接口
@router.post(
    "/account_list", 
    response_model=HttpResponse[PerfAccountListResponse], 
    summary="获取账号列表"
)
async def get_account_list(params: GetAccountListRequest):
    """
    获取账号列表，支持多条件筛选
    Args:
        params: 请求参数
            - page: 页码，默认1
            - page_size: 每页大小，默认10
            - business_id: 可选，业务线ID
            - is_occupied: 可选，是否被占用 0-未占用 1-已占用
            - account_type: 可选，账号类型 1-性能测试账号 2-普通账号
    Returns:
        HttpResponse[PerfAccountListResponse]: 包含分页信息和账号列表的响应
    """
    try:
        total, items = perf_account_db.get_account_list(params)
        data = perf_account_handler.handle_account_list_data(page=params, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get(
    "/account_detail",
    response_model=HttpResponse[PerfAccountItem], 
    summary="获取账号详情"
)
async def get_account_detail(account_id: int):
    """
    获取单个账号详情
    Args:
        account_id: 账号ID
    """
    try:
        account = perf_account_db.get_account_detail(account_id)
        if not account:
            return response_fail(msg="账号不存在")
        data = perf_account_handler.handle_account_detail(account)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

# 账号信息更新接口
@router.post(
    "/add_account", 
    response_model=HttpResponse[PerfAccountItem], 
    summary="创建账号"
)
async def add_account(params: AddAccountParams):
    """
    添加新账号
    Args:
        params: 账号参数
            - 必填字段：business_id, uid, iphone, username, owner
            - 可选字段：captcha, email, pwd, country, country_code_alpha2, phone_area_code
            - 默认值字段：app(0), is_occupied(0), account_type(2)
    """
    try:
        account = perf_account_db.add_account(params)
        data = perf_account_handler.handle_add_account(account)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put(
    "/update_account", 
    response_model=HttpResponse[PerfAccountItem], 
    summary="更新账号信息"
)
async def update_account(params: UpdateAccountParams):
    """
    更新账号信息
    Args:
        params: 更新参数
            - 必填字段：id
            - 可选字段：所有字段
    """
    try:
        account = perf_account_db.update_account(params.id, params)
        data = perf_account_handler.handle_update_account(account)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put(
    "/update_account_occupied",
    response_model=HttpResponse[Optional[bool]],
    summary="更新账号占用状态"
)
async def update_account_occupied(account_id: int, is_occupied: int):
    """
    更新账号占用状态
    Args:
        account_id: 账号ID
        is_occupied: 是否被占用 0-未占用 1-已占用
    Returns:
        更新结果
    """
    try:
        perf_account_db.update_account_occupied(account_id, is_occupied)
        return response_success(data=True)
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete(
    "/delete_account",
    response_model=HttpResponse[bool],
    summary="删除账号"
)
async def delete_account(account_id: int):
    """
    删除账号
    Args:
        account_id: 账号ID
    Returns:
        删除结果
    """
    try:
        perf_account_db.delete_account(account_id)
        data = perf_account_handler.handle_delete_account(True)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))
