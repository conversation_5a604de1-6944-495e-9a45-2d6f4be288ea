'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 19:13:27
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_metric_router.py
Description: 性能指标相关路由模块
'''
from typing import List, Optional
from fastapi import APIRouter, Query

from handlers.perf_ui_auto.perf_metric_handler import perf_metric_handler
from schemas.request.perf_ui_auto.perf_metric_req import CreateMetricRequest, UpdateMetricRequest, MetricListRequest, BatchCreateMetricRequest
from schemas.response.perf_ui_auto.perf_metric_res import MetricResponse, MetricListResponse, BatchMetricResponse
from utils.common.response import HttpResponse, response_success, response_fail

router = APIRouter(
    prefix="/perf_metric"
)

@router.post(
    "/metric_list",
    response_model=HttpResponse[MetricListResponse],
    summary="获取性能指标列表"
)
async def get_metric_list(params: MetricListRequest):
    """
    获取性能指标列表
    Args:
        params: 请求参数
            - metric_type: 可选，指标类型 1-common 2-android 3-ios
            - metric_category: 可选，指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
            - metric_key: 可选，指标key
            - metric_name: 可选，指标名称
            - creator: 可选，创建者
            - start_time: 可选，开始时间
            - end_time: 可选，结束时间
            - page: 页码，默认1
            - page_size: 每页数量，默认10
    Returns:
        HttpResponse[MetricListResponse]: 包含指标列表和总记录数的响应
    """
    try:
        result = perf_metric_handler.get_metrics(params)
        return response_success(data=result)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get(
    "/metric_detail",
    response_model=HttpResponse[MetricResponse],
    summary="获取性能指标详情"
)
async def get_metric_detail(metric_id: int = Query(..., description="指标ID", gt=0)):
    """
    获取性能指标详情
    Args:
        metric_id: 指标ID
    Returns:
        HttpResponse[MetricResponse]: 指标详情响应
    """
    try:
        result = perf_metric_handler.get_metric(metric_id)
        if result.metric is None:
            return response_fail(msg="指标不存在")
        return response_success(data=result)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post(
    "/add_metric",
    response_model=HttpResponse[MetricResponse],
    summary="创建性能指标"
)
async def add_metric(params: CreateMetricRequest):
    """
    创建性能指标
    Args:
        params: 创建参数
            - metric_key: 指标key
            - metric_name: 指标名称
            - metric_type: 指标类型 1-common 2-android 3-ios
            - metric_category: 指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
            - metric_unit: 可选，指标单位
            - metric_desc: 可选，指标描述
            - creator: 创建者
    Returns:
        HttpResponse[MetricResponse]: 创建结果响应
    """
    try:
        result = perf_metric_handler.create_metric(params)
        if result.metric is None:
            return response_fail(msg="创建失败")
        return response_success(data=result, msg="创建成功")
    except Exception as e:
        return response_fail(msg=str(e))

@router.post(
    "/batch_add_metrics",
    response_model=HttpResponse[BatchMetricResponse],
    summary="批量创建性能指标"
)
async def batch_add_metrics(params: BatchCreateMetricRequest):
    """
    批量创建性能指标
    Args:
        params: 批量创建参数
            - metrics: 性能指标列表
                - metric_key: 指标key
                - metric_name: 指标名称
                - metric_type: 指标类型 1-common 2-android 3-ios
                - metric_category: 指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
                - metric_unit: 可选，指标单位
                - metric_desc: 可选，指标描述
                - creator: 创建者
    Returns:
        HttpResponse[BatchMetricResponse]: 批量创建结果响应
    """
    try:
        result = perf_metric_handler.batch_create_metrics(params)
        msg = f"批量创建完成: 成功{result.success_count}个, 失败{result.failed_count}个"
        return response_success(data=result, msg=msg)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put(
    "/update_metric",
    response_model=HttpResponse[MetricResponse],
    summary="更新性能指标"
)
async def update_metric(params: UpdateMetricRequest):
    """
    更新性能指标
    Args:
        params: 更新参数
            - id: 指标ID
            - metric_key: 可选，指标key
            - metric_name: 可选，指标名称
            - metric_type: 可选，指标类型 1-common 2-android 3-ios
            - metric_category: 可选，指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
            - metric_unit: 可选，指标单位
            - metric_desc: 可选，指标描述
    Returns:
        HttpResponse[MetricResponse]: 更新结果响应
    """
    try:
        result = perf_metric_handler.update_metric(params)
        if result.metric is None:
            return response_fail(msg="指标不存在或更新失败")
        return response_success(data=result, msg="更新成功")
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete(
    "/delete_metric",
    response_model=HttpResponse[MetricResponse],
    summary="删除性能指标"
)
async def delete_metric(metric_id: int = Query(..., description="指标ID", gt=0)):
    """
    删除性能指标
    Args:
        metric_id: 指标ID
    Returns:
        HttpResponse[MetricResponse]: 删除结果响应
    """
    try:
        result = perf_metric_handler.delete_metric(metric_id)
        # 删除接口返回的MetricResponse.metric是None，这是正确的，因为已删除
        return response_success(data=result, msg="删除成功")
    except Exception as e:
        return response_fail(msg=str(e))
