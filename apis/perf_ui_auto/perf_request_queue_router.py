from fastapi import APIRouter, Query, Body, HTTPException
from typing import Optional, Dict, List
from dals.redis.perf_ui_auto.perf_request_redis import perf_request_redis
from utils.common.response import HttpResponse, response_success, response_fail
from utils.common.bytelog import byte_log
from schemas.response.perf_ui_auto.perf_request_queue_res import PerfRequestData, GetAllRequestsRes

router = APIRouter(
    prefix="/perf_request_queue",
    tags=["性能测试请求队列"]
)

@router.get(
    "/all",
    response_model=HttpResponse[GetAllRequestsRes],
    summary="获取性能测试请求队列所有请求"
)
async def get_all_requests(
    client_id: int = Query(..., description="客户端ID", ge=1)
) -> HttpResponse:
    """
    获取指定客户端的所有请求队列数据
    
    Args:
        client_id (int): 客户端ID，必须大于等于1
        
    Returns:
        HttpResponse[GetAllRequestsRes]: 包含所有请求的列表
    
    Raises:
        HTTPException: 当 client_id 无效时抛出 400 错误
    """
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        # 获取所有请求
        requests = perf_request_redis.get_all_sub_requests(client_id)
        
        try:
            # 构造响应数据
            response_data = GetAllRequestsRes(
                requests=[
                    PerfRequestData(
                        event_key=req.get("event_key", ""),
                        request_body=req.get("request_body", {}),
                        request_time=req.get("request_time", "")
                    )
                    for req in requests
                ]
            )
            return response_success(data=response_data.model_dump())
        except Exception as e:
            byte_log.error(f"请求数据格式错误: {str(e)}")
            return response_fail(msg="请求数据格式错误")
    except HTTPException as e:
        raise e
    except Exception as e:
        byte_log.error(f"获取所有请求失败: {str(e)}")
        return response_fail(msg=f"获取所有请求失败: {str(e)}")

@router.delete(
    "/clear",
    response_model=HttpResponse[bool],
    summary="清空性能测试请求队列"
)
async def clear_requests(
    client_id: int = Query(..., description="客户端ID", ge=1)
) -> HttpResponse:
    """
    清空指定客户端的请求队列
    
    Args:
        client_id (int): 客户端ID，必须大于等于1
        
    Returns:
        HttpResponse[bool]: 清空操作是否成功
    
    Raises:
        HTTPException: 当 client_id 无效时抛出 400 错误
    """
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        # 清空队列
        success = perf_request_redis.clear_queue(client_id)
        return response_success(data=success)
    except HTTPException as e:
        raise e
    except Exception as e:
        byte_log.error(f"清空请求队列失败: {str(e)}")
        return response_fail(msg=f"清空请求队列失败: {str(e)}")

