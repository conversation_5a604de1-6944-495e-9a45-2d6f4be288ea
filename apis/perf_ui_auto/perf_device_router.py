'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_device_router.py
Description: 性能测试设备路由模块
'''
from typing import List, Optional, Union

from fastapi import APIRouter, Depends, Query, Body

from dals.db.perf_ui_auto.perf_device_db import perf_device_db
from handlers.perf_ui_auto import perf_device_handler
from schemas.request.perf_ui_auto.perf_device_req import (
    UploadPerfDeviceParams,
    GetDeviceListParams
)
from schemas.response.perf_ui_auto.perf_device_res import (
    PerfDeviceItem,
    PerfDeviceListResponse
)
from utils.common.response import (
    HttpResponse,
    response_success,
    response_fail
)
from utils.common.depends import PageQuery

router = APIRouter(prefix="/perf_device")


# 设备信息查询接口
@router.get(
    "/device_list",
    response_model=HttpResponse[PerfDeviceListResponse],
    summary="分页查询性能测试设备列表"
)
async def get_device_list(
    page: PageQuery,
    params: GetDeviceListParams = Depends()
):
    """
    获取设备列表（支持分页）
    """
    try:
        total, items = perf_device_db.get_device_list(
            params=params,
            page=page.page,
            page_size=page.page_size
        )
        data = perf_device_handler.get_device_list(
            page=page.page,
            page_size=page.page_size,
            total=total,
            items=items
        )
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get(
    "/device_detail",
    response_model=HttpResponse[Optional[PerfDeviceItem]],
    summary="根据设备ID查询设备详情"
)
async def get_device_detail(device_id: int):
    """
    获取单个设备的详细信息
    Args:
        device_id: 设备ID
    Returns:
        设备详细信息
    """
    try:
        item = perf_device_db.get_device_detail(device_id)
        data = perf_device_handler.get_device_detail(item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get(
    "/available_devices",
    response_model=HttpResponse[Optional[List[PerfDeviceItem]]],
    summary="根据系统类型获取指定数量的可用设备"
)
async def get_available_devices(sys_type: int, count: int):
    """
    获取指定数量的可用设备
    Args:
        sys_type: 系统类型 (1-android, 2-ios)
        count: 需要的设备数量
    Returns:
        可用设备列表
    """
    try:
        devices = perf_device_db.get_available_devices(sys_type, count)
        data = perf_device_handler.get_available_devices(devices)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


# 设备信息更新接口
@router.post(
    "/upload_perf_device",
    response_model=HttpResponse[Optional[str]],
    summary="批量上传性能测试设备信息并更新设备状态"
)
async def upload_perf_device(client_id: int = Query(..., description="客户端ID"), 
                           device_list: List[UploadPerfDeviceParams] = Body(..., description="设备列表")):
    """
    上传性能测试设备信息
    Args:
        client_id: 客户端ID（查询参数）
        device_list: 设备列表（请求体）
    功能:
        1. 更新上传的设备信息
        2. 将数据库中当前client_id没有上传的设备更新为离线状态
    Returns:
        上传结果
    """
    try:
        if not client_id:
            return response_fail(msg="客户端ID不能为空")
            
        # 获取已存在的设备列表
        existing_devices = perf_device_db.get_devices_by_client_id(client_id)
        
        # 获取上传设备的UDID集合
        uploading_device_udids = {device.udid for device in device_list}
        
        # 找出需要设置为离线的设备ID
        offline_device_ids = [dev.id for dev in existing_devices 
                            if dev.udid not in uploading_device_udids]
                            
        # 更新设备信息
        perf_device_db.update_perf_device(client_id, device_list, offline_device_ids)
        
        return response_success(data="设备信息更新成功")
    except Exception as e:
        return response_fail(msg=str(e))


@router.put(
    "/update_device_occupied",
    response_model=HttpResponse[Optional[bool]],
    summary="更新设备占用状态(占用/释放)"
)
async def update_device_occupied(device_id: int, is_occupied: int):
    """
    更新设备占用状态
    Args:
        device_id: 设备ID
        is_occupied: 是否被占用 0-未占用 1-已占用
    Returns:
        更新结果
    """
    try:
        perf_device_db.update_device_occupied(device_id, is_occupied)
        return response_success(data=True)
    except Exception as e:
        return response_fail(msg=str(e))


@router.delete(
    "/delete_device",
    response_model=HttpResponse[Optional[bool]],
    summary="删除设备"
)
async def delete_device(device_id: int):
    """
    删除设备
    Args:
        device_id: 设备ID
    Returns:
        删除结果
    """
    try:
        perf_device_db.delete_device(device_id)
        return response_success(data=True)
    except Exception as e:
        return response_fail(msg=str(e))
