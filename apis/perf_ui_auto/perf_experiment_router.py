'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_experiment_router.py
Description: 实验相关路由
'''
from typing import Any, Optional
from fastapi import APIRouter, Query

from handlers.perf_ui_auto.perf_experiment_handler import perf_experiment_handler
from schemas.request.perf_ui_auto.perf_experiment_req import (
    CreateExperimentRequest,
    ListExperimentsRequest,
    UpdateExperimentRequest,
    DeleteExperimentRequest,
    GetExperimentDetailRequest,
    ViewFlightByVidRequest,
)
from schemas.response.perf_ui_auto.perf_experiment_res import (
    ExperimentItem,
    ExperimentListResponse,
)
from utils.common.response import HttpResponse, response_success, response_fail

router = APIRouter(
    prefix="/experiment"
)

@router.post(
    "/create", 
    response_model=HttpResponse[ExperimentItem], 
    summary="创建实验"
)
async def create_experiment(request: CreateExperimentRequest):
    """
    创建新实验
    Args:
        request: 创建实验请求参数
            - name: 实验名称
            - hit_type: 命中类型 1-DID命中 2-UID命中
            - experiment_group_version_ids: 实验组版本IDs
            - control_group_version_ids: 对照组版本IDs
            - description: 可选，实验描述
            - creator: 创建者
    Returns:
        HttpResponse[ExperimentItem]: 包含创建的实验信息的响应
    """
    try:
        experiment = perf_experiment_handler.create_experiment(request)
        return response_success(data=experiment)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get(
    "/list", 
    response_model=HttpResponse[ExperimentListResponse], 
    summary="获取实验列表"
)
async def get_experiment_list(request: ListExperimentsRequest):
    """
    获取实验列表，支持分页查询
    Args:
        request: 查询参数
            - page_size: 每页数量，默认20
            - page: 页码，默认1
            - name: 可选，实验名称
            - hit_type: 可选，命中类型
    Returns:
        HttpResponse[ExperimentListResponse]: 包含分页信息和实验列表的响应
    """
    try:
        total, items = perf_experiment_handler.get_experiment_list(request)
        data = perf_experiment_handler.handle_experiment_list(request, total, items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put(
    "/update", 
    response_model=HttpResponse[ExperimentItem], 
    summary="更新实验"
)
async def update_experiment(request: UpdateExperimentRequest):
    """
    更新实验信息
    Args:
        request: 更新参数
            - id: 实验ID
            - name: 可选，实验名称
            - hit_type: 可选，命中类型
            - experiment_group_version_ids: 可选，实验组版本IDs
            - control_group_version_ids: 可选，对照组版本IDs
            - description: 可选，实验描述
    Returns:
        HttpResponse[ExperimentItem]: 包含更新后的实验信息的响应
    """
    try:
        experiment = perf_experiment_handler.update_experiment(request)
        return response_success(data=experiment)
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete(
    "/delete",
    response_model=HttpResponse[bool],
    summary="删除实验"
)
async def delete_experiment(request: DeleteExperimentRequest):
    """
    删除实验
    Args:
        request: 删除参数
            - id: 实验ID
    Returns:
        HttpResponse[bool]: 删除结果
    """
    try:
        success = perf_experiment_handler.delete_experiment(request)
        return response_success(data=success)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get(
    "/detail",
    response_model=HttpResponse[ExperimentItem],
    summary="获取实验详情"
)
async def get_experiment_detail(id: int = Query(..., description="实验ID")):
    """
    获取实验详情
    Args:
        id: 实验ID
    Returns:
        HttpResponse[ExperimentItem]: 包含实验详细信息的响应
    """
    try:
        params = GetExperimentDetailRequest(id=id)
        experiment = perf_experiment_handler.get_experiment_detail(params)
        return response_success(data=experiment)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post(
    "/view_flight_by_vid",
    response_model=HttpResponse[list],
    summary="根据vid列表查询实验信息"
)
async def view_flight_by_vid(request: ViewFlightByVidRequest):
    """
    根据vid列表查询实验信息
    Args:
        request: ViewFlightByVidRequest
    Returns:
        HttpResponse[list]
    """
    try:
        result = await perf_experiment_handler.view_flight_by_vid(request)
        return response_success(data=result)
    except Exception as e:
        return response_fail(msg=str(e))
