'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /backend/apis/perf_ui_auto/perf_case_router.py
Description: 性能测试用例相关路由模块
'''
from typing import Any
from fastapi import APIRouter, Depends, Query
from handlers.perf_ui_auto.perf_case_handler import handle_update_case, get_case_list_handle, get_case_detail_handle
from schemas.request.perf_ui_auto.perf_case_req import CaseListParams,UpdateCaseParams
from utils.common.depends import PageQuery
from utils.common.response import HttpResponse, response_success, response_fail

router = APIRouter(
    prefix="/perf_case"
)


@router.post("/case_list", response_model=HttpResponse[Any], summary="分页查询性能测试用例列表")
async def get_case_list(params: CaseListParams, page: PageQuery):
    data,success= get_case_list_handle(params=params, page=page.page, page_size=page.page_size)
    return response_success(data=data)


@router.post("/update_case", response_model=HttpResponse[Any], summary="更新性能测试用例配置信息")
async def update_case(params:UpdateCaseParams):
    data,success = handle_update_case()
    if success:
        return response_success(data=data)
    else:
        return response_fail(data=data,msg='拉取更新脚本执行错误')


@router.get("/case_detail", response_model=HttpResponse[Any], summary="根据用例ID查询性能测试用例详情")
async def get_case_detail(case_id: int):
    data, success = get_case_detail_handle(case_id=case_id)
    if success:
        return response_success(data=data)
    return response_fail(msg='获取用例详情失败')