"""
性能测试任务相关路由
"""
from datetime import datetime
from typing import Union, Any
from fastapi import APIRouter, Query, Body, Depends, BackgroundTasks

from dals.redis.perf_ui_auto.perf_request_redis import perf_request_redis
from defines.perf_ui_auto_define import *
from handlers.perf_ui_auto.perf_task_handler import get_task_status_by_task_id, get_request_items_by_task_id, \
    handle_requests_for_queue
from schemas.request.perf_ui_auto.perf_task_req import (
    PerfTaskListParams,
    CreatePerfTaskParams,
    UpdatePerfTaskParams,
    UploadPerfCaseRunDetailParams,
    UpdateSubTaskStatusParams,
    UpdateTaskStatusParams, CancelPerfTaskParams
)
from schemas.response.perf_ui_auto.perf_task_res import (
    PerfTaskListRes,
    PerfTaskRes,
    UploadPerfCaseRunDetailItem,
    PerfSubTaskRes,
    PerfTaskAllRes,
    PerfCaseRunDetailRes,
    CaseExecutionStatsListRes
)
from utils.common.bytelog import byte_log
from utils.common.depends import PageQuery
from utils.common.response import HttpResponse, response_success, response_fail
from dals.db.perf_ui_auto.perf_task_db import perf_task_db
from service.perf_ui_auto import perf_task_service
from dals.redis.perf_ui_auto.perf_task_redis import perf_task_redis
from handlers.perf_ui_auto import perf_task_handler

router = APIRouter(prefix="/perf_task")

@router.post("/task_list", response_model=HttpResponse[PerfTaskListRes], summary="分页查询性能测试任务列表")
async def get_task_list(page: PageQuery, params: PerfTaskListParams):
    """获取性能任务列表"""
    try:
        total, items = perf_task_db.get_task_list(page=page, params=params)
        data = perf_task_handler.handle_task_list(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/task_detail", response_model=HttpResponse[PerfTaskRes], summary="根据任务ID查询任务和子任务详情")
async def get_task_detail(task_id: int):
    """获取性能任务和子任务详情，编辑任务时使用"""
    try:
        task_item = perf_task_db.get_task(task_id)
        sub_task_items = perf_task_db.get_sub_tasks_by_task_id(task_id)
        if not task_item or not sub_task_items:
            return response_fail(msg="未找到任务数据")
            
        data = perf_task_handler.handle_task_data(task_item=task_item, sub_task_items=sub_task_items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/task_detail_all", response_model=HttpResponse[PerfTaskAllRes], summary="根据任务ID查询任务和子任务的完整详情")
async def get_task_detail_all(task_id: int):
    """获取性能任务和子任务的完整详情，包含关联数据，执行详情时调用"""
    try:
        task_item_tuple = perf_task_db.get_task_all(task_id)
        sub_task_item_tuple_list = perf_task_db.get_sub_tasks_by_task_id_all(task_id)
        if not task_item_tuple or not sub_task_item_tuple_list:
            return response_fail(msg="未找到任务数据")
            
        data = perf_task_handler.handle_task_data_all(
            task_item_tuple=task_item_tuple, 
            sub_task_item_tuple_list=sub_task_item_tuple_list
        )
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put("/create_task", response_model=HttpResponse[PerfTaskRes], summary="创建新的性能测试任务和子任务")
async def create_task(params: CreatePerfTaskParams):
    """创建性能任务和子任务"""
    try:
        task_item = perf_task_db.create_task(params)
        sub_task_items = perf_task_db.create_sub_tasks(params, task_item)
        if not task_item or not sub_task_items:
            return response_fail(msg="创建任务失败")
            
        data = perf_task_handler.handle_task_data(task_item=task_item, sub_task_items=sub_task_items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/update_task", response_model=HttpResponse[PerfTaskRes], summary="更新已有性能测试任务和子任务")
async def update_task(params: UpdatePerfTaskParams):
    """更新性能任务和子任务"""
    try:
        task_item = perf_task_db.update_task(params)
        sub_task_items = perf_task_db.update_sub_tasks(params)
        if not task_item or not sub_task_items:
            return response_fail(msg="更新任务失败")
            
        data = perf_task_handler.handle_task_data(task_item=task_item, sub_task_items=sub_task_items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/copy_task", response_model=HttpResponse[Union[PerfTaskRes, None]], summary="复制已有性能测试任务和子任务")
async def copy_task(task_id: int, operator: str):
    """复制性能任务和子任务"""
    try:
        task_item = perf_task_db.copy_task(task_id, operator)
        sub_task_items = perf_task_db.copy_sub_tasks(origin_task_id=task_id, new_task_id=task_item.id)
        if not task_item or not sub_task_items:
            return response_fail(msg="复制任务失败")
            
        data = perf_task_handler.handle_task_data(task_item=task_item, sub_task_items=sub_task_items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete("/delete_task", response_model=HttpResponse[Any], summary="删除指定性能测试任务和子任务")
async def delete_task(task_id: int):
    """删除性能任务和子任务"""
    try:
        perf_task_db.delete_task(task_id)
        perf_task_db.delete_sub_tasks(task_id=task_id)
        return response_success(data=None)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/start_task", response_model=HttpResponse[Any], summary="启动指定性能测试任务并将子任务加入队列")
async def start_task(task_id: int):
    """启动性能测试任务，将子任务添加到对应client的队列中"""
    try:
        # 更新主任务状态为等待中
        task_item = perf_task_db.update_task_status_by_id(
            UpdateTaskStatusParams(
                id=task_id,
                status=TASK_STATUS_PENDING,
                start_time=datetime.now()
            )
        )
        
        # 获取所有子任务
        sub_task_items = perf_task_db.get_sub_tasks_by_task_id(task_id)
        
        # 处理并推送子任务到队列
        sub_tasks_data = perf_task_handler.handle_sub_tasks_for_queue(sub_task_items)
        data = all([perf_task_redis.push_sub_task(task_item.client_id, sub_task) for sub_task in sub_tasks_data])
        return response_success(data=data)
        
    except Exception as e:
        byte_log.error(f"启动任务失败: {str(e)}")
        return response_fail(msg=str(e))

@router.get("/retry_task", response_model=HttpResponse[Any], summary="重试失败的性能测试子任务")
async def retry_task(task_id: int):
    """重试任务,将失败的子任务重新添加到队列中"""
    try:
        # 更新主任务状态为重试等待中
        task_item = perf_task_db.update_task_status_by_id(
            UpdateTaskStatusParams(id=task_id, status=TASK_STATUS_RETRY_PENDING)
        )
        
        # 获取所有失败的子任务
        sub_task_items = perf_task_db.get_sub_tasks_by_task_id(task_id)
        failed_sub_tasks = [
            task for task in sub_task_items 
            if task.status in [TASK_STATUS_FAILED, TASK_STATUS_CANCELED, TASK_STATUS_TIMEOUT]
        ]
        
        # 更新失败子任务状态为重试等待中
        updated_sub_tasks = []
        for sub_task in failed_sub_tasks:
            updated_sub_task = perf_task_db.update_sub_task_status_by_id(
                UpdateSubTaskStatusParams(id=sub_task.id, status=TASK_STATUS_RETRY_PENDING)
            )
            if updated_sub_task:
                updated_sub_tasks.append(updated_sub_task)
        
        # 处理并推送更新后的子任务到队列
        sub_tasks_data = perf_task_handler.handle_sub_tasks_for_queue(updated_sub_tasks)
        data = all([perf_task_redis.push_sub_task(task_item.client_id, sub_task) for sub_task in sub_tasks_data])
        return response_success(data=data)
        
    except Exception as e:
        byte_log.error(f"重试任务失败: {str(e)}")
        return response_fail(msg=str(e))


@router.post("/cancel_task", response_model=HttpResponse[Any], summary="取消任务")
async def cancel_task(params: CancelPerfTaskParams):
    """取消任务"""
    try:
        if params.status not in [TASK_STATUS_PENDING, TASK_STATUS_RUNNING, TASK_STATUS_RETRY_PENDING, TASK_STATUS_RETRY_RUNNING]:
            return response_fail(msg="任务状态不允许取消")
        task_id = params.task_id
        client_id = params.client_id

        # 把所有的子任务从性能任务队列中移除
        perf_task_redis.remove_sub_tasks_by_task_id(client_id=client_id, task_id=task_id)

        # 更新主任务状态为已取消
        task_item = perf_task_db.update_task_status_by_id(
            UpdateTaskStatusParams(
                id=task_id,
                status=TASK_STATUS_CANCELED
            )
        )

        # 获取所有需要改成已取消的的子任务（只要不是运行成功/失败,或者超时的子任务，统统改成已取消状态
        sub_task_items = perf_task_db.get_sub_tasks_by_task_id(task_id)
        sub_tasks_to_cancel = [
            task for task in sub_task_items
            if task.status not in [TASK_STATUS_SUCCESS]
        ]

        # 更新需要更改的子任务状态为已取消状态
        updated_sub_tasks = []
        for sub_task in sub_tasks_to_cancel:
            updated_sub_task = perf_task_db.update_sub_task_status_by_id(
                UpdateSubTaskStatusParams(id=sub_task.id, status=TASK_STATUS_CANCELED)
            )
            if updated_sub_task:
                updated_sub_tasks.append(updated_sub_task)

        # 把取消当前任务的消息发送给请求队列
        requests_items = get_request_items_by_task_id([task_id])
        requests_data = handle_requests_for_queue(requests_items)
        data = all([perf_request_redis.push_request(client_id=client_id, request=request) for request in requests_data])
        return response_success(data=data)

    except Exception as e:
            byte_log.error(f"取消任务失败: {str(e)}")
            return response_fail(msg=str(e))

@router.post("/update_task_status_by_id", response_model=HttpResponse[Any], summary="更新指定性能测试任务的执行状态")
async def update_task_status_by_id(params: UpdateTaskStatusParams, background_tasks: BackgroundTasks):
    """更新任务执行状态"""
    try:
        task_item = perf_task_db.update_task_status_by_id(params)
        if not task_item:
            return response_fail(msg="更新任务状态失败")
            
        data = perf_task_handler.handle_task_status_data(task_item)
        background_tasks.add_task(check_task_status, params)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"更新任务状态失败: {str(e)}")
        return response_fail(msg=str(e))

def check_task_status(params: UpdateTaskStatusParams):
    """检查任务状态并发送通知"""
    task_id = params.id
    status = params.status
    
    if status in [TASK_STATUS_SUCCESS, TASK_STATUS_FAILED, TASK_STATUS_CANCELED, TASK_STATUS_TIMEOUT]:
        task_item = perf_task_db.get_task(task_id)
        sub_task_items = perf_task_db.get_sub_tasks_by_task_id(task_id)
        if task_item and sub_task_items:
            perf_task_service.send_lark_card(task_item, sub_task_items)

@router.post("/update_sub_task_status_by_id", response_model=HttpResponse[PerfSubTaskRes], summary="更新指定性能测试子任务的执行状态")
async def update_sub_task_status_by_id(params: UpdateSubTaskStatusParams):
    """更新子任务执行状态"""
    try:
        sub_task_item = perf_task_db.update_sub_task_status_by_id(params)
        if not sub_task_item:
            return response_fail(msg="更新子任务状态失败")
            
        data = perf_task_handler.handle_sub_task_data(item=sub_task_item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/upload_sub_task_case_run_detail", response_model=HttpResponse[Union[UploadPerfCaseRunDetailItem, None]], summary="上传性能测试子任务的用例执行详情")
async def upload_sub_task_case_run_detail(params: UploadPerfCaseRunDetailParams):
    """上传子任务执行的用例运行详情到数据库"""
    try:
        case_run_detail_item = perf_task_db.upload_perf_case_run_detail(params)
        data = perf_task_handler.get_upload_perf_case_run_detail(case_run_detail_item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/get_sub_task_case_run_detail", response_model=HttpResponse[PerfCaseRunDetailRes], summary="获取性能测试子任务的用例执行详情")
async def get_sub_task_case_run_detail(page: PageQuery, sub_task_id: int):
    """获取子任务执行的用例详情"""
    try:
        total, item_tuple_list = perf_task_db.get_sub_task_case_run_detail(page=page, sub_task_id=sub_task_id)
        data = perf_task_handler.handle_sub_task_case_run_detail_list(
            page=page,
            total=total,
            item_tuple_list=item_tuple_list
        )
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/get_case_execution_stats", response_model=HttpResponse[CaseExecutionStatsListRes], summary="获取性能测试子任务的用例执行统计数据")
async def get_case_execution_stats(sub_task_id: int = Query(..., description="子任务ID")):
    """获取子任务用例执行统计"""
    try:
        if sub_task_id <= 0:
            return response_fail(msg="无效的子任务ID", code=-1)
        
        stats = perf_task_db.get_case_execution_stats(sub_task_id)
        if not stats:
            return response_fail(msg="未找到用例执行统计", code=-1)
        
        return response_success(data=stats)
    except Exception as e:
        byte_log.error(f"获取用例执行统计失败: {str(e)}")
        return response_fail(msg=str(e))
