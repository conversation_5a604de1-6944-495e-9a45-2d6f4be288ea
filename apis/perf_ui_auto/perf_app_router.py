from typing import Any, Optional

from fastapi import APIRouter

from dals.db.perf_ui_auto.perf_app_db import perf_app_db
from handlers.perf_ui_auto import perf_app_handler
from schemas.request.perf_ui_auto.perf_app_req import *
from schemas.response.perf_ui_auto.perf_app_res import *
from utils.common.depends import PageQuery
from utils.common.response import HttpResponse, response_success, response_fail

router = APIRouter(
    prefix="/perf_app"
)

@router.get("/app_version_list", response_model=HttpResponse[Any], summary="获取应用版本列表")
async def app_version_list(page: PageQuery, business_id: int = None, version: str = ""):
    try:
        total, items = perf_app_db.get_version_list(page=page, business_id=business_id, version=version)
        data = perf_app_handler.get_version_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/app_list", response_model=HttpResponse[PerfAppListRes], summary="获取应用列表")
async def app_list(page: PageQuery, params: GetAppListParams):
    try:
        total, items = perf_app_db.get_app_list(page=page, params=params)
        data = perf_app_handler.handle_app_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/add_app", response_model=HttpResponse[PerfAppListItemRes], summary="创建新应用")
async def add_app(params: AddAppParams):
    try:
        item = perf_app_db.insert_app(params=params)
        if not item:
            return response_fail(msg='insert fail')
        data = perf_app_handler.handle_insert_app_data(item=item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put("/update_app", response_model=HttpResponse[PerfAppListItemRes], summary="更新应用信息")
async def update_app(params: UpdateAppParams):
    try:
        item = perf_app_db.update_app(params=params)
        if not item:
            return response_fail(msg='update fail')
        data = perf_app_handler.handle_update_app_data(item=item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/app_group_list", response_model=HttpResponse[PerfAppGroupListRes], summary="获取应用组列表")
async def app_group_list(page: PageQuery, params: GetAppGroupListParams):
    try:
        total, items = perf_app_db.get_app_group_list(page=page, params=params)
        data = perf_app_handler.handle_app_group_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/add_app_group", response_model=HttpResponse[AddPerfAppGroupRes], summary="创建新应用组")
async def add_app_group(params: AddAppGroupParams):
    try:
        item = perf_app_db.insert_app_group(params=params)
        if not item:
            return response_fail(msg='insert fail')
        data = perf_app_handler.handle_insert_app_group_data(item=item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put("/update_app_group", response_model=HttpResponse[AddPerfAppGroupRes], summary="更新应用组信息")
async def update_app_group(params: UpdateAppGroupParams):
    try:
        item = perf_app_db.update_app_group(params=params)
        if not item:
            return response_fail(msg='update fail')
        data = perf_app_handler.handle_update_app_group_data(item=item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/app_group_detail", response_model=HttpResponse[PerfAppGroupDetailRes], summary="获取应用组详细信息")
async def app_group_detail(app_group_id: int):
    try:
        item = perf_app_db.get_app_group_detail(id=app_group_id)
        data = perf_app_handler.handle_app_group_detail_data(item=item)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete(
    "/delete_app",
    response_model=HttpResponse[bool],
    summary="删除应用"
)
async def delete_app(app_id: int):
    """
    删除应用
    Args:
        app_id: 应用ID
    Returns:
        删除结果
    """
    try:
        perf_app_db.delete_app(id=app_id)
        data = perf_app_handler.handle_delete_app(True)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete(
    "/delete_app_group",
    response_model=HttpResponse[bool],
    summary="删除应用组"
)
async def delete_app_group(app_group_id: int):
    """
    删除应用组
    Args:
        app_group_id: 应用组ID
    Returns:
        删除结果
    """
    try:
        perf_app_db.delete_app_group(id=app_group_id)
        data = perf_app_handler.handle_delete_app_group(True)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))
