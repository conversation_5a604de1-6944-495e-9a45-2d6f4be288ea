'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 19:13:27
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_data_router.py
Description: 性能数据相关路由模块
'''
from typing import Any, Union

from fastapi import APIRouter

from dals.db.perf_ui_auto.perf_data_db import perf_data_db
from handlers.perf_ui_auto import perf_data_handler
from schemas.request.perf_ui_auto.perf_data_req import *
from utils.common.response import HttpResponse, response_success, response_fail

router = APIRouter(
    prefix="/perf_data"
)

@router.post("/data_list", response_model=HttpResponse[Any], summary="分页查询性能测试数据列表")
async def get_report_data(params: PerfDataReportParams):
    try:
        data = perf_data_handler.get_report_data(params=params)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/perf_data_tos_url", response_model=HttpResponse[Any], summary="获取性能原始数据tos链接")
async def perf_data_tos_url(params: PerfDataTosUrlParams):
    try:
        items = perf_data_db.get_perf_data_tos_url(params=params)
        data = perf_data_handler.get_perf_data_tos_url(items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))

@router.put("/upload_perf_data", response_model=HttpResponse[Any], summary="上传性能测试数据")
async def upload_perf_data(params: PerfDataIn):
    """
    上传性能数据
    :param params: 性能数据参数
    :return: 上传的性能数据
    """
    try:
        # 从 metrics_data 中获取特定字段并进行处理
        if params.metrics_data.get("gpu_load") == 0:
            params.metrics_data["gpu_load"] = None
        if params.metrics_data.get("energy_location_cost") == 0:
            params.metrics_data["energy_location_cost"] = None
        if params.metrics_data.get("energy_thermal_cost") == 0:
            params.metrics_data["energy_thermal_cost"] = None
            
        perf_data_item = perf_data_db.upload_perf_data(params)
        if not perf_data_item:
            return response_fail(msg="上传性能数据失败")
        data = perf_data_handler.upload_perf_data("上传性能数据成功")
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))
