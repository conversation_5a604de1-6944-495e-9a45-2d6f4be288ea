'''
Author: hejiabei.oxep <EMAIL>
Date: 2025-01-16 19:31:54
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_stats_router.py
Description: 性能统计API路由
'''
from typing import Optional, Any
from datetime import date
from fastapi import APIRouter, Query, Depends

from handlers.perf_ui_auto.perf_stats_handler import perf_stats_handler
from schemas.request.perf_ui_auto.perf_stats_req import (
    BusinessCaseStatsRequest,
    TaskExecutionStatsRequest,
    CaseExecutionStatsRequest,
    BusinessCaseTrendRequest,
    TaskExecutionTrendRequest,
    CaseExecutionTrendRequest
)
from schemas.response.perf_ui_auto.perf_stats_res import (
    BusinessCaseStatsResponse,
    TaskExecutionStatsResponse,
    CaseExecutionStatsResponse,
    BusinessCaseTrendResponse,
    TaskExecutionTrendResponse,
    CaseExecutionTrendResponse
)
from utils.common.response import HttpResponse, response_success, response_fail
from utils.common.bytelog import byte_log

router = APIRouter(prefix="/stats", tags=["性能统计"])


@router.get("/business_cases", response_model=HttpResponse[BusinessCaseStatsResponse], summary="获取业务用例统计")
async def get_business_case_stats(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取业务用例统计数据"""
    try:
        request = BusinessCaseStatsRequest(
            business_id=business_id,
            start_date=start_date,
            end_date=end_date
        )
        
        data = perf_stats_handler.handle_business_case_stats(request)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取业务用例统计失败: {str(e)}")
        return response_fail(msg=str(e))


@router.get("/task_executions", response_model=HttpResponse[TaskExecutionStatsResponse], summary="获取任务执行统计")
async def get_task_execution_stats(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取任务执行统计数据"""
    try:
        request = TaskExecutionStatsRequest(
            business_id=business_id,
            start_date=start_date,
            end_date=end_date
        )
        
        data = perf_stats_handler.handle_task_execution_stats(request)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取任务执行统计失败: {str(e)}")
        return response_fail(msg=str(e))


@router.get("/case_executions", response_model=HttpResponse[CaseExecutionStatsResponse], summary="获取用例执行统计")
async def get_case_execution_stats(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线"),
    case_id: Optional[int] = Query(None, description="用例ID，为空时获取所有用例"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取用例执行统计数据"""
    try:
        request = CaseExecutionStatsRequest(
            business_id=business_id,
            case_id=case_id,
            start_date=start_date,
            end_date=end_date
        )
        
        data = perf_stats_handler.handle_case_execution_stats(request)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取用例执行统计失败: {str(e)}")
        return response_fail(msg=str(e))


@router.get("/business_cases/trend", response_model=HttpResponse[BusinessCaseTrendResponse], summary="获取业务用例趋势")
async def get_business_case_trend(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线"),
    days: int = Query(30, description="统计天数", ge=1, le=365)
):
    """获取业务用例趋势数据"""
    try:
        request = BusinessCaseTrendRequest(
            business_id=business_id,
            days=days
        )
        
        data = perf_stats_handler.handle_business_case_trend(request)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取业务用例趋势失败: {str(e)}")
        return response_fail(msg=str(e))


@router.get("/task_executions/trend", response_model=HttpResponse[TaskExecutionTrendResponse], summary="获取任务执行趋势")
async def get_task_execution_trend(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线"),
    days: int = Query(30, description="统计天数", ge=1, le=365)
):
    """获取任务执行趋势数据"""
    try:
        request = TaskExecutionTrendRequest(
            business_id=business_id,
            days=days
        )
        
        data = perf_stats_handler.handle_task_execution_trend(request)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取任务执行趋势失败: {str(e)}")
        return response_fail(msg=str(e))


@router.get("/case_executions/trend", response_model=HttpResponse[CaseExecutionTrendResponse], summary="获取用例执行趋势")
async def get_case_execution_trend(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线"),
    case_id: Optional[int] = Query(None, description="用例ID，为空时获取所有用例"),
    days: int = Query(30, description="统计天数", ge=1, le=365)
):
    """获取用例执行趋势数据"""
    try:
        request = CaseExecutionTrendRequest(
            business_id=business_id,
            case_id=case_id,
            days=days
        )
        
        data = perf_stats_handler.handle_case_execution_trend(request)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取用例执行趋势失败: {str(e)}")
        return response_fail(msg=str(e))


@router.get("/summary", response_model=HttpResponse[Any], summary="获取统计概览")
async def get_stats_summary(
    business_id: Optional[int] = Query(None, description="业务线ID，为空时获取所有业务线")
):
    """获取统计概览数据"""
    try:
        data = perf_stats_handler.get_stats_summary(business_id=business_id)
        return response_success(data=data)
    except Exception as e:
        byte_log.error(f"获取统计概览失败: {str(e)}")
        return response_fail(msg=str(e))
