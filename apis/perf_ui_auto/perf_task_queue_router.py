from fastapi import APIRouter, Query, Body, HTTPException
from typing import Optional, List
from dals.redis.perf_ui_auto.perf_task_redis import perf_task_redis
from utils.common.response import HttpResponse, response_success, response_fail
from utils.common.bytelog import byte_log
from schemas.response.perf_ui_auto.perf_task_res import PerfSubTaskRes

router = APIRouter(
    prefix="/perf_task_queue"
)

@router.get("/get_length", response_model=HttpResponse[int], summary="获取性能测试任务队列长度")
async def get_queue_length(client_id: int = Query(..., description="客户端ID", ge=1)):
    """获取指定客户端队列的长度"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        length = perf_task_redis.get_queue_length(client_id)
        return response_success(data=length)
    except Exception as e:
        byte_log.error(f"获取队列长度失败: {str(e)}")
        return response_fail(msg=f"获取队列长度失败: {str(e)}")

@router.delete("/clear", response_model=HttpResponse[bool], summary="清空性能测试任务队列")
async def clear_queue(client_id: int = Query(..., description="客户端ID", ge=1)):
    """清空指定客户端的队列"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        success = perf_task_redis.clear_queue(client_id)
        return response_success(data=success)
    except Exception as e:
        byte_log.error(f"清空队列失败: {str(e)}")
        return response_fail(msg=f"清空队列失败: {str(e)}")

@router.get("/get_head_sub_task", response_model=HttpResponse[Optional[PerfSubTaskRes]], summary="获取性能测试任务队列头部子任务")
async def get_head_sub_task(client_id: int = Query(..., description="客户端ID", ge=1)):
    """获取指定客户端队列头部的子任务（不移除）"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        sub_task = perf_task_redis.get_head_sub_task(client_id)
        try:
            return response_success(data=PerfSubTaskRes(**sub_task) if sub_task else None)
        except Exception as e:
            byte_log.error(f"子任务数据格式错误: {str(e)}")
            return response_fail(msg="子任务数据格式错误")
    except Exception as e:
        byte_log.error(f"获取队头子任务失败: {str(e)}")
        return response_fail(msg=f"获取队头子任务失败: {str(e)}")

@router.get("/get_all_sub_tasks", response_model=HttpResponse[List[PerfSubTaskRes]], summary="获取性能测试任务队列所有子任务")
async def get_all_sub_tasks(client_id: int = Query(..., description="客户端ID", ge=1)):
    """获取指定客户端队列中的所有子任务"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        sub_tasks = perf_task_redis.get_all_sub_tasks(client_id)
        try:
            return response_success(data=[PerfSubTaskRes(**task) for task in sub_tasks])
        except Exception as e:
            byte_log.error(f"子任务数据格式错误: {str(e)}")
            return response_fail(msg="子任务数据格式错误")
    except Exception as e:
        byte_log.error(f"获取所有子任务失败: {str(e)}")
        return response_fail(msg=f"获取所有子任务失败: {str(e)}")

@router.post("/push_sub_task", response_model=HttpResponse[bool], summary="添加子任务到性能测试任务队列")
async def push_sub_task(
    client_id: int = Query(..., description="客户端ID", ge=1),
    sub_task: PerfSubTaskRes = Body(..., description="子任务信息")
):
    """将子任务添加到指定客户端的队列尾部"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")

        # 检查队列中是否已存在相同ID的子任务
        if perf_task_redis.check_sub_task_exists(client_id, sub_task.id):
            byte_log.warning(f"客户端{client_id}队列中已存在ID为{sub_task.id}的子任务，跳过添加")
            return response_success(data=True, msg="子任务已存在，跳过添加")

        success = perf_task_redis.push_sub_task(client_id, sub_task.model_dump())
        return response_success(data=success)
    except Exception as e:
        byte_log.error(f"添加子任务失败: {str(e)}")
        return response_fail(msg=f"添加子任务失败: {str(e)}")

@router.delete("/remove_head_sub_task", response_model=HttpResponse[bool], summary="移除性能测试任务队列头部子任务")
async def remove_head_sub_task(client_id: int = Query(..., description="客户端ID", ge=1)):
    """移除指定客户端队列头部的子任务"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        success = perf_task_redis.remove_head_sub_task(client_id)
        return response_success(data=success)
    except Exception as e:
        byte_log.error(f"移除队头任务失败: {str(e)}")
        return response_fail(msg=f"移除队头任务失败: {str(e)}")

@router.put("/move_to_tail", response_model=HttpResponse[bool], summary="移动性能测试任务队列头部子任务到队尾")
async def move_to_tail(client_id: int = Query(..., description="客户端ID", ge=1)):
    """将指定客户端队列头部的子任务移到队尾"""
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        success = perf_task_redis.move_to_tail(client_id)
        return response_success(data=success)
    except Exception as e:
        byte_log.error(f"移动子任务到队尾失败: {str(e)}")
        return response_fail(msg=f"移动子任务到队尾失败: {str(e)}")

@router.delete("/remove_sub_tasks_by_task_id", response_model=HttpResponse[bool], summary="删除队列中指定任务的所有子任务")
async def remove_sub_tasks_by_task_id(
    client_id: int = Query(..., description="客户端ID", ge=1),
    task_id: int = Query(..., description="任务ID", ge=1)
):
    """
    从指定客户端的队列中删除特定任务ID的所有子任务数据
    
    Args:
        client_id: 客户端ID
        task_id: 要删除的任务ID
    
    Returns:
        HttpResponse[bool]: 删除操作是否成功
    """
    try:
        if not client_id:
            raise HTTPException(status_code=400, detail="无效的client_id")
            
        if not task_id:
            raise HTTPException(status_code=400, detail="无效的task_id")
            
        success = perf_task_redis.remove_sub_tasks_by_task_id(client_id, task_id)
        return response_success(data=success)
    except Exception as e:
        byte_log.error(f"删除任务{task_id}的子任务失败: {str(e)}")
        return response_fail(msg=f"删除任务{task_id}的子任务失败: {str(e)}")