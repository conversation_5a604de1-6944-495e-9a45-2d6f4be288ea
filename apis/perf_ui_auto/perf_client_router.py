'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_client_router.py
Description: 
'''
from typing import Union, Optional

from fastapi import APIRouter
from schemas.request.perf_ui_auto.perf_client_req import RegisterPerfClientParams, UpdateClientDetailParams
from schemas.response.perf_ui_auto.perf_client_res import PerfClientList, ClientItem
from utils.common.response import HttpResponse, response_success, response_fail
from dals.db.perf_ui_auto.perf_client_db import perf_client_db
from handlers.perf_ui_auto import perf_client_handler

router = APIRouter(
    prefix="/perf_client"
)

@router.get("/get_client_detail", response_model=HttpResponse[Union[ClientItem, None]], summary="根据客户端ID或MAC地址查询客户端详情")
async def get_client_detail(client_id: Optional[int] = None, mac_address: Optional[str] = None):
    try:
        if not client_id and not mac_address:
            return response_fail(msg="client_id和mac_address至少需要提供一个")
        
        # 查询数据库
        client = perf_client_db.get_client_detail(client_id, mac_address)
        if not client:
            return response_fail(msg=f"未找到相关的客户端记录")
        
        data = perf_client_handler.get_client_detail(client)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=f"获取客户端详情失败: {str(e)}")


@router.post("/register_perf_client", response_model=HttpResponse[Union[str, None]], summary="注册性能测试执行客户端")
async def register_perf_client(params: RegisterPerfClientParams):
    try:
        perf_client_db.register_perf_client(params)
        data = perf_client_handler.register_perf_client("注册客户端成功")
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/client_list", response_model=HttpResponse[Union[PerfClientList, None]], summary="获取性能测试客户端列表")
async def get_client_list():
    try:
        total, client_items_tuple = perf_client_db.get_perf_client_list()
        data = perf_client_handler.get_client_list(total, client_items_tuple)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.put("/update_client", response_model=HttpResponse[str], summary="更新性能测试客户端信息")
async def update_client(params: UpdateClientDetailParams):
    try:
        client_detail = perf_client_db.update_client(params)
        data = perf_client_handler.update_client_detail(client_detail)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.delete("/del_client", response_model=HttpResponse[Union[str, None]], summary="删除性能测试客户端")
async def del_client(client_id: int):
    try:
        perf_client_db.del_perf_client(client_id)
        return response_success(data="delete client success")
    except Exception as e:
        return response_fail(msg=str(e))
