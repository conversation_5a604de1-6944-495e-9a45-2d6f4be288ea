'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/apis/perf_ui_auto/perf_config_router.py
Description: 性能配置管理接口
'''
from typing import Optional
from fastapi import APIRouter, Query
from handlers.perf_ui_auto.perf_config_handler import perf_config_handler
from schemas.request.perf_ui_auto.perf_config_req import CreateConfigRequest, UpdateConfigRequest, ConfigListRequest
from schemas.response.perf_ui_auto.perf_config_res import PerfConfigListResponse, PerfConfigItem
from utils.common.response import HttpResponse, response_success, response_fail

router = APIRouter(
    prefix="/perf_config"
)

@router.post(
    "/config_list",
    response_model=HttpResponse[PerfConfigListResponse],
    summary="获取配置列表"
)
async def get_config_list(params: ConfigListRequest):
    """
    获取性能配置列表
    Args:
        params: 请求参数
            - config_name: 可选，配置名称
            - creator: 可选，创建者
            - start_time: 可选，开始时间
            - end_time: 可选，结束时间
            - page: 页码，默认1
            - page_size: 每页数量，默认10
    Returns:
        HttpResponse[PerfConfigListResponse]: 包含配置列表和总记录数的响应
    """
    try:
        result = perf_config_handler.get_all_configs(params)
        return response_success(data=result)
    except Exception as e:
        return response_fail(msg=str(e))

@router.get(
    "/config_detail",
    response_model=HttpResponse[Optional[PerfConfigItem]],
    summary="获取配置详情"
)
async def get_config_detail(config_id: int = Query(..., description="配置ID")):
    """
    获取单个配置详情
    Args:
        config_id: 配置ID
    Returns:
        HttpResponse[Optional[PerfConfigItem]]: 配置详情响应
    """
    try:
        result = perf_config_handler.get_config(config_id)
        if result is None:
            return response_fail(msg="配置不存在")
        return response_success(data=result)
    except Exception as e:
        return response_fail(msg=str(e))

@router.post(
    "/add_config",
    response_model=HttpResponse[Optional[PerfConfigItem]],
    summary="创建配置"
)
async def add_config(params: CreateConfigRequest):
    """
    添加新配置
    Args:
        params: 配置参数
            - config_name: 配置名称
            - metrics: 性能指标列表
            - creator: 创建者
    Returns:
        HttpResponse[Optional[PerfConfigItem]]: 创建结果响应
    """
    try:
        result = perf_config_handler.create_config(params)
        if result is None:
            return response_fail(msg="创建配置失败")
        return response_success(data=result, msg="创建成功")
    except Exception as e:
        return response_fail(msg=str(e))

@router.put(
    "/update_config",
    response_model=HttpResponse[Optional[PerfConfigItem]],
    summary="更新配置信息"
)
async def update_config(params: UpdateConfigRequest):
    """
    更新配置信息
    Args:
        params: 更新参数
            - id: 配置ID
            - config_name: 可选，配置名称
            - metrics: 可选，性能指标列表
    Returns:
        HttpResponse[Optional[PerfConfigItem]]: 更新结果响应
    """
    try:
        result = perf_config_handler.update_config(params)
        if result is None:
            return response_fail(msg="配置不存在或更新失败")
        return response_success(data=result, msg="更新成功")
    except Exception as e:
        return response_fail(msg=str(e))

@router.delete(
    "/delete_config",
    response_model=HttpResponse[bool],
    summary="删除配置"
)
async def delete_config(config_id: int = Query(..., description="配置ID")):
    """
    删除配置
    Args:
        config_id: 配置ID
    Returns:
        HttpResponse[bool]: 删除结果响应
    """
    try:
        result = perf_config_handler.delete_config(config_id)
        if not result:
            return response_fail(msg="配置不存在或删除失败")
        return response_success(data=result, msg="删除成功")
    except Exception as e:
        return response_fail(msg=str(e))
