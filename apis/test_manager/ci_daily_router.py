from typing import Any

from fastapi import APIRouter

from dals.db.test_manager.ci_daily_db import ci_daily_db
from handlers.test_manager import ci_daily_handler
from schemas.request.test_manager.ci_daily_req import *
from schemas.response.test_manager.ci_daily_res import *
from utils.common.depends import PageQuery
from utils.common.response import *

router = APIRouter(
    prefix="/ci_daily"
)


@router.post("/case_list", response_model=HttpResponse[Union[CaseList, None]], summary="获取用例列表")
async def get_case_list(page: PageQuery, params: GetCaseListParams):
    try:
        total, items = ci_daily_db.get_case_list(page=page, params=params)
        data = ci_daily_handler.get_case_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/branch_list", response_model=HttpResponse[Union[BranchList, None]], summary="获取分支列表")
async def get_branch_list(page: PageQuery, branch: str = ""):
    try:
        total, items = ci_daily_db.get_branch_list(page=page, branch=branch)
        data = ci_daily_handler.get_branch_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/result_code_list", response_model=HttpResponse[Union[ResultCodeList, None]], summary="获取错误码列表")
async def get_result_code_list(page: PageQuery, result_code: int = 0):
    try:
        total, items = ci_daily_db.get_result_code_list(page=page, result_code=result_code)
        data = ci_daily_handler.get_result_code_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/attribute_type_list", response_model=HttpResponse[Union[AttributeTypeList, None]], summary="获取归因类型列表")
async def get_attribute_type_list(page: PageQuery):
    try:
        total, items = ci_daily_db.get_attribute_type_list(page=page)
        data = ci_daily_handler.get_attribute_type_list_data(page=page, total=total, items=items)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/case_detail", response_model=HttpResponse[Union[CaseDetail, None]], summary="获取用例详情")
async def get_case_detail(test_case_detail_id: int):
    try:
        test_task_item, test_platform_item, test_case_detail_item = ci_daily_db.get_case_detail(test_case_detail_id)
        data = ci_daily_handler.get_case_detail_data(
            test_task_item=test_task_item,
            test_platform_item=test_platform_item,
            test_case_detail_item=test_case_detail_item
        )
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.put("/update_case_detail", response_model=HttpResponse[Union[UpdateCaseDetail, None]], summary="更新用例详情")
async def update_case_detail(params: UpdateCaseDetailParams):
    try:
        case_detail = ci_daily_db.update_case_detail(params)
        data = ci_daily_handler.update_case_detail_data(case_detail=case_detail)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.post("/get_ci_daily", response_model=HttpResponse[Any], summary="获取Ci和Daily的数据")
async def get_ci_daily(params: GetCiDailyDataParams):
    try:
        data = ci_daily_handler.get_ci_daily_data(params=params)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/del_repeat_ci_daily", response_model=HttpResponse[Any], summary="删除Ci和Daily的重复数据")
async def del_repeat_ci_daily():
    try:
        repeat_count = ci_daily_db.del_repeat_case()
        data = ci_daily_handler.del_repeat_ci_daily_data(repeat_count)
        return response_success(data=data)
    except Exception as e:
        return response_fail(msg=str(e))
