from fastapi import APIRouter, Query
from utils.common.response import HttpResponse, response_success, response_fail
from handlers.tool_manager.ffmpeg_stream_handler import ffmpeg_stream_handler
from schemas.request.tool_manager.ffmpeg_stream_req import PushStreamRequest
from schemas.response.tool_manager.ffmpeg_stream_res import (
    PushStreamResponse,
    StopStreamResponse,
    StartupStreamingResponse,
    ShutdownStreamingResponse
)

router = APIRouter(
    prefix="/ffmpeg_stream"
)

@router.get("/startup_streaming", response_model=HttpResponse[StartupStreamingResponse], summary="启动所有推流")
async def startup_streaming():
    """
    启动所有预配置的视频推流
    
    Returns:
        HttpResponse: 启动结果，包含所有推流进程的 ID
    """
    try:
        result = await ffmpeg_stream_handler.startup_streaming()
        response = StartupStreamingResponse(**result)
        return response_success(data=response.model_dump())
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/shutdown_streaming", response_model=HttpResponse[ShutdownStreamingResponse], summary="关闭所有推流")
async def shutdown_streaming():
    """
    关闭所有活动的推流
    
    Returns:
        HttpResponse: 关闭结果
    """
    try:
        result = await ffmpeg_stream_handler.shutdown_streaming()
        response = ShutdownStreamingResponse(**result)
        return response_success(data=response.model_dump())
    except Exception as e:
        return response_fail(msg=str(e))

@router.post("/push_stream", response_model=HttpResponse[PushStreamResponse], summary="FFmpeg 推流接口")
async def push_stream(request: PushStreamRequest):
    """
    FFmpeg 推流接口
    
    Args:
        request: 推流请求参数
        
    Returns:
        HttpResponse: 推流结果，包含进程 ID
    """
    try:
        result = await ffmpeg_stream_handler.push_stream(
            video_name=request.video_name.value,
            target_url=request.target_url,
            env=request.env
        )
        response = PushStreamResponse(**result)
        if result["status"] == "fail":
            return response_fail(msg=result["message"], data=response.model_dump())
        return response_success(data=response.model_dump())
    except Exception as e:
        return response_fail(msg=str(e))

@router.get("/stop_stream", response_model=HttpResponse[StopStreamResponse], summary="停止推流接口")
async def stop_stream(pid: int = Query(..., description="推流进程ID")):
    """
    停止 FFmpeg 推流
    
    Args:
        pid: 推流进程ID
        
    Returns:
        HttpResponse: 停止推流结果
    """
    try:
        result = await ffmpeg_stream_handler.stop_stream(pid)
        response = StopStreamResponse(**result)
        return response_success(data=response.model_dump())
    except Exception as e:
        return response_fail(msg=str(e))
