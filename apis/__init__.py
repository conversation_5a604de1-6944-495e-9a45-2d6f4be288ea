'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 19:13:27
FilePath: /global_rtc_test_platform/apis/__init__.py
Description: 
'''
from fastapi import APIRouter
from apis.tool_manager import ffmpeg_stream_router
from apis.perf_ui_auto import (
    perf_config_router,
    perf_client_router,
    perf_task_router,
    perf_device_router,
    perf_case_router,
    perf_app_router,
    perf_data_router,
    perf_metric_router,
    perf_account_router,
    perf_task_queue_router,
    perf_request_queue_router,
    perf_experiment_router,
    perf_stats_router
)
from apis.business import business_router
from apis.test_manager import ci_daily_router
from apis.version_manager import version_list_router
from apis.version_manager import version_detail_router
from apis.libra_experiment import libra_experiment_review_router
from proj_settings import deploy_cluster_env, DeployCluster

api_router = APIRouter()
if deploy_cluster_env in [None, DeployCluster.CN_BOE, DeployCluster.CN_ONLINE]:
    # 性能自动化
    api_router.include_router(perf_client_router.router, prefix="/perf_ui_auto", tags=["性能自动化-客户端"])
    api_router.include_router(perf_task_router.router, prefix="/perf_ui_auto", tags=["性能自动化-任务"])
    api_router.include_router(perf_device_router.router, prefix="/perf_ui_auto", tags=["性能自动化-设备"])
    api_router.include_router(perf_case_router.router, prefix="/perf_ui_auto", tags=["性能自动化-用例"])
    api_router.include_router(perf_app_router.router, prefix="/perf_ui_auto", tags=["性能自动化-APP"])
    api_router.include_router(perf_data_router.router, prefix="/perf_ui_auto", tags=["性能自动化-数据"])
    api_router.include_router(perf_metric_router.router, prefix="/perf_ui_auto", tags=["性能自动化-指标"])
    api_router.include_router(perf_account_router.router, prefix="/perf_ui_auto", tags=["性能自动化-账号"])
    api_router.include_router(perf_task_queue_router.router, prefix="/perf_ui_auto", tags=["性能自动化-任务队列"])
    api_router.include_router(perf_request_queue_router.router, prefix="/perf_ui_auto", tags=["性能自动化-请求队列"])
    api_router.include_router(perf_config_router.router, prefix="/perf_ui_auto", tags=["性能自动化-配置"])
    api_router.include_router(perf_experiment_router.router, prefix="/perf_ui_auto", tags=["性能自动化-实验"])
    api_router.include_router(perf_stats_router.router, prefix="/perf_ui_auto", tags=["性能自动化-统计"])

    # 工具管理
    api_router.include_router(ffmpeg_stream_router.router, prefix="/tool_manager", tags=["工具管理"])
    
    # 测试管理
    api_router.include_router(ci_daily_router.router, prefix="/test_manager", tags=["测试管理"])

    # 版本管理
    api_router.include_router(version_list_router.router, prefix="/version_manager/list", tags=["版本管理"])
    api_router.include_router(version_detail_router.router, prefix="/version_manager/detail", tags=["版本管理"])

    # libra实验review
    api_router.include_router(libra_experiment_review_router.router, prefix="/libra_experiment", tags=["libra实验review"])

    # 业务
    api_router.include_router(business_router.router, prefix="/business",tags=["业务"])

if deploy_cluster_env in [None, DeployCluster.VA_ONLINE]:
    pass