-- 数据库迁移脚本：修改 perf_ui_auto_case_run_detail 表结构
-- 执行时间：请在业务低峰期执行
-- 注意：执行前请备份数据库

-- 1. 添加新的 JSON 字段
ALTER TABLE perf_ui_auto_case_run_detail 
ADD COLUMN case_log_tos_urls JSON COMMENT '用例日志文件tos地址JSON格式';

ALTER TABLE perf_ui_auto_case_run_detail 
ADD COLUMN perf_data_tos_urls JSON COMMENT '性能数据文件tos地址JSON格式';

-- 2. 数据迁移：将原有的字符串字段转换为 JSON 格式
-- 将 case_log_tos_url 的值转换为 {"case.log": "原值"} 格式
UPDATE perf_ui_auto_case_run_detail 
SET case_log_tos_urls = JSON_OBJECT('case.log', case_log_tos_url)
WHERE case_log_tos_url IS NOT NULL AND case_log_tos_url != '';

-- 将 perf_data_tos_url 的值转换为 {"perf_data.json": "原值"} 格式
UPDATE perf_ui_auto_case_run_detail 
SET perf_data_tos_urls = JSON_OBJECT('perf_data.json', perf_data_tos_url)
WHERE perf_data_tos_url IS NOT NULL AND perf_data_tos_url != '';

-- 3. 删除旧字段
ALTER TABLE perf_ui_auto_case_run_detail 
DROP COLUMN case_log_tos_url;

ALTER TABLE perf_ui_auto_case_run_detail 
DROP COLUMN perf_data_tos_url;

ALTER TABLE perf_ui_auto_case_run_detail 
DROP COLUMN trace_data_tos_url;

ALTER TABLE perf_ui_auto_case_run_detail 
DROP COLUMN byteio_data_tos_url;

-- 验证迁移结果
-- 检查表结构
DESCRIBE perf_ui_auto_case_run_detail;

-- 检查数据样例
SELECT id, case_log_tos_urls, perf_data_tos_urls 
FROM perf_ui_auto_case_run_detail 
WHERE case_log_tos_urls IS NOT NULL OR perf_data_tos_urls IS NOT NULL 
LIMIT 5;
