# 获取当前目录
current_dir=$(pwd)

# 拼接temp文件夹的路径
temp_path="$current_dir/temp"

# 如果文件存在，最好删除一下再更新
if [ -d "$temp_path" ]; then
    cd "$current_dir"
    echo "已进入temp文件夹"
    rm -rf "$temp_path"

    mkdir "$temp_path"
    cd "$temp_path"
    echo "已经再次创建并进入temp文件夹"
    git clone https://yinyanting.2022:<EMAIL>/bytertc_i18n/global_business_perf.git

else
# 文件不存在，则clone,并且git init一下
    mkdir "$temp_path"
    cd "$temp_path"
    echo "已创建并进入temp文件夹"
    git clone https://yinyanting.2022:<EMAIL>/bytertc_i18n/global_business_perf.git

fi
cd "$current_dir"