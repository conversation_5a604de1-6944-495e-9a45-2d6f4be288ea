import os
import subprocess
from enum import Enum
from typing import List, Union

from pydantic.v1 import BaseSettings

# 部署集群环境
env = subprocess.run(["env"], capture_output=True).stdout.decode()
TCE_HOST_ENV: str = env.split("TCE_HOST_ENV=")[-1].split("\n")[0] if "TCE_HOST_ENV=" in env else None
TCE_ZONE: str = env.split("TCE_ZONE=")[-1].split("\n")[0] if "TCE_ZONE=" in env else None
MY_HOST_IP: str = env.split("MY_HOST_IP=")[-1].split("\n")[0] if "MY_HOST_IP=" in env else None


class DeployCluster(Enum):
    CN_BOE = "cn_boe"
    CN_ONLINE = "cn_online"
    VA_ONLINE = "va_online"


class Settings(BaseSettings):
    PROJECT_DESC: str = "Global Business 测试平台接口汇总"  # 描述
    PROJECT_VERSION: Union[int, str] = "1.0"  # 版本
    API_PREFIX: str = "/api"  # 接口前缀

    BASEDIR: str = os.path.abspath(os.path.dirname(__file__))
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    # uvicorn 服务
    V4_HOST: str = "0.0.0.0"
    V6_HOST: str = "::"
    HOST: str = V4_HOST if MY_HOST_IP else V6_HOST
    PORT: int = 8000
    RELOAD: bool = True

    # 数据库配置
    DATABASE_CHARSET: str = "utf8mb4"
    DATABASE_DB_PSM: str = "toutiao.mysql.global_streamer_test_platform_write"
    DATABASE_URL: str = f"mysql+pymysql://:@/?charset={DATABASE_CHARSET}&&db_psm={DATABASE_DB_PSM}"

    # Redis配置
    REDIS_DB: str = "0"
    REDIS_PSM: str = "toutiao.redis.global_streamer_test_platform"
    REDIS_SOCKET_CONNECT_TIMEOUT: str = "0.25"
    REDIS_TIMEOUT: str = "0.3"
    REDIS_URL: str = f"redis://?db={REDIS_DB}&redis_psm={REDIS_PSM}&socket_connect_timeout={REDIS_SOCKET_CONNECT_TIMEOUT}&socket_timeout={REDIS_TIMEOUT}"

    # TOS(OSS)配置
    TOS_ACCESS_KEY: str = "0HU2QRROXRUG1771DG14"
    TOS_BUCKET_NAME: str = "global-rtc-test-platform"
    TOS_PSM: str = "toutiao.tos.tosapi"
    TOS_CLUSTER: str = "default"
    TOS_IDC: str = "boe"

    # RocketMQ配置
    ROCKETMQ_PSM: str = "global.rtc.test_platform"
    ROCKETMQ_CLUSTER: str = "test_common4"
    ROCKETMQ_TOPIC: str = "global_business_perf_task_topic"
    ROCKETMQ_GROUP: str = "*"

    # 操作人名称和邮箱
    OPERATOR_NAME: str = "hejiabei.oxep"
    OPERATOR_EMAIL: str = "<EMAIL>"

    # 平台OpenAPI配置
    BITS_DOMAIN: str = "https://bits.bytedance.net"
    BITS_TOKEN: str = "Bearer 9741ebfc3f06d633945c70ba4dd129eb"

    VPASS_DOMAIN: str = "http://casebase.bytedance.net"

    MEEGO_PLUGIN_ID: str = "MII_66FA5BEDA449C002"
    MEEGO_PLUGIN_SECRET: str = "374C866EC6280AE5D284D221B0E574A9"
    MEEGO_DOMAIN: str = "https://meego.larkoffice.com"
    MEEGO_MY_USER_KEY: str = "7310035728690495489"

    GAME_PERF_DOMAIN: str = "http://127.0.0.1:3000"
    GAME_PERF_WS_DOMAIN: str = "ws://127.0.0.1:3000"

    TRACE_DOMAIN: str = "http://rtc-maliva.tiktok-row.org"

    LIBRA_DOMAIN: str = "https://sg-data.bytedance.net"
    LIBRA_APP_ID: str = "cli_4187a32cd41692"
    LIBRA_APP_SECRET: str = "N4fiFD1CA0AQGXPRSsAoAWrRXfytcyrm"

    LARK_DOMAIN: str = 'https://open.larkoffice.com'
    LARK_APP_ID: str = '********************'
    LARK_APP_SERCERT: str = '9d2lsc9xGGyWwUnqMUYFMbvwUuZhm2No'
    LARK_TOKEN: str = 'Bearer ******************************************'
    LARK_TOKEN_TENANT: str = 'Bearer ******************************************'
    LARK_CHECKLIST_CARD_ID: str = 'AAqHwHhbnZd04'
    LARK_LIBRA_REVIEW_RESULT_CARD_ID: str = 'AAq7RlfMJEqxK'
    LARK_PERF_TASK_STATUS_CARD_ID: str = 'AAqj2mGziIdWw'

    FRONT_END_HOST = 'https://gstest.sre-boe.bytedance.net'

    # 版本checklist额外需要通知的人、测试环境通知的人
    VERSION_CHECKLIST_EXTRA_REMINDER_EMAIL_LIST = ['<EMAIL>']
    VERSION_CHECKLIST_DEVELOPER_REMINDER_EMAIL_LIST = ['<EMAIL>']
    VOD_REVIEWER_EMAIL_LIST = ["xumingqiu", "maliming", "yaopengfei.yao", "leizihui", "dumanyi", "gunan", "zhangyue.68", "wuzhihao.wzh", "ganliying", "wangfang.flora", "yuanhuimin.ts", "zenghao.19941010", "maqin.mgim", "zhangxiyu.0807", "jiangkailong"]
    LIVE_REVIEWER_EMAIL_LIST = ["dongxiaoxia","lihan.harry","pengbili","xiaoyao.rhine","yinyanting.2022","maweijia.1211","guoxiaosheng","chenli.gogo","gengdongya","dengmingjie","sunzeyang.szy"]
    
    # 日志配置
    LOGGER_DIR: str = "logs"  # 日志文件夹名
    LOG_PATH: str = os.path.join(BASEDIR, LOGGER_DIR)  # 日志文件路径
    LOGGER_NAME: str = "{time:YYYY-MM-DD}.log"  # 日志文件名 (时间格式) {time:YYYY-MM-DD_HH-mm-ss}.log
    LOGGER_LEVEL: str = "DEBUG"  # 日志等级: ["DEBUG" | "INFO" ｜ "WARNING"]
    LOGGER_ROTATION: str = "00:00"  # 日志分片: 按 时间段/文件大小 切分日志. 例如 ["500 MB" | "12:00" | "1 week"]
    LOGGER_RETENTION: str = "30 days"  # 日志保留的时间: 超出将删除最早的日志. 例如 ["1 days"]


class CnBoeSettings(Settings):
    PORT: int = 8800 
    RELOAD: bool = False
    LOGGER_LEVEL: str = "DEBUG"  # 日志等级: ["DEBUG" | "INFO" ｜ "WARNING"]

    FRONT_END_HOST = 'https://gstest.sre-boe.bytedance.net'


class CnOnlineSettings(Settings):
    PORT: int = 9800
    RELOAD: bool = False

    LOGGER_LEVEL: str = "INFO"  # 日志等级: ["DEBUG" | "INFO" ｜ "WARNING"]
    LOG_PATH: str = "/opt/tiger/toutiao/log"  # 日志文件路径

    # TOS(OSS)配置
    TOS_ACCESS_KEY: str = "EX4GVMDFW7W20J5GC645"
    TOS_BUCKET_NAME: str = "global-rtc-test-platform"
    TOS_PSM: str = "toutiao.tos.tosapi"
    TOS_CLUSTER: str = "default"
    TOS_IDC: str = "CN-3DC"

    # RocketMQ配置
    ROCKETMQ_PSM = "global.rtc.test_platform"
    ROCKETMQ_CLUSTER = "web_common4"
    ROCKETMQ_TOPIC = "global_business_perf_task_topic"
    ROCKETMQ_GROUP = "*"

    FRONT_END_HOST = 'https://gstest.sre.bytedance.net'


class VaOnlineSettings(Settings):
    PORT: int = 10800
    RELOAD: bool = False

    LOGGER_LEVEL: str = "INFO"  # 日志等级: ["DEBUG" | "INFO" ｜ "WARNING"]
    LOG_PATH: str = "/opt/tiger/toutiao/log"  # 日志文件路径

    # TOS(OSS)配置
    TOS_ACCESS_KEY: str = "04J98XQ7WJJA7CMF0NMO"
    TOS_BUCKET_NAME: str = "global-rtc-test-platform-sg"
    TOS_PSM: str = "toutiao.tos.tosapi"
    TOS_CLUSTER: str = "default"
    TOS_IDC: str = "sg1"


settings = Settings()
deploy_cluster_env = None
if TCE_ZONE in ["BOE-Arbutus"] and TCE_HOST_ENV == "boe":
    deploy_cluster_env = DeployCluster.CN_BOE
    settings = CnBoeSettings()
elif TCE_ZONE in ["China-North"] and TCE_HOST_ENV == "online":
    deploy_cluster_env = DeployCluster.CN_ONLINE
    settings = CnOnlineSettings()
elif TCE_ZONE in ["Aliyun_VA"] and TCE_HOST_ENV == "online":
    deploy_cluster_env = DeployCluster.VA_ONLINE
    settings = VaOnlineSettings()
