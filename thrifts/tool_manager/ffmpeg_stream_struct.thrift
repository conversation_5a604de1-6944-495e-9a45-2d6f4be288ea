// 定义命名空间
namespace py tool_manager.ffmpeg_stream_struct

// 启动推流请求参数
struct StartupStreamingRequest {
}

// 启动推流响应结果
struct StartupStreamingResponse {
    1: required list<i32> pids; // 推流进程ID列表
    2: required string message; // 操作结果消息
}

// 关闭推流请求参数
struct ShutdownStreamingRequest {
}

// 关闭推流响应结果
struct ShutdownStreamingResponse {
    1: required bool success; // 是否成功关闭所有推流
    2: required string message; // 操作结果消息
}

// 推流请求参数
struct PushStreamRequest {
    1: required string video_name;
    2: required string target_url;
}

// 推流响应结果
struct PushStreamResponse {
    1: required i32 pid; // 推流进程ID
}

// 停止推流请求参数
struct StopStreamRequest {
    1: required i32 pid; // 推流进程ID
}

// 停止推流响应结果
struct StopStreamResponse {
    1: required bool success; // 停止推流是否成功
}