// 定义命名空间
namespace py libra_experiment_struct

// 定义请求参数数据类型
struct LibraExperimentParams {
  1: string business_name;
  2: string experiment_name;
  3: string experiment_creator;
  4: string experiment_reviewer_result;
  5: string experiment_app_ids;
  6: string layer_display_name;
  7: string experiment_description;
  8: i32 experiment_libra_id;
  9: string experiment_type;
  10: string experiment_status;
  11: double experiment_numerical_traffic;
  12: string experiment_tags;
  13: string experiment_start_time;
  14: i32 experiment_start_ts;
  15: string experiment_end_time;
  16: i32 experiment_end_ts;
  17: string experiment_libra_url;
  18: string experiment_create_time;
  19: i32 experiment_create_ts;
  20: string experiment_filter_rule;
  21: string experiment_enable_gradual;
  22: string experiment_meego_info;
  23: string experiment_version_info;
  24: string experiment_metrics_info;
  25: string experiment_qa_reviewer;
}


// 定义返回参数数据类型
struct LibraExperimentResponse {
  1: i32 id;
  2: i32 business_id;
  3: string experiment_name;
  4: string experiment_creator;
  5: string experiment_reviewer;
  6: string experiment_reviewer_result;
  7: string experiment_app_ids;
  8: string layer_display_name;
  9: string experiment_description;
  10: i32 experiment_libra_id;
  11: string experiment_type;
  12: string experiment_status;
  13: double experiment_numerical_traffic;
  14: string experiment_tags;
  15: string experiment_start_time;
  16: i32 experiment_start_ts;
  17: string experiment_end_time;
  18: i32 experiment_end_ts;
  19: string experiment_libra_url;
  20: string experiment_create_time;
  21: i32 experiment_create_ts;
  22: string experiment_filter_rule;
  23: string experiment_filter_rule_device_platform;
  24: string experiment_filter_rule_version_code;
  25: string experiment_filter_rule_priority_region;
  26: string experiment_enable_gradual;
  27: string experiment_meego_info;
  28: string experiment_meego_url;
  29: string experiment_version_info;
  30: string experiment_metrics_info;
  31: string create_time;
  32: string update_time;
  33: string experiment_qa_reviewer;
}

struct FindLatestCheckRuleParames {
  1: i32 business_id
}

struct AddLibraExperimentRuleParams {
  1: required i32 business_id
  2: required i32 libra_experiment_field_meta_id (description="实验规则元数据id")
  3: required i32 operator_type (description="规则类型")
  4: required i32 is_check_value (description="是否检查值本身")
  5: required string expect_value (description="期望的值")
  6: required i32 is_must (description="是否必须通过")
  7: required i32 version_code (description="规则版本号")
  8: required i32 is_using (description="是否启用")
}

struct UpdateLibraExperimentRuleParams {
  1: required i32 id (description="规则id")
  2: optional i32 business_id (description="business id")
  3: optional i32 libra_experiment_field_meta_id (description="实验规则元数据id")
  4: optional i32 operator_type (description="规则类型")
  5: optional i32 is_check_value (description="是否检查值本身")
  6: optional string expect_value (description="期望的值")
  7: optional i32 is_must (description="是否必须通过")
  8: optional i32 version_code (description="规则版本号")
  9: optional i32 is_using (description="是否启用")
}