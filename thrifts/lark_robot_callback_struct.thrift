// 定义命名空间
namespace py lark_robot_callback_struct

// 定义请求参数数据类型
struct Header {
  1: string event_id;
  2: string token;
  3: string create_time;
  4: string event_type;
  5: string tenant_key;
  6: string app_id;
}

struct Operator {
  1: string tenant_key;
  2: string user_id;
  3: string open_id;
}

struct Action {
  1: string value;  //格式是object
  2: string tag;
  3: string timezone;
  4: string form_value; //格式是object
  5: string name;
}

struct Context {
  1: string url;
  2: string preview_token;
  3: string open_message_id;
  4: string open_chat_id;
}

struct Event {
  1: Operator operator;
  2: string token;
  3: Action action;
  4: string host;
  5: string host;
  6: string delivery_type;
  7: Context context;
}

// 组合请求参数
struct CallbackParams {
  1: string schema;
  2: Header header;
  3: Event event;
  4: string challenge;
  5: string token;
  6: string type;
}


// 定义返回参数数据类型
struct I18n {
  1: string zh_cn;
  2: string en_us;
}

struct Toast {
  1: string type;
  2: string content;
  3: I18n i18n;
}

struct Config {
  1: bool enable_forward;
}

struct ElementsText {
  1: string content;
  2: string tag;
}

struct Elements {
  1: string tag;
  2: ElementsText text;
}

struct HeaderTitle {
  1: string content;
  2: string tag;
}

struct Header {
  1: string template;
  2: HeaderTitle title;
}

struct CardData {
  1: Config config;
  2: Elements elements;
  3: Header header;
}

struct Card {
  1: string type;
  2: CardData data;
}

// 组合返回参数
struct CallbackResponse {
  1: string challenge;
  2: Toast toast;
  3: Card card;
}