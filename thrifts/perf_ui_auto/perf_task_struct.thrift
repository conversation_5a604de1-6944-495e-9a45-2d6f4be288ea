namespace py perf_ui_auto.perf_task_struct

// 获取任务列表的参数
struct PerfTaskListParams {
    1: optional list<i32> business_id (description="业务线ID列表")
    2: optional list<i32> client_id (description="客户端ID列表")
    3: optional list<i32> status (description="任务状态列表")
    4: optional list<i32> perf_tool_type (description="性能采集工具类型列表")
    5: optional list<string> task_name (description="任务名称列表")
    6: optional list<string> owner (description="任务负责人列表")
}

// 创建子任务的参数
struct CreatePerfSubTaskParams {
    1: required string name (description="子任务名称", maxLength="255")
    2: required i32 platform (description="平台 1-Android 2-ios")
    3: required list<i32> case_id_list (description="用例ID列表")
    4: required i32 perf_device_id (description="性能设备ID")
    5: required i32 app_group_id (description="APP组ID")
    6: optional i32 perf_account_id (description="性能账号ID")
    7: required string video_url (description="视频地址", maxLength="1000")
    8: required i32 app_install_type (description="安装方式 0-不安装 1-卸载安装 2-覆盖安装")
    9: optional list<string> finish_notice (description="子任务完成通知列表")
    10: required list<i32> perf_collect_type_list (description="性能采集类型列表")
    11: required i32 perf_collect_duration (description="性能采集等待时长（s）")
    12: required i32 perf_collect_interval (description="性能采集间隔（ms）")
    13: required i32 perf_collect_mode (description="性能采集方式 1-有线连接采集 2-无线连接采集")
    14: required i32 perf_device_power_level (description="性能设备电量最低值百分比（%）")
    15: required i32 case_run_count (description="单条用例运行次数")
    16: required i32 case_retry_count (description="用例失败重试次数")
    17: required bool enabled (description="是否启用")
}

// 创建任务的参数
struct CreatePerfTaskParams {
    1: required string name (description="任务名称", maxLength="128")
    2: required i32 business_id (description="业务线ID")
    3: required i32 client_id (description="客户端ID")
    4: required string owner (description="任务负责人", maxLength="64")
    5: required i32 type (description="任务类型 1-版本回归 2-Libra实验")
    6: optional i32 perf_tool_type (description="性能采集工具类型 1-DS 2-GamePerf")
    7: optional i32 config_id (description="性能配置ID")
    8: optional i32 experiment_id (description="实验ID")
    9: required list<CreatePerfSubTaskParams> sub_tasks (description="子任务列表")
}

// 更新子任务的参数
struct UpdatePerfSubTaskParams {
    1: optional i32 id (description="子任务ID")
    2: optional i32 task_id (description="任务ID")
    3: optional string name (description="子任务名称", maxLength="255")
    4: optional i32 platform (description="平台 1-Android 2-ios")
    5: optional list<i32> case_id_list (description="用例ID列表")
    6: optional i32 perf_device_id (description="性能设备ID")
    7: optional i32 app_group_id (description="APP组ID")
    8: optional i32 perf_account_id (description="性能账号ID")
    9: optional string video_url (description="视频地址", maxLength="1000")
    10: optional i32 app_install_type (description="安装方式 0-不安装 1-卸载安装 2-覆盖安装")
    11: optional list<string> finish_notice (description="子任务完成通知")
    12: optional list<i32> perf_collect_type_list (description="性能采集类型列表")
    13: optional i32 perf_collect_duration (description="性能采集等待时长（s）")
    14: optional i32 perf_collect_interval (description="性能采集间隔（ms）")
    15: optional i32 perf_collect_mode (description="性能采集方式 1-有线连接采集 2-无线连接采集")
    16: optional i32 perf_device_power_level (description="性能设备电量最低值百分比（%）")
    17: optional i32 case_run_count (description="单条用例运行次数")
    18: optional i32 case_retry_count (description="用例失败重试次数")
    19: optional bool enabled (description="是否启用")
    20: optional i32 status (description="子任务状态")
    21: optional i32 result (description="子任务结果")
    22: optional i32 duration (description="子任务执行时长")
    23: optional string start_time (description="开始时间")
    24: optional string end_time (description="结束时间")
    25: optional string create_time (description="创建时间")
    26: optional string update_time (description="更新时间")
}

// 取消任务的参数
struct CancelPerfTaskParams {
    1: required i32 task_id (description="任务ID")
    2: required i32 status (description="任务状态")
    3: required i32 client_id (description="客户端ID")
}

// 更新任务的参数
struct UpdatePerfTaskParams {
    1: required i32 id (description="任务ID")
    2: optional string name (description="任务名称", maxLength="128")
    3: optional i32 business_id (description="业务线ID")
    4: optional i32 client_id (description="客户端ID")
    5: optional string owner (description="任务负责人", maxLength="64")
    6: optional i32 type (description="任务类型 1-版本回归 2-Libra实验")
    7: optional i32 perf_tool_type (description="性能采集工具类型 1-DS 2-GamePerf")
    8: optional i32 config_id (description="性能配置ID")
    9: optional i32 experiment_id (description="实验ID")
    10: optional i32 status (description="任务状态")
    11: optional i32 duration (description="任务执行时长")
    12: optional list<UpdatePerfSubTaskParams> sub_tasks (description="子任务列表")
    13: optional string start_time (description="开始时间")
    14: optional string end_time (description="结束时间")

}

// 上传用例运行详情的参数
struct UploadPerfCaseRunDetailParams {
    1: optional i32 id (description="ID")
    2: optional i32 sub_task_id (description="子任务ID")
    3: optional i32 device_id (description="设备ID")
    4: optional i32 app_id (description="应用ID")
    5: optional i32 case_id (description="用例ID")
    6: optional map<string, string> case_log_tos_urls (description="用例日志TOS地址JSON格式")
    7: optional map<string, string> perf_data_tos_urls (description="性能数据TOS地址JSON格式")
    8: optional list<string> screenshot_tos_urls (description="截图TOS地址列表")
    9: optional string cpu_profile_tos_url (description="CPU性能分析数据TOS链接")
    12: optional i32 status (description="状态")
    13: optional i32 version_type (description="版本类型 1-实验组 2-对照组")
}

// 更新子任务状态的参数
struct UpdateSubTaskStatusParams {
    1: required i32 id (description="子任务ID")
    2: optional i32 status (description="子任务状态")
    3: optional string start_time (description="开始时间")
    4: optional string end_time (description="结束时间")
    5: optional i32 error_code (description="错误码")
    6: optional string error_title (description="错误标题")
    7: optional string error_detail (description="错误详情")
}

// 更新任务状态的参数
struct UpdateTaskStatusParams {
    1: required i32 id (description="任务ID")
    2: optional i32 status (description="任务状态")
    3: optional string start_time (description="开始时间")
    4: optional string end_time (description="结束时间")
}

// 单个用例的执行统计
struct CaseExecutionStatsItem {
    1: required i32 case_id (description="用例ID")
    2: required i32 total_count (description="总执行次数")
    3: required i32 success_count (description="成功次数")
    4: required i32 failed_count (description="失败次数")
    5: required i32 timeout_count (description="超时次数")
    6: required i32 canceled_count (description="取消次数")
    7: required i32 retry_count (description="重试次数")
}

// 应用的执行统计
struct AppExecutionStatsItem {
    1: required i32 app_id (description="应用ID")
    2: required list<CaseExecutionStatsItem> cases (description="用例统计列表")
}

// 用例执行统计列表响应
struct CaseExecutionStatsListRes {
    1: optional i32 total_cases = 0 (description="用例总数")
    2: optional i32 total_executions = 0 (description="总执行次数")
    3: optional double success_rate = 0.0 (description="总体成功率")
    4: optional list<AppExecutionStatsItem> app_stats (description="应用执行统计列表")
}

// 获取任务列表的参数
struct GetTaskListRequestParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: required PerfTaskListParams params (description="任务列表查询参数")
}

// 获取任务详情的参数
struct GetTaskDetailParams {
    1: required i32 task_id (description="任务ID")
}

// 获取任务完整详情的参数
struct GetTaskDetailAllParams {
    1: required i32 task_id (description="任务ID")
}

// 复制任务的参数
struct CopyTaskParams {
    1: required i32 task_id (description="要复制的任务ID")
    2: required string operator (description="操作人", maxLength="64")
}

// 删除任务的参数
struct DeleteTaskParams {
    1: required i32 task_id (description="要删除的任务ID")
}

// 启动任务的参数
struct StartTaskParams {
    1: required i32 task_id (description="要启动的任务ID")
}

// 重试任务的参数
struct RetryTaskParams {
    1: required i32 task_id (description="要重试的任务ID")
}

// 获取任务进度的参数
struct GetTaskProgressParams {
    1: required i32 task_id (description="任务ID")
}

// 获取子任务用例运行详情的参数
struct GetSubTaskCaseRunDetailParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: required i32 sub_task_id (description="子任务ID")
}

// 获取用例运行列表的参数
struct GetCaseExecutionStatsParams {
    1: required i32 sub_task_id (description="子任务ID")
}