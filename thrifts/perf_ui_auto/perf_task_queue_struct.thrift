namespace py perf_ui_auto.perf_task_queue_struct

// 子任务响应结构体
struct PerfSubTaskRes {
    1: required i32 id (description="子任务ID")
    2: required i32 task_id (description="任务ID")
    3: required string name (description="子任务名称")
    4: required i32 platform (description="平台 1-Android 2-ios")
    5: required list<i32> case_id_list (description="用例id列表")
    6: required i32 perf_device_id (description="性能设备id")
    7: optional i32 perf_account_id (description="性能账号id")
    7: required i32 app_group_id (description="APP组id")
    8: required string video_url (description="视频地址")
    9: required i32 perf_collect_mode (description="性能采集方式 1-有线连接采集 2-无线连接采集")
    10: required i32 app_install_type (description="安装方式 0-不安装 1-卸载安装 2-覆盖安装")
    11: optional list<string> finish_notice (description="子任务完成通知")
    12: required list<i32> perf_collect_type_list (description="性能采集类型列表")
    13: required i32 perf_collect_duration (description="性能采集等待时长（s）")
    14: required i32 perf_collect_interval (description="性能采集间隔（ms）")
    15: required i32 perf_device_power_level (description="性能设备电量最低值百分比（%）")
    16: required i32 case_run_count (description="单条用例运行次数")
    17: required i32 case_retry_count (description="用例失败重试次数")
    18: required bool enabled (description="是否启用 0-否 1-是（默认启用）")
    19: required i32 status (description="子任务状态 0-待执行 1-执行中 2-执行完成 3-执行失败")
    20: required i32 result (description="子任务结果 0-无结果 1-通过 2-不通过")
    21: required i32 duration (description="子任务运行时长（单位秒s）")
    22: optional string start_time (description="子任务开始时间")
    23: optional string end_time (description="子任务结束时间")
    24: required string create_time (description="创建时间")
    25: required string update_time (description="更新时间")
}

// 获取队列长度的参数
struct GetQueueLengthParams {
    1: required i32 client_id (description="客户端ID")
}

// 清空队列的参数
struct ClearQueueParams {
    1: required i32 client_id (description="客户端ID")
}

// 获取队头子任务的参数
struct GetHeadSubTaskParams {
    1: required i32 client_id (description="客户端ID")
}

// 获取所有子任务的参数
struct GetAllSubTasksParams {
    1: required i32 client_id (description="客户端ID")
}

// 添加子任务的参数
struct PushSubTaskParams {
    1: required i32 client_id (description="客户端ID")
    2: required PerfSubTaskRes sub_task (description="子任务信息")
}

// 移除队头子任务的参数
struct RemoveHeadSubTaskParams {
    1: required i32 client_id (description="客户端ID")
}

// 移动到队尾的参数
struct MoveToTailParams {
    1: required i32 client_id (description="客户端ID")
}

// 批量获取队列状态的参数
struct BatchGetQueueStatusParams {
    1: required list<i32> client_ids (description="客户端ID列表")
}

// 队列操作响应
struct QueueOperationRes {
    1: required bool success (description="操作是否成功")
    2: optional string message (description="错误信息")
}

// 队列状态响应
struct QueueStatusRes {
    1: required i32 length (description="队列长度")
    2: optional PerfSubTaskRes head_task (description="队头任务")
}

// 删除指定任务子任务的参数
struct RemoveSubTasksByTaskIdParams {
    1: required i32 client_id (description="客户端ID")
    2: required i32 task_id (description="任务ID")
}