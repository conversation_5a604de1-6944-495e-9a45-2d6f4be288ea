namespace py perf_ui_auto.perf_device_struct

// 获取设备列表的参数
struct GetDeviceListParams {
    1: optional i32 client_id (description="客户端ID")
    2: optional i32 sys_type (description="系统类型,1:Android,2:iOS")
    3: optional i32 is_occupied (description="是否被占用,0:未占用,1:已占用")
}

// 上传设备信息的参数
struct UploadPerfDeviceParams {
    1: required string name (description="设备名称")
    2: required string udid (description="设备唯一标识符")
    3: required string model (description="型号")
    4: required i32 sys_type (description="系统类型 1-Android 2-iOS")
    5: required string sys_version (description="系统版本")
    6: required string brand (description="品牌")
    7: required string resolution (description="分辨率")
    8: required list<i32> connect_type (description="连接类型列表 [1-USB, 2-WiFi]")
    9: required i32 state (description="设备状态 0-离线 1-在线")
    10: optional string ip (description="IP地址")
    11: optional string serial_port (description="串口号")
    12: optional string user (description="使用者")
    13: optional string owner (description="所有者")
}

// 获取设备列表的请求参数
struct GetDeviceListRequestParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: required GetDeviceListParams params (description="设备列表查询参数")
}

// 获取设备详情的参数
struct GetDeviceDetailParams {
    1: required i32 device_id (description="设备ID")
}

// 获取可用设备的参数
struct GetAvailableDevicesParams {
    1: required i32 sys_type (description="系统类型")
    2: required i32 count (description="需要的设备数量")
}

// 更新设备占用状态的参数
struct UpdateDeviceOccupiedParams {
    1: required i32 device_id (description="设备ID")
    2: required i32 is_occupied (description="是否被占用 0-未占用 1-已占用")
}

// 批量更新设备占用状态的参数
struct BatchUpdateDeviceOccupiedParams {
    1: required list<i32> device_ids (description="要更新的设备ID列表")
    2: required i32 is_occupied (description="是否被占用,0:未占用,1:已占用")
}

// 上传设备信息的请求参数
struct UploadPerfDeviceRequestParams {
    1: required i32 client_id (description="客户端ID")
    2: required list<UploadPerfDeviceParams> device_list (description="设备列表")
}

// 删除设备的参数
struct DeleteDeviceParams {
    1: required i32 device_id (description="设备ID")
}