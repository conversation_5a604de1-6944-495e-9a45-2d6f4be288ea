namespace py perf_ui_auto.perf_request_queue_struct

// 获取所有请求的参数
struct GetAllRequestsParams {
    1: required i32 client_id (description="客户端ID")
}

// 清空队列的参数
struct ClearRequestsParams {
    1: required i32 client_id (description="客户端ID")
}

// 请求数据结构体
struct PerfRequestData {
    1: required string event_key (description="事件类型")
    2: optional map<string, string> request_body (description="请求体")
    3: optional string request_time (description="请求时间")
}

// 获取所有请求的响应
struct GetAllRequestsRes {
    1: required list<PerfRequestData> requests (description="请求列表")
}
