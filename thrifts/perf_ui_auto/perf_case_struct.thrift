namespace py perf_ui_auto.perf_case_struct

// 查询用例列表的参数
struct CaseListParams {
    1: optional string case_name (description="用例名称", maxLength="64")
    2: optional i32 sys_type (description="系统类型,1:Android,2:iOS")
    3: optional i32 status (description="用例状态,0:禁用,1:启用")
}

// 更新用例的参数
struct UpdateCaseParams {
    1: required i32 case_id (description="用例ID")
    2: optional string case_name (description="用例名称", maxLength="64")
    3: optional i32 sys_type (description="系统类型,1:Android,2:iOS")
    4: optional string description (description="用例描述", maxLength="256")
}

// 查询用例列表的请求参数
struct GetCaseListParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: required CaseListParams params (description="用例列表查询参数")
}

// 查询用例详情的参数
struct GetCaseDetailParams {
    1: required i32 case_id (description="用例ID")
}

// 批量更新用例状态的参数
struct BatchUpdateCaseStatusParams {
    1: required list<i32> case_ids (description="要更新的用例ID列表")
    2: required i32 status (description="更新后的状态,0:禁用,1:启用")
}