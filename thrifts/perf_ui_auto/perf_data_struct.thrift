namespace py perf_ui_auto.perf_data_struct

// 基础性能数据结构
struct PerfDataBase {
    1: optional i32 id (description="数据ID")
    2: optional bool is_avg = false (description="是否为平均值")
    3: optional i32 case_run_detail_id (description="用例运行详情ID")
    4: optional i32 platform (description="平台类型")
    5: optional map<string, double> metrics_data (description="性能指标数据")
}

// Android性能数据参数
struct UploadAndroidPerfDataParams {
    1: required PerfDataBase base_info (description="基础性能数据")
    2: optional double cpu_normalized_total_usage (description="CPU总体标准化使用率", min="0.0", max="100.0")
    3: optional double cpu_normalized_proc_usage (description="CPU进程标准化使用率", min="0.0", max="100.0")
    4: optional double gpu_usage (description="GPU使用率", min="0.0", max="100.0")
    5: optional double gpu_load (description="GPU负载", min="0.0")
    6: optional double mem_pss (description="PSS内存", unit="MB")
    7: optional double mem_uss (description="USS内存", unit="MB")
    8: optional double mem_dalvik_heap (description="Dalvik堆内存", unit="MB")
    9: optional double mem_dalvik_other (description="Dalvik其他内存", unit="MB")
    10: optional double battery_current (description="电池电流", unit="mA")
    11: optional double net_total_receive (description="总网络接收", unit="KB/s")
    12: optional double net_total_sent (description="总网络发送", unit="KB/s")
    13: optional double fd_count (description="文件描述符数量")
}

// iOS性能数据参数
struct UploadIOSPerfDataParams {
    1: required PerfDataBase base_info (description="基础性能数据")
    2: optional double cpu_total_usage (description="CPU总使用率", min="0.0", max="100.0")
    3: optional double cpu_proc_usage (description="CPU进程使用率", min="0.0", max="100.0")
    4: optional double gpu_device (description="GPU设备使用率", min="0.0", max="100.0")
    5: optional double gpu_render (description="GPU渲染使用率", min="0.0", max="100.0")
    6: optional double gpu_tiler (description="GPU Tiler使用率", min="0.0", max="100.0")
    7: optional double mem_total_footprint (description="总内存占用", unit="MB")
    8: optional double mem_resident (description="常驻内存", unit="MB")
    9: optional double mem_footprint (description="内存占用", unit="MB")
    10: optional double battery_temp (description="电池温度", unit="℃")
    11: optional double battery_amperage (description="电池电流", unit="mA")
    12: optional double ctx_switch (description="上下文切换次数")
    13: optional double int_wakeups (description="中断唤醒次数")
    14: optional double energy_cost (description="总能耗", unit="mAh")
    15: optional double energy_cpu_cost (description="CPU能耗", unit="mAh")
    16: optional double energy_gpu_cost (description="GPU能耗", unit="mAh")
    17: optional double energy_net_cost (description="网络能耗", unit="mAh")
    18: optional double energy_location_cost (description="定位能耗", unit="mAh")
    19: optional double energy_thermal_cost (description="散热能耗", unit="mAh")
    20: optional double energy_overhead_cost (description="其他能耗", unit="mAh")
}

// 性能数据报告参数
struct PerfDataReportParams {
    1: required i32 id (description="数据ID")
}

// 性能数据TOS链接参数
struct PerfDataTosUrlParams {
    1: required i32 sub_task_id (description="子任务ID")
    2: required i32 case_id (description="用例ID")
    3: required i32 app_id (description="应用ID")
}

// 查询版本列表的请求参数
struct GetDataVersionListParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: optional string version (description="版本号过滤", maxLength="32")
}

// 上传性能数据的参数
struct UploadPerfDataParams {
    1: required i32 case_run_detail_id (description="用例运行详情ID")
    2: required bool is_avg = false (description="是否为平均值")
    3: required i32 platform (description="平台类型")
    4: required map<string, double> metrics_data (description="性能指标数据")
}

// 查询性能数据统计的参数
struct GetPerfDataStatsParams {
    1: required i32 case_id (description="用例ID")
    2: required string start_time (description="统计开始时间,格式:yyyy-MM-dd HH:mm:ss")
    3: required string end_time (description="统计结束时间,格式:yyyy-MM-dd HH:mm:ss")
}

// 批量上传性能数据的参数
struct BatchUploadPerfDataParams {
    1: required list<UploadAndroidPerfDataParams> android_params (description="批量Android性能数据")
    2: required list<UploadIOSPerfDataParams> ios_params (description="批量iOS性能数据")
}