namespace py perf_ui_auto.perf_client_struct

// 注册性能测试客户端的参数
struct RegisterPerfClientParams {
    1: required i32 client_id (description="客户端ID")
    2: required i32 business_id (description="业务线id")
    3: optional string name (description="客户端名称", maxLength="64")
    4: optional i32 sys_type (description="系统类型")
    5: required string mac_address (description="MAC地址", pattern="^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")
    6: optional string ipv4 (description="ipv4")
    7: optional string ipv6 (description="ipv6")
    8: optional i32 port (description="端口")
    9: required i32 state (description="状态 0-离线 1-在线")
    10: required string owner (description="负责人", maxLength="64")
}


// 更新客户端的参数
struct UpdateClientDetailParams {
    1: required i32 client_id (description="客户端ID")
    2: optional i32 business_id (description="业务线id")
    3: optional string name (description="客户端名称", maxLength="64")
    4: optional i32 sys_type (description="系统类型")
    5: optional string mac_address (description="MAC地址", pattern="^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")
    6: optional string ipv4 (description="ipv4")
    7: optional string ipv6 (description="ipv6")
    8: optional i32 port (description="端口")
    9: optional i32 state (description="状态 0-离线 1-在线")
    10: optional string owner (description="负责人", maxLength="64")
}

// 获取客户端详情的参数
struct GetClientDetailParams {
    1: optional i32 client_id (description="客户端ID")
    2: optional string mac_address (description="MAC地址", pattern="^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")
}

// 删除客户端的参数
struct DeleteClientParams {
    1: required i32 client_id (description="要删除的客户端ID")
}