namespace py perf_ui_auto.perf_app_struct

// 获取应用列表的参数
struct GetAppListParams {
    1: optional i32 id (description="应用ID")
    2: optional i32 business_id (description="业务ID") 
    3: optional i32 platform (description="平台类型,1:Android,2:iOS")
    4: optional string name (description="应用名称")
    5: optional string version (description="版本号")
    6: optional i32 app_type (description="应用类型")
}

// 获取应用列表的请求参数
struct GetAppListRequestParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: required GetAppListParams params (description="应用列表查询参数")
}

// 添加应用的参数
struct AddAppParams {
    1: required i32 business_id (description="业务ID")
    2: required i32 platform (description="平台类型,1:Android,2:iOS")
    3: required string version (description="版本号")
    4: required i32 app_type (description="应用类型")
    5: required string url (description="应用下载地址")
    6: optional string jenkins_build_result_url (description="Jenkins构建结果地址")
    7: optional string repackage_cert (description="重打包证书")
    8: required string creator (description="创建者")
}

// 更新应用的参数
struct UpdateAppParams {
    1: required i32 id (description="应用ID")
    2: optional string app_name (description="应用名称")
    3: optional string version (description="版本号")
    4: optional i32 sys_type (description="系统类型,1:Android,2:iOS")
    5: optional string description (description="应用描述")
}

// 查询应用分组列表的参数
struct GetAppGroupListParams {
    1: optional string version (description="版本名称")
    2: optional i32 business_id (description="业务id")
    3: optional string app_group_name (description="应用分组名称")
}

// 添加应用分组的参数
struct AddAppGroupParams {
    1: required string group_name (description="分组名称")
    2: required i32 sys_type (description="系统类型,1:Android,2:iOS")
    3: required list<i32> app_ids (description="应用ID列表")
    4: required string creator (description="创建者")
}

// 更新应用分组的参数
struct UpdateAppGroupParams {
    1: required i32 id (description="分组ID")
    2: optional string group_name (description="分组名称")
    3: optional i32 sys_type (description="系统类型,1:Android,2:iOS")
    4: optional list<i32> app_ids (description="应用ID列表")
}

// 新增请求参数
struct GetAppVersionListParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: optional string version (description="版本号", maxLength="32")
}

// 查询应用组列表的请求参数
struct GetAppGroupListRequestParams {
    1: required i32 page (description="页码")
    2: required i32 page_size (description="每页大小")
    3: required GetAppGroupListParams params (description="应用组列表查询参数")
}

// 查询应用组详情的参数
struct GetAppGroupDetailParams {
    1: required i32 app_group_id (description="应用组ID")
}

// 删除应用的参数
struct DeleteAppParams {
    1: required i32 id (description="应用ID")
}

// 删除应用组的参数
struct DeleteAppGroupParams {
    1: required i32 id (description="应用组ID")
}