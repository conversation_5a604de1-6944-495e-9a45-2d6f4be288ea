namespace py perf_ui_auto.perf_stats_struct

// 性能统计分析模块 Thrift 接口定义
// 提供业务用例统计、任务执行统计、用例执行统计等功能
// 支持实时统计和趋势分析

// 业务用例统计请求参数
struct BusinessCaseStatsRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
    2: optional string start_date (description="开始日期，格式：yyyy-MM-dd")
    3: optional string end_date (description="结束日期，格式：yyyy-MM-dd")
}

// 任务执行统计请求参数
struct TaskExecutionStatsRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
    2: optional string start_date (description="开始日期，格式：yyyy-MM-dd")
    3: optional string end_date (description="结束日期，格式：yyyy-MM-dd")
}

// 用例执行统计请求参数
struct CaseExecutionStatsRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
    2: optional i32 case_id (description="用例ID，为空时获取所有用例")
    3: optional string start_date (description="开始日期，格式：yyyy-MM-dd")
    4: optional string end_date (description="结束日期，格式：yyyy-MM-dd")
}

// 业务用例趋势请求参数
struct BusinessCaseTrendRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
    2: optional i32 days = 30 (description="统计天数，范围：1-365", min="1", max="365")
}

// 任务执行趋势请求参数
struct TaskExecutionTrendRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
    2: optional i32 days = 30 (description="统计天数，范围：1-365", min="1", max="365")
}

// 用例执行趋势请求参数
struct CaseExecutionTrendRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
    2: optional i32 case_id (description="用例ID，为空时获取所有用例")
    3: optional i32 days = 30 (description="统计天数，范围：1-365", min="1", max="365")
}

// 统计概览请求参数
struct StatsSummaryRequest {
    1: optional i32 business_id (description="业务线ID，为空时获取所有业务线")
}

// 业务用例统计项
struct BusinessCaseStatsItem {
    1: required i32 business_id (description="业务线ID")
    2: required string business_name (description="业务线名称")
    3: required i32 total_cases (description="总用例数")
    4: required i32 active_cases (description="活跃用例数")
    5: required string stats_date (description="统计日期，格式：yyyy-MM-dd")
}

// 业务用例统计响应
struct BusinessCaseStatsResponse {
    1: required i32 total (description="总记录数")
    2: required list<BusinessCaseStatsItem> items (description="统计项列表")
}

// 任务执行统计项
struct TaskExecutionStatsItem {
    1: required i32 business_id (description="业务线ID")
    2: required string business_name (description="业务线名称")
    3: required i32 total_executions (description="总执行次数")
    4: required i32 success_executions (description="成功执行次数")
    5: required i32 failed_executions (description="失败执行次数")
    6: required double success_rate (description="成功率(%)")
    7: required i32 avg_duration (description="平均执行时长(秒)")
    8: required string stats_date (description="统计日期，格式：yyyy-MM-dd")
}

// 任务执行统计响应
struct TaskExecutionStatsResponse {
    1: required i32 total (description="总记录数")
    2: required list<TaskExecutionStatsItem> items (description="统计项列表")
}

// 用例执行统计项
struct CaseExecutionStatsItem {
    1: required i32 business_id (description="业务线ID")
    2: required string business_name (description="业务线名称")
    3: required i32 case_id (description="用例ID")
    4: required string case_name (description="用例名称")
    5: required i32 total_executions (description="总执行次数")
    6: required i32 success_executions (description="成功执行次数")
    7: required i32 failed_executions (description="失败执行次数")
    8: required double success_rate (description="成功率(%)")
    9: required i32 avg_duration (description="平均执行时长(秒)")
    10: required string stats_date (description="统计日期，格式：yyyy-MM-dd")
}

// 用例执行统计响应
struct CaseExecutionStatsResponse {
    1: required i32 total (description="总记录数")
    2: required list<CaseExecutionStatsItem> items (description="统计项列表")
}

// 业务用例趋势项
struct BusinessCaseTrendItem {
    1: required i32 business_id (description="业务线ID")
    2: required string business_name (description="业务线名称")
    3: required i32 total_cases (description="总用例数")
    4: required string stats_date (description="统计日期，格式：yyyy-MM-dd")
}

// 业务用例趋势响应
struct BusinessCaseTrendResponse {
    1: required i32 total (description="总记录数")
    2: required list<BusinessCaseTrendItem> items (description="趋势项列表")
}

// 任务执行趋势项
struct TaskExecutionTrendItem {
    1: required i32 business_id (description="业务线ID")
    2: required string business_name (description="业务线名称")
    3: required i32 total_executions (description="总执行次数")
    4: required i32 success_executions (description="成功执行次数")
    5: required i32 failed_executions (description="失败执行次数")
    6: required double success_rate (description="成功率(%)")
    7: required i32 avg_duration (description="平均执行时长(秒)")
    8: required string stats_date (description="统计日期，格式：yyyy-MM-dd")
}

// 任务执行趋势响应
struct TaskExecutionTrendResponse {
    1: required i32 total (description="总记录数")
    2: required list<TaskExecutionTrendItem> items (description="趋势项列表")
}

// 用例执行趋势项
struct CaseExecutionTrendItem {
    1: required i32 business_id (description="业务线ID")
    2: required string business_name (description="业务线名称")
    3: required i32 case_id (description="用例ID")
    4: required string case_name (description="用例名称")
    5: required i32 total_executions (description="总执行次数")
    6: required i32 success_executions (description="成功执行次数")
    7: required i32 failed_executions (description="失败执行次数")
    8: required double success_rate (description="成功率(%)")
    9: required i32 avg_duration (description="平均执行时长(秒)")
    10: required string stats_date (description="统计日期，格式：yyyy-MM-dd")
}

// 用例执行趋势响应
struct CaseExecutionTrendResponse {
    1: required i32 total (description="总记录数")
    2: required list<CaseExecutionTrendItem> items (description="趋势项列表")
}

// 统计概览响应
struct StatsSummaryResponse {
    1: required i32 total_cases (description="总用例数")
    2: required i32 total_active_cases (description="总活跃用例数")
    3: required i32 total_task_executions (description="总任务执行次数")
    4: required i32 total_success_executions (description="总成功执行次数")
    5: required double overall_success_rate (description="总体成功率(%)")
    6: required i32 business_count (description="业务线数量")
}
