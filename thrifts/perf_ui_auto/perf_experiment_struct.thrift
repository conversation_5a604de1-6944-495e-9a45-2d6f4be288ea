namespace py perf_ui_auto.perf_experiment_struct

// 命中类型枚举
enum LibraHitType {
    DID = 1,  // DID命中
    UID = 2,  // UID命中
}

// 实验信息结构
struct ExperimentInfo {
    1: required i64 id,                           // 实验ID
    2: required string name,                      // 实验名称
    3: required LibraHitType hit_type,           // 命中类型
    4: required list<i64> experiment_group_version_ids,  // 实验组版本IDs
    5: required list<i64> control_group_version_ids,     // 对照组版本IDs
    6: required i64 create_time,                 // 创建时间
    7: required i64 update_time,                 // 更新时间
    8: optional string description,              // 实验描述
    9: required string creator,                  // 创建者
}

// 创建实验请求
struct CreateExperimentRequest {
    1: required string name,
    2: required LibraHitType hit_type,
    3: required list<i64> experiment_group_version_ids,
    4: required list<i64> control_group_version_ids,
    5: optional string description,
    6: required string creator,                  // 创建者
}

// 创建实验响应
struct CreateExperimentResponse {
    1: required bool success,
    2: optional string message,
    3: optional ExperimentInfo experiment,
}

// 查询实验列表请求
struct ListExperimentsRequest {
    1: optional i32 page_size = 20,
    2: optional i32 page = 1,
    3: optional string name,
    4: optional LibraHitType hit_type,
}

// 查询实验列表响应
struct ListExperimentsResponse {
    1: required bool success,
    2: optional string message,
    3: required list<ExperimentInfo> experiments,
    4: required i32 total,
}

// 更新实验请求
struct UpdateExperimentRequest {
    1: required i64 id,
    2: optional string name,
    3: optional LibraHitType hit_type,
    4: optional list<i64> experiment_group_version_ids,
    5: optional list<i64> control_group_version_ids,
    6: optional string description,
}

// 更新实验响应
struct UpdateExperimentResponse {
    1: required bool success,
    2: optional string message,
    3: optional ExperimentInfo experiment,
}

// 删除实验请求
struct DeleteExperimentRequest {
    1: required i64 id,
}

// 删除实验响应
struct DeleteExperimentResponse {
    1: required bool success,
    2: optional string message,
}

// 获取实验详情请求
struct GetExperimentDetailRequest {
    1: required i64 id,
}

// 获取实验详情响应
struct GetExperimentDetailResponse {
    1: required bool success,
    2: optional string message,
    3: optional ExperimentInfo experiment,
}

// 根据vid列表查询实验信息请求
struct ViewFlightByVidRequest {
    1: required list<i64> vids,
}

// 根据vid列表查询实验信息响应
struct ViewFlightByVidResponse {
    1: required bool success,
    2: optional string message,
    3: optional list<map<string, string>> data,
}
