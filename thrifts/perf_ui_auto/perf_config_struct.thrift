namespace py perf_ui_auto.perf_config

/**
 * 比对标准类型
 */
enum ComparisonType {
    INCREASE = 1,  // 增加
    DECREASE = 2   // 减少
}

/**
 * 值类型
 */
enum ValueType {
    PERCENTAGE = 1,  // 百分比
    ABSOLUTE = 2     // 绝对值
}

/**
 * 性能指标配置项
 */
struct PerfMetricConfigItem {
    1: required i64 metric_id,                // 指标ID
    2: required string metric_key,            // 指标key
    3: required string metric_name,           // 指标名称
    4: required i32 metric_type,              // 指标类型 1-common 2-android 3-ios
    5: required i32 metric_category,          // 指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
    6: optional string metric_unit,           // 指标单位
    7: optional string metric_desc,           // 指标描述
    8: optional map<string, string> query_conditions,  // 查询条件配置
    9: required bool is_important,            // 是否重要指标
    10: optional ComparisonType comparison_type,  // 比对标准类型
    11: optional ValueType value_type,        // 值类型
    12: optional double threshold_value       // 阈值
}

/**
 * 性能配置项
 */
struct PerfConfigItem {
    1: required i64 id,                    // 配置ID
    2: required i64 business_id,           // 业务线ID
    3: required string config_name,        // 配置名称
    4: required list<PerfMetricConfigItem> metrics,  // 性能指标列表
    5: required string creator,            // 创建者
    6: required i64 create_time,           // 创建时间
    7: required i64 update_time            // 更新时间
}

/**
 * 创建配置请求
 */
struct CreateConfigRequest {
    1: required i64 business_id,           // 业务线ID
    2: required string config_name,        // 配置名称
    3: required list<PerfMetricConfigItem> metrics,  // 性能指标列表
    4: required string creator             // 创建者
}

/**
 * 更新配置请求
 */
struct UpdateConfigRequest {
    1: required i64 id,                    // 配置ID
    2: optional i64 business_id,           // 业务线ID
    3: optional string config_name,        // 配置名称
    4: optional list<PerfMetricConfigItem> metrics  // 性能指标列表
}

/**
 * 配置列表请求
 */
struct ConfigListRequest {
    1: optional i64 business_id,           // 业务线ID
    2: optional string config_name,        // 配置名称
    3: optional string creator,            // 创建者
    4: optional i64 start_time,            // 开始时间
    5: optional i64 end_time,              // 结束时间
    6: required i32 page = 1,              // 页码
    7: required i32 page_size = 10         // 每页数量
}

/**
 * 配置列表响应
 */
struct PerfConfigListResponse {
    1: required i32 page,                  // 页码
    2: required i32 page_size,             // 每页数量
    3: required i32 total,                 // 总记录数
    4: required list<PerfConfigItem> items // 配置列表
}

/**
 * 获取配置详情请求
 */
struct GetConfigDetailRequest {
    1: required i64 config_id,           // 配置ID
}

/**
 * 删除配置请求
 */
struct DeleteConfigRequest {
    1: required i64 config_id,           // 配置ID
}
