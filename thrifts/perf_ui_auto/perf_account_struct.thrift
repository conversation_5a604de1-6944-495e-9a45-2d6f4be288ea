// 定义命名空间
namespace py perf_ui_auto.perf_account_struct

// 查询账号列表的请求参数
struct GetAccountListRequest {
    1: required i32 page = 1 (description="页码")
    2: required i32 page_size = 10 (description="每页大小")
    3: optional i32 business_id (description="业务线ID")
    4: optional i32 is_occupied (description="是否被占用 0-未占用 1-已占用")
    5: optional i32 account_type (description="账号类型 1-性能测试账号 2-普通账号")
}

// 添加账号的参数
struct AddAccountParams {
    // 必填字段
    1: required i32 business_id (description="业务线ID")
    2: required string uid (description="用户ID")
    3: required string iphone (description="虚拟手机号")
    4: required string username (description="用户名")
    5: required string owner (description="负责人")
    
    // 可选字段
    6: optional string captcha (description="验证码")
    7: optional string email (description="邮箱")
    8: optional string pwd (description="密码")
    9: optional string country (description="国家名称(例如:中国,美国)")
    10: optional string country_code_alpha2 (description="ISO 3166-1 国家二字码(例如:CN,US)")
    11: optional string phone_area_code (description="电话区号(例如:86,1)")
    
    // 有默认值的字段
    12: required i32 app = 0 (description="APP")
    13: required i32 is_occupied = 0 (description="是否被占用 0-未占用 1-已占用")
    14: required i32 account_type = 2 (description="账号类型 1-性能测试账号 2-普通账号")
}

// 更新账号的参数
struct UpdateAccountParams {
    // 必填字段
    1: required i32 id (description="账号ID")
    
    // 可选字段
    2: optional i32 business_id (description="业务线ID")
    3: optional string uid (description="用户ID")
    4: optional string iphone (description="虚拟手机号")
    5: optional string username (description="用户名")
    6: optional string owner (description="负责人")
    7: optional string captcha (description="验证码")
    8: optional string email (description="邮箱")
    9: optional string pwd (description="密码")
    10: optional string country (description="国家名称(例如:中国,美国)")
    11: optional string country_code_alpha2 (description="ISO 3166-1 国家二字码(例如:CN,US)")
    12: optional string phone_area_code (description="电话区号(例如:86,1)")
    13: optional i32 app (description="APP")
    14: optional i32 is_occupied (description="是否被占用 0-未占用 1-已占用")
    15: optional i32 account_type (description="账号类型 1-性能测试账号 2-普通账号")
}

// 查询账号详情的参数
struct GetAccountDetailParams {
    1: required i32 account_id (description="账号ID")
}

// 更新账号占用状态的参数
struct UpdateAccountOccupiedParams {
    1: required i32 account_id (description="账号ID")
    2: required i32 is_occupied (description="是否被占用 0-未占用 1-已占用")
}

// 批量更新账号占用状态的参数
struct BatchUpdateAccountOccupiedParams {
    1: required list<i32> account_ids (description="账号ID列表")
    2: required i32 is_occupied (description="是否被占用 0-未占用 1-已占用")
}

// 删除账号的参数
struct DeleteAccountParams {
    1: required i32 account_id (description="账号ID")
}
