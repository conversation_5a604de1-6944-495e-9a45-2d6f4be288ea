namespace py perf_ui_auto.perf_metric_struct

/**
 * Trace采集指标查询条件
 */
struct TraceQueryConditions {
    1: required string Namespace,           // 命名空间
    2: required i64 Start,                  // 开始时间戳
    3: required i64 End,                    // 结束时间戳
    4: optional i32 Offset = 0,             // 偏移量
    5: optional i32 Limit = 200,            // 限制数量
    6: optional list<string> Filter = [],   // 过滤条件
    7: required string QueryString,         // 查询字符串
    8: required string Owner                // 所有者
}

/**
 * ByteIO指标查询条件
 */
struct ByteIOQueryConditions {
    1: required i64 app_id,                 // 应用ID
    2: required i64 device_id,              // 设备ID
    3: required i64 start_time,             // 开始时间戳
    4: required i64 end_time,               // 结束时间戳
    5: required list<string> log_types,     // 日志类型列表
    6: required list<string> event_names    // 事件名称列表
}

/**
 * 性能指标基础信息
 */
struct BaseMetricInfo {
    1: required string metric_key,          // 指标key
    2: required string metric_name,         // 指标名称
    3: optional i32 metric_type = 1,        // 指标类型 1-common 2-android 3-ios
    4: optional i32 metric_category = 1,    // 指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
    5: optional string metric_unit,         // 指标单位
    6: optional string metric_desc,         // 指标描述
    7: optional map<string, string> query_conditions  // 查询条件配置
}

/**
 * 创建性能指标请求
 */
struct CreateMetricRequest {
    1: required string metric_key,          // 指标key
    2: required string metric_name,         // 指标名称
    3: optional i32 metric_type = 1,        // 指标类型 1-common 2-android 3-ios
    4: optional i32 metric_category = 1,    // 指标分类 1-基础性能采集指标 2-Trace采集指标 3-ByteIO指标
    5: optional string metric_unit,         // 指标单位
    6: optional string metric_desc,         // 指标描述
    7: optional map<string, string> query_conditions,  // 查询条件配置
    8: required string creator              // 创建者
}

/**
 * 批量创建性能指标请求
 */
struct BatchCreateMetricRequest {
    1: required list<CreateMetricRequest> metrics  // 性能指标列表
}

/**
 * 更新性能指标请求
 */
struct UpdateMetricRequest {
    1: required i64 id,                     // 指标ID
    2: optional string metric_key,          // 指标key
    3: optional string metric_name,         // 指标名称
    4: optional i32 metric_type,            // 指标类型 1-common 2-android 3-ios
    5: optional i32 metric_category,        // 指标分类 1-基础性能采集指标 2-Trace采集指标 3-ByteIO指标
    6: optional string metric_unit,         // 指标单位
    7: optional string metric_desc,         // 指标描述
    8: optional map<string, string> query_conditions  // 查询条件配置
}

/**
 * 性能指标列表请求
 */
struct MetricListRequest {
    1: optional i32 metric_type,            // 指标类型 1-common 2-android 3-ios
    2: optional i32 metric_category,        // 指标分类 1-基础性能采集指标 2-Trace采集指标 3-ByteIO指标
    3: optional string metric_key,          // 指标key
    4: optional string metric_name,         // 指标名称
    5: optional string creator,             // 创建者
    6: optional i64 start_time,             // 开始时间
    7: optional i64 end_time,               // 结束时间
    8: required i32 page = 1,               // 页码
    9: required i32 page_size = 10          // 每页数量
}

/**
 * 性能指标输出信息
 */
struct PerfMetricOut {
    1: required i64 id,                     // 指标ID
    2: required string metric_key,          // 指标key
    3: required string metric_name,         // 指标名称
    4: required i32 metric_type,            // 指标类型 1-common 2-android 3-ios
    5: required i32 metric_category,        // 指标分类 1-基础性能采集指标 2-Trace采集指标 3-ByteIO指标
    6: optional string metric_unit,         // 指标单位
    7: optional string metric_desc,         // 指标描述
    8: optional map<string, string> query_conditions,  // 查询条件配置
    9: required string creator,             // 创建者
    10: required i64 create_time,           // 创建时间
    11: required i64 update_time            // 更新时间
}

/**
 * 单个性能指标响应
 */
struct MetricResponse {
    1: required bool success,               // 是否成功
    2: optional string message,             // 响应消息
    3: optional PerfMetricOut metric        // 性能指标信息
}

/**
 * 批量性能指标响应
 */
struct BatchMetricResponse {
    1: required bool success,               // 是否成功
    2: optional string message,             // 响应消息
    3: required i32 success_count,          // 成功数量
    4: required i32 failed_count,           // 失败数量
    5: required list<PerfMetricOut> success_metrics,  // 成功创建的指标列表
    6: required list<map<string, string>> failed_metrics  // 创建失败的指标信息
}

/**
 * 性能指标列表响应
 */
struct MetricListResponse {
    1: required bool success,               // 是否成功
    2: required i32 total,                  // 总记录数
    3: optional string message,             // 响应消息
    4: required list<PerfMetricOut> metrics // 性能指标列表
}

/**
 * 获取指标详情请求
 */
struct GetMetricDetailRequest {
    1: required i64 metric_id,            // 指标ID
}

/**
 * 删除指标请求
 */
struct DeleteMetricRequest {
    1: required i64 metric_id,            // 指标ID
}
