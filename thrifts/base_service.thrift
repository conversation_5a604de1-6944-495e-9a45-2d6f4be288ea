include "ci_daily_struct.thrift"
include "lark_robot_callback_struct.thrift"
include "libra_experiment/libra_experiment_struct.thrift"
include "perf_ui_auto/perf_account_struct.thrift"
include "perf_ui_auto/perf_app_struct.thrift"
include "perf_ui_auto/perf_case_struct.thrift"
include "perf_ui_auto/perf_client_struct.thrift"
include "perf_ui_auto/perf_data_struct.thrift"
include "perf_ui_auto/perf_device_struct.thrift"
include "perf_ui_auto/perf_task_struct.thrift"
include "perf_ui_auto/perf_task_queue_struct.thrift"
include "perf_ui_auto/perf_request_queue_struct.thrift"
include "perf_ui_auto/perf_config_struct.thrift"
include "perf_ui_auto/perf_metric_struct.thrift"
include "perf_ui_auto/perf_experiment_struct.thrift"
include "perf_ui_auto/perf_stats_struct.thrift"
include "business/business_struct.thrift"
include "tool_manager/ffmpeg_stream_struct.thrift"

// 定义命名空间
namespace py base_service

// 定义响应状态码和信息
enum ResponseCode {
  SUCCESS = 0,
  ERROR = 1,
}
// 定义通用数据结构
union AnyData {
 1: i64 int_value;
 2: double double_value;
 3: string string_value;
 4: bool bool_value;
 5: binary binary_value;
 6: map<string, string> map_value;
 7: list<string> list_value;
}

// 定义统一的HTTP响应结构体
struct HttpResponse {
  1: required ResponseCode code;
  2: required string msg;
  3: optional AnyData data;
}

// 定义服务接口
service BaseService {
  // CI Daily相关接口
  HttpResponse getCiDaily(1: required ci_daily_struct.GetCiDailyDataParams params)
    (api.post='/api/test_manager/ci_daily/get_ci_daily', api.api_level="0", api.category="ci_daily")

  // 飞书机器人回调相关接口
  lark_robot_callback_struct.CallbackResponse checklistCardCallback(1: required lark_robot_callback_struct.CallbackParams params)
    (api.post='/api/version_manager/detail/checklist_card_callback', api.api_level="0", api.category="version_detail")

  // Libra 实验相关接口
  HttpResponse addExperiment(1: required libra_experiment_struct.LibraExperimentParams params)
    (api.put='/api/libra_experiment/add_experiment', api.api_level="0", api.category="libra_experiment")
  HttpResponse FindLatestCheckRuleByBusinessId(1: required libra_experiment_struct.FindLatestCheckRuleParames params)
    (api.get='/api/libra_experiment/find_latest_check_rule_by_business_id', api.api_level="0", api.category="libra_experiment")
  HttpResponse addRule(1: required libra_experiment_struct.AddLibraExperimentRuleParams params)
    (api.post='/api/libra_experiment/add_rule', api.api_level="0", api.category="libra_experiment")
  HttpResponse updateRule(1: required libra_experiment_struct.UpdateLibraExperimentRuleParams params)
    (api.post='/api/libra_experiment/update_rule', api.api_level="0", api.category="libra_experiment")

  // 业务相关接口
  HttpResponse fetchAllBusiness(1: required business_struct.BusinessFetchAllReq params)
    (api.get='/api/business/fetch_all', api.api_level="0", api.category="business")
  HttpResponse addBusiness(1: required business_struct.BusinessAddReq params)
    (api.post='/api/business/add', api.api_level="0", api.category="business")
  HttpResponse updateBusiness(1: required business_struct.BusinessUpdateReq params)
    (api.put='/api/business/update', api.api_level="0", api.category="business")

  // 账号管理相关接口
  HttpResponse getAccountList(1: perf_account_struct.GetAccountListRequestParams params)
    (api.post='/api/perf_ui_auto/perf_account/account_list', api.api_level="0", api.category="perf_account")
  HttpResponse getAccountDetail(1: perf_account_struct.GetAccountDetailParams params)
    (api.get='/api/perf_ui_auto/perf_account/account_detail', api.api_level="0", api.category="perf_account")
  HttpResponse addAccount(1: perf_account_struct.AddAccountParams params)
    (api.post='/api/perf_ui_auto/perf_account/add_account', api.api_level="0", api.category="perf_account")
  HttpResponse updateAccount(1: perf_account_struct.UpdateAccountParams params)
    (api.put='/api/perf_ui_auto/perf_account/update_account', api.api_level="0", api.category="perf_account")
  HttpResponse updateAccountOccupied(1: perf_account_struct.UpdateAccountOccupiedParams params)
    (api.put='/api/perf_ui_auto/perf_account/update_account_occupied', api.api_level="0", api.category="perf_account")
  HttpResponse batchUpdateAccountOccupied(1: perf_account_struct.BatchUpdateAccountOccupiedParams params)
    (api.put='/api/perf_ui_auto/perf_account/batch_update_account_occupied', api.api_level="0", api.category="perf_account")
  HttpResponse deleteAccount(1: perf_account_struct.DeleteAccountParams params)
    (api.delete='/api/perf_ui_auto/perf_account/delete_account', api.api_level="0", api.category="perf_account")

  // 应用管理相关接口
  HttpResponse getAppVersionList(1: perf_app_struct.GetAppVersionListParams params)
    (api.get='/api/perf_ui_auto/perf_app/app_version_list', api.api_level="0", api.category="perf_app")
  HttpResponse getAppList(1: perf_app_struct.GetAppListParams params)
    (api.post='/api/perf_ui_auto/perf_app/app_list', api.api_level="0", api.category="perf_app")
  HttpResponse addApp(1: perf_app_struct.AddAppParams params)
    (api.post='/api/perf_ui_auto/perf_app/add_app', api.api_level="0", api.category="perf_app")
  HttpResponse updateApp(1: perf_app_struct.UpdateAppParams params)
    (api.put='/api/perf_ui_auto/perf_app/update_app', api.api_level="0", api.category="perf_app")
  HttpResponse deleteApp(1: perf_app_struct.DeleteAppParams params)
    (api.delete='/api/perf_ui_auto/perf_app/delete_app', api.api_level="0", api.category="perf_app")
    
  // 应用组管理相关接口
  HttpResponse getAppGroupList(1: perf_app_struct.GetAppGroupListRequestParams params)
    (api.post='/api/perf_ui_auto/perf_app/app_group_list', api.api_level="0", api.category="perf_app")
  HttpResponse addAppGroup(1: perf_app_struct.AddAppGroupParams params)
    (api.post='/api/perf_ui_auto/perf_app/add_app_group', api.api_level="0", api.category="perf_app")
  HttpResponse updateAppGroup(1: perf_app_struct.UpdateAppGroupParams params)
    (api.put='/api/perf_ui_auto/perf_app/update_app_group', api.api_level="0", api.category="perf_app")
  HttpResponse getAppGroupDetail(1: perf_app_struct.GetAppGroupDetailParams params)
    (api.get='/api/perf_ui_auto/perf_app/app_group_detail', api.api_level="0", api.category="perf_app")

  // 用例管理相关接口
  HttpResponse getCaseList(1: perf_case_struct.GetCaseListParams params)
    (api.post='/api/perf_ui_auto/perf_case/case_list', api.api_level="0", api.category="perf_case")
  HttpResponse updateCase(1: perf_case_struct.UpdateCaseParams params)
    (api.post='/api/perf_ui_auto/perf_case/update_case', api.api_level="0", api.category="perf_case")
  HttpResponse getCaseDetail(1: perf_case_struct.GetCaseDetailParams params)
    (api.get='/api/perf_ui_auto/perf_case/case_detail', api.api_level="0", api.category="perf_case")
  HttpResponse batchUpdateCaseStatus(1: perf_case_struct.BatchUpdateCaseStatusParams params)
    (api.put='/api/perf_ui_auto/perf_case/batch_update_case_status', api.api_level="0", api.category="perf_case")

  // 客户端管理相关接口
  HttpResponse getClientDetail(1: perf_client_struct.GetClientDetailParams params)
    (api.get='/api/perf_ui_auto/perf_client/get_client_detail', api.api_level="0", api.category="perf_client")
  HttpResponse registerPerfClient(1: perf_client_struct.RegisterPerfClientParams params)
    (api.post='/api/perf_ui_auto/perf_client/register_perf_client', api.api_level="0", api.category="perf_client")
  HttpResponse getClientList()
    (api.get='/api/perf_ui_auto/perf_client/client_list', api.api_level="0", api.category="perf_client")
  HttpResponse updateClient(1: perf_client_struct.UpdateClientDetailParams params)
    (api.post='/api/perf_ui_auto/perf_client/update_client', api.api_level="0", api.category="perf_client")
  HttpResponse deleteClient(1: perf_client_struct.DeleteClientParams params)
    (api.delete='/api/perf_ui_auto/perf_client/del_client', api.api_level="0", api.category="perf_client")

  // 性能数据相关接口
  HttpResponse getReportData(1: perf_data_struct.PerfDataReportParams params)
    (api.post='/api/perf_ui_auto/perf_data/data_list', api.api_level="0", api.category="perf_data")
  HttpResponse getPerfDataTosUrl(1: perf_data_struct.PerfDataTosUrlParams params)
    (api.post='/api/perf_ui_auto/perf_data/perf_data_tos_url', api.api_level="0", api.category="perf_data")
  HttpResponse uploadPerfData(1: perf_data_struct.UploadPerfDataParams params)
    (api.put='/api/perf_ui_auto/perf_data/upload_perf_data', api.api_level="0", api.category="perf_data")
  HttpResponse getPerfDataStats(1: perf_data_struct.GetPerfDataStatsParams params)
    (api.get='/api/perf_ui_auto/perf_data/get_stats', api.api_level="0", api.category="perf_data")
  HttpResponse batchUploadPerfData(1: perf_data_struct.BatchUploadPerfDataParams params)
    (api.put='/api/perf_ui_auto/perf_data/batch_upload_perf_data', api.api_level="0", api.category="perf_data")
  


  // 设备管理相关接口
  HttpResponse getDeviceList(1: perf_device_struct.GetDeviceListRequestParams params)
    (api.get='/api/perf_ui_auto/perf_device/device_list', api.api_level="0", api.category="perf_device")
  HttpResponse getDeviceDetail(1: perf_device_struct.GetDeviceDetailParams params)
    (api.get='/api/perf_ui_auto/perf_device/device_detail', api.api_level="0", api.category="perf_device")
  HttpResponse getAvailableDevices(1: perf_device_struct.GetAvailableDevicesParams params)
    (api.get='/api/perf_ui_auto/perf_device/available_devices', api.api_level="0", api.category="perf_device")
  HttpResponse uploadPerfDevice(1: perf_device_struct.UploadPerfDeviceRequestParams params)
    (api.post='/api/perf_ui_auto/perf_device/upload_perf_device', api.api_level="0", api.category="perf_device")
  HttpResponse updateDeviceOccupied(1: perf_device_struct.UpdateDeviceOccupiedParams params)
    (api.put='/api/perf_ui_auto/perf_device/update_device_occupied', api.api_level="0", api.category="perf_device")
  HttpResponse batchUpdateDeviceOccupied(1: perf_device_struct.BatchUpdateDeviceOccupiedParams params)
    (api.put='/api/perf_ui_auto/perf_device/batch_update_device_occupied', api.api_level="0", api.category="perf_device")
  HttpResponse deleteDevice(1: perf_device_struct.DeleteDeviceParams params)
    (api.delete='/api/perf_ui_auto/perf_device/delete_device', api.api_level="0", api.category="perf_device")

  // 任务管理相关接口
  HttpResponse getTaskList(1: perf_task_struct.GetTaskListRequestParams params)
    (api.post='/api/perf_ui_auto/perf_task/task_list', api.api_level="0", api.category="perf_task")
  HttpResponse getTaskDetail(1: perf_task_struct.GetTaskDetailParams params)
    (api.get='/api/perf_ui_auto/perf_task/task_detail', api.api_level="0", api.category="perf_task")
  HttpResponse getTaskDetailAll(1: perf_task_struct.GetTaskDetailAllParams params)
    (api.get='/api/perf_ui_auto/perf_task/task_detail_all', api.api_level="0", api.category="perf_task")
  HttpResponse createTask(1: perf_task_struct.CreatePerfTaskParams params)
    (api.put='/api/perf_ui_auto/perf_task/create_task', api.api_level="0", api.category="perf_task")
  HttpResponse updateTask(1: perf_task_struct.UpdatePerfTaskParams params)
    (api.post='/api/perf_ui_auto/perf_task/update_task', api.api_level="0", api.category="perf_task")
  HttpResponse copyTask(1: perf_task_struct.CopyTaskParams params)
    (api.post='/api/perf_ui_auto/perf_task/copy_task', api.api_level="0", api.category="perf_task")
  HttpResponse deleteTask(1: perf_task_struct.DeleteTaskParams params)
    (api.delete='/api/perf_ui_auto/perf_task/delete_task', api.api_level="0", api.category="perf_task")
  HttpResponse startTask(1: perf_task_struct.StartTaskParams params)
    (api.get='/api/perf_ui_auto/perf_task/start_task', api.api_level="0", api.category="perf_task")
  HttpResponse retryTask(1: perf_task_struct.RetryTaskParams params)
    (api.get='/api/perf_ui_auto/perf_task/retry_task', api.api_level="0", api.category="perf_task")
  HttpResponse updateTaskStatus(1: perf_task_struct.UpdateTaskStatusParams params)
    (api.post='/api/perf_ui_auto/perf_task/update_task_status_by_id', api.api_level="0", api.category="perf_task")
  HttpResponse updateSubTaskStatus(1: perf_task_struct.UpdateSubTaskStatusParams params)
    (api.post='/api/perf_ui_auto/perf_task/update_sub_task_status_by_id', api.api_level="0", api.category="perf_task")
  HttpResponse uploadSubTaskCaseRunDetail(1: perf_task_struct.UploadPerfCaseRunDetailParams params)
    (api.post='/api/perf_ui_auto/perf_task/upload_sub_task_case_run_detail', api.api_level="0", api.category="perf_task")
  HttpResponse getSubTaskCaseRunDetail(1: perf_task_struct.GetSubTaskCaseRunDetailParams params)
    (api.get='/api/perf_ui_auto/perf_task/get_sub_task_case_run_detail', api.api_level="0", api.category="perf_task")
  HttpResponse getTaskProgress(1: perf_task_struct.GetTaskProgressParams params)
    (api.get='/api/perf_ui_auto/perf_task/get_progress', api.api_level="0", api.category="perf_task")
  HttpResponse getCaseExecutionStats(1: perf_task_struct.GetCaseExecutionStatsParams params)
    (api.get='/api/perf_ui_auto/perf_task/get_case_execution_stats', api.api_level="0", api.category="perf_task")

  // 队列管理相关接口
  HttpResponse getQueueLength(1: perf_task_queue_struct.GetQueueLengthParams params)
    (api.get='/api/perf_ui_auto/perf_task_queue/get_length', api.api_level="0", api.category="perf_task_queue")
  HttpResponse clearQueue(1: perf_task_queue_struct.ClearQueueParams params)
    (api.delete='/api/perf_ui_auto/perf_task_queue/clear', api.api_level="0", api.category="perf_task_queue")
  HttpResponse getHeadSubTask(1: perf_task_queue_struct.GetHeadSubTaskParams params)
    (api.get='/api/perf_ui_auto/perf_task_queue/get_head_sub_task', api.api_level="0", api.category="perf_task_queue")
  HttpResponse getAllSubTasks(1: perf_task_queue_struct.GetAllSubTasksParams params)
    (api.get='/api/perf_ui_auto/perf_task_queue/get_all_sub_tasks', api.api_level="0", api.category="perf_task_queue")
  HttpResponse pushSubTask(1: perf_task_queue_struct.PushSubTaskParams params)
    (api.post='/api/perf_ui_auto/perf_task_queue/push_sub_task', api.api_level="0", api.category="perf_task_queue")
  HttpResponse removeHeadSubTask(1: perf_task_queue_struct.RemoveHeadSubTaskParams params)
    (api.delete='/api/perf_ui_auto/perf_task_queue/remove_head_sub_task', api.api_level="0", api.category="perf_task_queue")
  HttpResponse moveToTail(1: perf_task_queue_struct.MoveToTailParams params)
    (api.put='/api/perf_ui_auto/perf_task_queue/move_to_tail', api.api_level="0", api.category="perf_task_queue")
  HttpResponse batchGetQueueStatus(1: perf_task_queue_struct.BatchGetQueueStatusParams params)
    (api.post='/api/perf_ui_auto/perf_task_queue/batch_get_status', api.api_level="0", api.category="perf_task_queue")
  HttpResponse removeSubTasksByTaskId(1: perf_task_queue_struct.RemoveSubTasksByTaskIdParams params)
    (api.delete='/api/perf_ui_auto/perf_task_queue/remove_sub_tasks_by_task_id', api.api_level="0", api.category="perf_task_queue")

  // 请求队列相关接口
  HttpResponse getAllRequests(1: perf_request_queue_struct.GetAllRequestsParams params)
    (api.get='/api/perf_ui_auto/perf_request_queue/all', api.api_level="0", api.category="perf_request_queue")
  HttpResponse clearRequests(1: perf_request_queue_struct.ClearRequestsParams params)
    (api.delete='/api/perf_ui_auto/perf_request_queue/clear', api.api_level="0", api.category="perf_request_queue")

  // 性能配置相关接口
  HttpResponse getConfigList(1: perf_config_struct.ConfigListRequest params)
    (api.post='/api/perf_ui_auto/perf_config/config_list', api.api_level="0", api.category="perf_config")
  HttpResponse getConfigDetail(1: perf_config_struct.GetConfigDetailRequest params)
    (api.get='/api/perf_ui_auto/perf_config/config_detail', api.api_level="0", api.category="perf_config")
  HttpResponse addConfig(1: perf_config_struct.CreateConfigRequest params)
    (api.post='/api/perf_ui_auto/perf_config/add_config', api.api_level="0", api.category="perf_config")
  HttpResponse updateConfig(1: perf_config_struct.UpdateConfigRequest params)
    (api.put='/api/perf_ui_auto/perf_config/update_config', api.api_level="0", api.category="perf_config")
  HttpResponse deleteConfig(1: perf_config_struct.DeleteConfigRequest params)
    (api.delete='/api/perf_ui_auto/perf_config/delete_config', api.api_level="0", api.category="perf_config")

  // 性能指标相关接口
  HttpResponse getMetricList(1: perf_metric_struct.MetricListRequest params)
    (api.post='/api/perf_ui_auto/perf_metric/metric_list', api.api_level="0", api.category="perf_metric")
  HttpResponse getMetricDetail(1: perf_metric_struct.GetMetricDetailRequest params)
    (api.get='/api/perf_ui_auto/perf_metric/metric_detail', api.api_level="0", api.category="perf_metric")
  HttpResponse addMetric(1: perf_metric_struct.CreateMetricRequest params)
    (api.post='/api/perf_ui_auto/perf_metric/add_metric', api.api_level="0", api.category="perf_metric")
  HttpResponse batchAddMetrics(1: perf_metric_struct.BatchCreateMetricRequest params)
    (api.post='/api/perf_ui_auto/perf_metric/batch_add_metrics', api.api_level="0", api.category="perf_metric")
  HttpResponse updateMetric(1: perf_metric_struct.UpdateMetricRequest params)
    (api.put='/api/perf_ui_auto/perf_metric/update_metric', api.api_level="0", api.category="perf_metric")
  HttpResponse deleteMetric(1: perf_metric_struct.DeleteMetricRequest params)
    (api.delete='/api/perf_ui_auto/perf_metric/delete_metric', api.api_level="0", api.category="perf_metric")

  // 实验相关接口
  HttpResponse createExperiment(1: perf_experiment_struct.CreateExperimentRequest params)
    (api.post='/api/perf_ui_auto/experiment/create', api.api_level="0", api.category="perf_experiment")
  HttpResponse getExperimentList(1: perf_experiment_struct.ListExperimentsRequest params)
    (api.get='/api/perf_ui_auto/experiment/list', api.api_level="0", api.category="perf_experiment")
  HttpResponse updateExperiment(1: perf_experiment_struct.UpdateExperimentRequest params)
    (api.put='/api/perf_ui_auto/experiment/update', api.api_level="0", api.category="perf_experiment")
  HttpResponse deleteExperiment(1: perf_experiment_struct.DeleteExperimentRequest params)
    (api.delete='/api/perf_ui_auto/experiment/delete', api.api_level="0", api.category="perf_experiment")
  HttpResponse getExperimentDetail(1: perf_experiment_struct.GetExperimentDetailRequest params)
    (api.get='/api/perf_ui_auto/experiment/detail', api.api_level="0", api.category="perf_experiment")
  HttpResponse viewFlightByVid(1: perf_experiment_struct.ViewFlightByVidRequest params)
    (api.post='/api/perf_ui_auto/experiment/view_flight_by_vid', api.api_level="0", api.category="perf_experiment")

  // 性能统计相关接口
  HttpResponse getBusinessCaseStats(1: perf_stats_struct.BusinessCaseStatsRequest params)
    (api.get='/api/perf_ui_auto/stats/business_cases', api.api_level="0", api.category="perf_stats")
  HttpResponse getTaskExecutionStats(1: perf_stats_struct.TaskExecutionStatsRequest params)
    (api.get='/api/perf_ui_auto/stats/task_executions', api.api_level="0", api.category="perf_stats")
  HttpResponse getCaseExecutionStats(1: perf_stats_struct.CaseExecutionStatsRequest params)
    (api.get='/api/perf_ui_auto/stats/case_executions', api.api_level="0", api.category="perf_stats")
  HttpResponse getBusinessCaseTrend(1: perf_stats_struct.BusinessCaseTrendRequest params)
    (api.get='/api/perf_ui_auto/stats/business_cases/trend', api.api_level="0", api.category="perf_stats")
  HttpResponse getTaskExecutionTrend(1: perf_stats_struct.TaskExecutionTrendRequest params)
    (api.get='/api/perf_ui_auto/stats/task_executions/trend', api.api_level="0", api.category="perf_stats")
  HttpResponse getCaseExecutionTrend(1: perf_stats_struct.CaseExecutionTrendRequest params)
    (api.get='/api/perf_ui_auto/stats/case_executions/trend', api.api_level="0", api.category="perf_stats")
  HttpResponse getStatsSummary(1: perf_stats_struct.StatsSummaryRequest params)
    (api.get='/api/perf_ui_auto/stats/summary', api.api_level="0", api.category="perf_stats")
}