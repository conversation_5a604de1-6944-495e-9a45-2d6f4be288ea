import json
from datetime import datetime
from typing import Union
class LibraExperimentFieldAndRule:
    field_name: str
    field_desc: str
    field_key: str
    field_type: int
    id: int
    business_id: int
    libra_experiment_field_meta_id: int
    operator_type: int
    is_check_value: int
    expect_value: str
    is_must: bool
    version_code: int
    is_using: bool
    create_time: datetime
    update_time: datetime

    def __init__(self,field_name,field_desc,field_key,field_type,id,business_id,libra_experiment_field_meta_id,
                 operator_type,is_check_value,expect_value,is_must,version_code,is_using,create_time,update_time):
        self.field_name = field_name
        self.field_desc = field_desc
        self.field_key = field_key
        self.field_type = field_type
        self.id = id
        self.business_id = business_id
        self.libra_experiment_field_meta_id = libra_experiment_field_meta_id
        self.operator_type = operator_type
        self.is_check_value = is_check_value
        self.expect_value = expect_value
        self.is_must = is_must
        self.version_code = version_code
        self.is_using = is_using
        self.create_time = create_time
        self.update_time = update_time

class LibraExperimentAndTestReport:
    id: int
    business_id: int
    experiment_name: str
    experiment_creator: str
    experiment_reviewer: str
    experiment_reviewer_result: Union[str, None]
    experiment_app_ids: Union[str, None]
    layer_display_name: Union[str, None]
    experiment_description: Union[str, None]
    experiment_libra_id: int
    experiment_type: Union[str, None]
    experiment_status: Union[str, None]
    experiment_numerical_traffic: Union[float, None]
    experiment_tags: Union[str, None]
    experiment_start_time: Union[str, None]
    experiment_start_ts: Union[datetime, None]
    experiment_end_time: Union[str, None]
    experiment_end_ts: Union[datetime, None]
    experiment_libra_url: Union[str, None]
    experiment_create_time: Union[str, None]
    experiment_create_ts: Union[datetime, None]
    experiment_filter_rule: Union[str, None]
    experiment_filter_rule_device_platform: Union[str, None]
    experiment_filter_rule_version_code: Union[str, None]
    experiment_filter_rule_priority_region: Union[str, None]
    experiment_enable_gradual: Union[str, None]
    experiment_meego_info: Union[str, None]
    experiment_meego_url: Union[str, None]
    experiment_version_info: Union[str, None]
    experiment_metrics_info: Union[str, None]
    create_time: datetime
    update_time: datetime
    experiment_qa_reviewer: str
    experiment_product_info: [str, None]
    test_report_url: Union[str, None]
    test_report_is_pass: Union[bool, None]
    test_report_tester: Union[str, None]

    def __init__(self,id,business_id,experiment_name,experiment_creator,experiment_reviewer,experiment_reviewer_result,
                 experiment_app_ids,layer_display_name,experiment_description,experiment_libra_id,experiment_type,
                 experiment_status,experiment_numerical_traffic,experiment_tags,experiment_start_time,experiment_start_ts,
                 experiment_end_time,experiment_end_ts,experiment_libra_url,experiment_create_time,
                 experiment_create_ts,experiment_filter_rule,experiment_filter_rule_device_platform,
                 experiment_filter_rule_version_code,experiment_filter_rule_priority_region,
                 experiment_enable_gradual,experiment_meego_info,experiment_meego_url,experiment_version_info,
                 experiment_metrics_info,create_time,update_time,experiment_qa_reviewer,experiment_product_info,
                 test_report_url=None,test_report_is_pass=None,test_report_tester=None):
        self.id = id
        self.business_id = business_id
        self.experiment_name = experiment_name
        self.experiment_creator = experiment_creator
        self.experiment_reviewer = json.loads(experiment_reviewer.replace("'","\"")) if experiment_reviewer else experiment_reviewer
        self.experiment_reviewer_result = json.loads(experiment_reviewer_result.replace("'","\"")) if experiment_reviewer_result else experiment_reviewer_result
        self.experiment_app_ids = json.loads(experiment_app_ids.replace("'","\"")) if experiment_app_ids else experiment_app_ids
        self.layer_display_name = layer_display_name
        self.experiment_description = experiment_description
        self.experiment_libra_id = experiment_libra_id
        self.experiment_type = experiment_type
        self.experiment_status = experiment_status
        self.experiment_numerical_traffic = float(experiment_numerical_traffic)
        self.experiment_tags = json.loads(experiment_tags.replace("'","\"")) if experiment_tags else experiment_tags
        self.experiment_start_time = experiment_start_time
        self.experiment_start_ts = experiment_start_ts
        self.experiment_end_time = experiment_end_time
        self.experiment_end_ts = experiment_end_ts
        self.experiment_libra_url = experiment_libra_url
        self.experiment_create_time = experiment_create_time
        self.experiment_create_ts = experiment_create_ts
        self.experiment_filter_rule = json.loads(experiment_filter_rule.replace("'","\"")) if experiment_filter_rule else experiment_filter_rule
        self.experiment_filter_rule_device_platform = json.loads(experiment_filter_rule_device_platform.replace("'","\"")) if experiment_filter_rule_device_platform else experiment_filter_rule_device_platform
        self.experiment_filter_rule_version_code = json.loads(experiment_filter_rule_version_code.replace("'","\"")) if experiment_filter_rule_version_code else experiment_filter_rule_version_code
        self.experiment_filter_rule_priority_region = json.loads(experiment_filter_rule_priority_region.replace("'","\"")) if experiment_filter_rule_priority_region else experiment_filter_rule_priority_region
        self.experiment_enable_gradual = experiment_enable_gradual
        self.experiment_meego_info = json.loads(experiment_meego_info.replace("'","\"")) if experiment_meego_info else experiment_meego_info
        self.experiment_meego_url = experiment_meego_url
        self.experiment_version_info = json.loads(experiment_version_info.replace("'","\"")) if experiment_version_info else experiment_version_info
        self.experiment_metrics_info = json.loads(experiment_metrics_info.replace("'","\"")) if experiment_metrics_info else experiment_metrics_info
        self.create_time = create_time
        self.update_time = update_time
        self.experiment_qa_reviewer = json.loads(experiment_qa_reviewer.replace("'","\"")) if experiment_qa_reviewer else experiment_qa_reviewer
        self.experiment_product_info = json.loads(experiment_product_info.replace("'","\"")) if experiment_product_info else experiment_product_info
        self.test_report_url = test_report_url
        self.test_report_is_pass = test_report_is_pass
        self.test_report_tester = test_report_tester

class AddLibraExperimentReviewResultParams():
    libra_experiment_id: int
    is_pass: bool
    def __init__(self,libra_experiment_id,is_pass):
        self.libra_experiment_id = libra_experiment_id
        self.is_pass = is_pass
class AddLibraExperimentReviewResultRuleDetailParams():
    libra_experiment_review_result_id: int
    libra_experiment_field_review_rule_id: int
    is_pass: bool
    message: str
    def __init__(self,libra_experiment_review_result_id,libra_experiment_field_review_rule_id,is_pass,message):
        self.libra_experiment_review_result_id = libra_experiment_review_result_id
        self.libra_experiment_field_review_rule_id = libra_experiment_field_review_rule_id
        self.is_pass = is_pass
        self.message = message
