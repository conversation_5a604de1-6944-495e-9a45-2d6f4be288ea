import ast
from datetime import datetime,time

class VersionManagerTodoChecklist:
    version_id: int
    version_name: str
    version_qa_owner: str
    version_checklist_id: int
    version_checklist_start_time: datetime
    stage_name: str
    id: int
    name: str
    checklist_desc: str
    stage: int
    remind_type: int
    remind_days: int
    remind_time: time
    reminder_type: int
    reminder: list
    create_time: datetime
    update_time: datetime

    def __init__(self,version_id,version_name,version_qa_owner,version_checklist_id,version_checklist_start_time,
             stage_name,id,name,checklist_desc,stage,remind_type,remind_days,remind_time,
             reminder_type,reminder,create_time,update_time):
        self.version_id= version_id
        self.version_name= version_name
        self.version_qa_owner= version_qa_owner
        self.version_checklist_id= version_checklist_id
        self.version_checklist_start_time= version_checklist_start_time
        self.stage_name= stage_name
        self.id= id
        self.name= name
        self.checklist_desc= checklist_desc
        self.stage= stage
        self.remind_type= remind_type
        self.remind_days= remind_days
        self.remind_time= remind_time
        self.reminder_type= reminder_type
        self.reminder= ast.literal_eval(reminder) if (reminder != '' and reminder is not None) else ast.literal_eval('[]')
        self.create_time= create_time
        self.update_time= update_time
