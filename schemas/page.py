from pydantic import BaseModel, Field, field_validator


class PageSchema(BaseModel):
    """
    分页查询
    """
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=500, description="每页数量")

    @field_validator('page')
    @classmethod
    def validate_page(cls, v):
        if not isinstance(v, int) or v < 1:
            return 1
        return v

    @field_validator('page_size')
    @classmethod
    def validate_page_size(cls, v):
        if not isinstance(v, int) or v < 1:
            return 10
        if v > 500:
            return 500
        return v
