from datetime import datetime
from typing import List

from schemas import BaseSchema


class CiDailyOut(BaseSchema):
    pass


class UpdateCaseDetail(CiDailyOut):
    id: int
    fail_reason_type: int = None
    fail_reason_str: str = None
    improvement_measure_str: str = None
    is_closed: bool = None
    meego_bug_url: str = None
    attribution_type: int = None


class BranchList(CiDailyOut):
    page: int
    page_size: int
    total: int
    branch_list: list


class ResultCodeList(CiDailyOut):
    page: int
    page_size: int
    total: int
    result_code_list: list


class AttributeTypeList(CiDailyOut):
    page: int
    page_size: int
    total: int
    attribute_type_list: list


class CaseListItem(CiDailyOut):
    id: int
    platform: str
    pipeline_type: str
    pipeline_create_time: datetime
    branch: str
    case_id: str
    module: int
    qa_owner: str
    result_code: int
    result_message: str
    fail_reason_type: int
    fail_reason_str: str
    improvement_measure_str: str
    is_closed: bool
    meego_bug_url: str
    attribution_type: int


class CaseList(CiDailyOut):
    page: int
    page_size: int
    total: int
    items: List[CaseListItem]


class MrTaskItem(CiDailyOut):
    id: int
    mr_id: int
    mr_state: str
    mr_create_time: datetime


class TestTaskItem(CiDailyOut):
    id: int
    pipeline_id: int
    branch: str
    pipeline_type: str
    pipeline_user: str
    pipeline_create_time: datetime
    pipeline_finish_time: datetime
    task_url: str


class TestPlatformItem(CiDailyOut):
    id: int
    job_id: int
    platform: str
    watchcat_demo_url: str
    case_log_url: str
    start_time: datetime
    end_time: datetime
    job_url: str
    total_count: int
    pass_count: int
    fail_count: int
    test_task: TestTaskItem


class CaseDetail(CiDailyOut):
    id: int
    case_id: str
    result_code: int
    result_message: str
    module: int
    qa_owner: str
    case_log_url: str
    watchcat_log_url: str
    start_time: datetime
    end_time: datetime
    cost_time: int
    expect_assert: list
    actual_assert: list
    device_info_list: list
    fail_reason_str: str
    fail_reason_type: int
    improvement_measure_str: str
    is_closed: bool
    meego_bug_url: str
    attribution_type: int
    test_platform: TestPlatformItem


class Config:
    orm_mode = True
