from datetime import datetime
from typing import Union
from schemas import BaseSchema

class LarkRebotCardCallbackOut(BaseSchema):
    pass

"""
https://open.larkoffice.com/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-callback-communication
响应体示例
{
    "toast":{
        "type":"info",
        "content":"卡片交互成功",
        "i18n":{
            "zh_cn":"卡片交互成功",
            "en_us":"card action success"
        }
    },
    "card":{
        "type":"raw",
        "data":{
            "config":{
                "enable_forward":true
            },
            "elements":[
                {
                    "tag":"div",
                    "text":{
                        "content":"This is the plain text",
                        "tag":"plain_text"
                    }
                }
            ],
            "header":{
                "template":"blue",
                "title":{
                    "content":"This is the title",
                    "tag":"plain_text"
                }
            }
        }
    }
}

"""

class TestCallbackRes(LarkRebotCardCallbackOut):
    challenge: str

class I18n(LarkRebotCardCallbackOut):
    zh_cn: str
    en_us: str

class Toast(LarkRebotCardCallbackOut):
    type: str
    content: str
    i18n: I18n

class Config(LarkRebotCardCallbackOut):
    enable_forward: bool

class ElementsText(LarkRebotCardCallbackOut):
    content: str
    tag: str

class Elements(LarkRebotCardCallbackOut):
    tag: str
    text: ElementsText

class HeaderTitle(LarkRebotCardCallbackOut):
    content: str
    tag: str

class Header(LarkRebotCardCallbackOut):
    template: str
    title: HeaderTitle

class CardData(LarkRebotCardCallbackOut):
    config: Config
    elements: Elements
    header: Header

class Card(LarkRebotCardCallbackOut):
    type: str
    data: CardData

class LarkRebotCardCallbackRes(LarkRebotCardCallbackOut):
    toast: Toast = None
    card: Card = None