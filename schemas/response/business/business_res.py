from datetime import datetime
from typing import List, Optional
from schemas import BaseSchema

class BusinessOut(BaseSchema):
    pass

class BusinessItemRes(BusinessOut):
    id: int
    business_name: str
    create_time: datetime
    update_time: datetime
    business_dir: Optional[str] = None

class BusinessFetchAllRes(BusinessOut):
    total: int
    items: List[BusinessItemRes]

class BusinessAddRes(BusinessOut):
    id: int

class BusinessUpdateRes(BusinessOut):
    id: int
    business_name: str
    business_dir: Optional[str] = None
