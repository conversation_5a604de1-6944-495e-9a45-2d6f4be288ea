'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2025-01-16 19:31:54
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_stats_res.py
Description: 性能统计响应模型定义
'''
from typing import List
from datetime import date
from schemas import BaseSchema


class PerfStatsOut(BaseSchema):
    """性能统计基础响应模型"""
    pass


class BusinessCaseStatsItem(PerfStatsOut):
    """业务用例统计项"""
    business_id: int
    business_name: str
    total_cases: int
    active_cases: int
    stats_date: date


class BusinessCaseStatsResponse(PerfStatsOut):
    """业务用例统计响应"""
    total: int
    items: List[BusinessCaseStatsItem]


class TaskExecutionStatsItem(PerfStatsOut):
    """任务执行统计项"""
    business_id: int
    business_name: str
    total_executions: int
    success_executions: int
    failed_executions: int
    success_rate: float
    avg_duration: int
    stats_date: date


class TaskExecutionStatsResponse(PerfStatsOut):
    """任务执行统计响应"""
    total: int
    items: List[TaskExecutionStatsItem]


class CaseExecutionStatsItem(PerfStatsOut):
    """用例执行统计项"""
    business_id: int
    business_name: str
    case_id: int
    case_name: str
    total_executions: int
    success_executions: int
    failed_executions: int
    success_rate: float
    avg_duration: int
    stats_date: date


class CaseExecutionStatsResponse(PerfStatsOut):
    """用例执行统计响应"""
    total: int
    items: List[CaseExecutionStatsItem]


class BusinessCaseTrendItem(PerfStatsOut):
    """业务用例趋势项"""
    business_id: int
    business_name: str
    total_cases: int
    stats_date: date


class BusinessCaseTrendResponse(PerfStatsOut):
    """业务用例趋势响应"""
    total: int
    items: List[BusinessCaseTrendItem]


class TaskExecutionTrendItem(PerfStatsOut):
    """任务执行趋势项"""
    business_id: int
    business_name: str
    total_executions: int
    success_executions: int
    failed_executions: int
    success_rate: float
    avg_duration: int
    stats_date: date


class TaskExecutionTrendResponse(PerfStatsOut):
    """任务执行趋势响应"""
    total: int
    items: List[TaskExecutionTrendItem]


class CaseExecutionTrendItem(PerfStatsOut):
    """用例执行趋势项"""
    business_id: int
    business_name: str
    case_id: int
    case_name: str
    total_executions: int
    success_executions: int
    failed_executions: int
    success_rate: float
    avg_duration: int
    stats_date: date


class CaseExecutionTrendResponse(PerfStatsOut):
    """用例执行趋势响应"""
    total: int
    items: List[CaseExecutionTrendItem]
