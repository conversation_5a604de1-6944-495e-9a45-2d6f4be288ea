'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_metric_res.py
Description: 性能指标响应模型定义
'''
from typing import List, Optional, Dict
from datetime import datetime
from schemas import BaseSchema

class MetricOut(BaseSchema):
    pass

class PerfMetricInfo(MetricOut):
    """性能指标信息"""
    id: int
    metric_key: str
    metric_name: str
    metric_type: int  # 1-common 2-android 3-ios
    metric_category: int  # 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
    metric_unit: Optional[str]
    metric_desc: Optional[str]
    query_conditions: Optional[Dict]
    creator: str
    create_time: datetime
    update_time: datetime

class MetricListResponse(BaseSchema):
    """性能指标列表响应"""
    page: int
    page_size: int
    total: int
    items: List[PerfMetricInfo]

class MetricResponse(BaseSchema):
    """单个性能指标响应"""
    metric: Optional[PerfMetricInfo]

class BatchMetricResponse(BaseSchema):
    """批量性能指标响应"""
    success_count: int
    failed_count: int
    success_metrics: List[PerfMetricInfo]
    failed_metrics: List[dict]
