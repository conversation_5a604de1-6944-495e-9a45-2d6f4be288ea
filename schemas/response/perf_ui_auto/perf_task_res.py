'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-13 11:16:38
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_task_res.py
Description: 
'''
from typing import List, Union, Optional, Dict
from datetime import datetime

from schemas import BaseSchema
from pydantic import Field

from schemas.response.business.business_res import BusinessItemRes
from schemas.response.perf_ui_auto.perf_app_res import AddPerfAppGroupRes, PerfAppListItemRes
from schemas.response.perf_ui_auto.perf_case_res import CaseDetailItem
from schemas.response.perf_ui_auto.perf_client_res import ClientItem
from schemas.response.perf_ui_auto.perf_device_res import PerfDeviceItem
from schemas.response.perf_ui_auto.perf_config_res import PerfConfigItem
from schemas.response.perf_ui_auto.perf_experiment_res import ExperimentItem
from schemas.response.perf_ui_auto.perf_account_res import PerfAccountItem


class PerfTaskOut(BaseSchema):
    pass

class PerfTaskListItem(PerfTaskOut):
    id: int
    name: str
    business_id: int
    client_id: int
    config_id: Optional[int] = None
    experiment_id: Optional[int] = None
    owner: str
    type: int
    perf_tool_type: Optional[int] = None
    status: int
    result: int
    duration: int
    start_time: Union[datetime, None]
    end_time: Union[datetime, None]
    create_time: datetime
    update_time: datetime
    business_name: str

class PerfTaskListRes(PerfTaskOut):
    page: int
    page_size: int
    total: int
    items: List[PerfTaskListItem]

class PerfSubTaskRes(PerfTaskOut):
    id: int
    task_id: int
    name: str
    platform: int
    case_id_list: List[int]
    perf_device_id: int
    app_group_id: int
    perf_account_id: Optional[int] = None
    video_url: str
    app_install_type: int
    finish_notice: Union[list, None]
    perf_collect_type_list: List[int]
    perf_collect_duration: int
    perf_collect_interval: int
    perf_collect_mode: int
    perf_device_power_level: int
    case_run_count: int
    case_retry_count: int
    enabled: bool
    status: int
    result: int
    duration: int
    start_time: Union[datetime, None]
    end_time: Union[datetime, None]
    error_code: Optional[int] = None
    error_title: Optional[str] = None
    error_detail: Optional[str] = None
    create_time: datetime
    update_time: datetime
    

class PerfRequestRes(PerfTaskOut):
    event_key: str
    # 1. perf_request_cancel_task: 任务取消请求
    request_body: Union[Dict, None]
    # 1、任务取消请求示例：{'task_id': 104}
    request_time: Union[datetime, None]


class PerfTaskRes(PerfTaskOut):
    id: int
    name: str = None
    business_id: int = None
    client_id: int = None
    config_id: Optional[int] = None
    experiment_id: Optional[int] = None
    owner: str = None
    type: int = None
    perf_tool_type: Optional[int] = None
    status: int = None
    result: int = None
    duration: int = None
    start_time: Union[datetime, None] = None
    end_time: Union[datetime, None] = None
    sub_tasks: List[PerfSubTaskRes] = None
    create_time: datetime
    update_time: datetime

class UploadPerfCaseRunDetailItem(BaseSchema):
    id: int
    sub_task_id: Optional[int] = None
    device_id: Optional[int] = None
    app_id: Optional[int] = None
    case_id: Optional[int] = None
    case_log_tos_urls: Optional[Dict[str, str]] = None
    perf_data_tos_urls: Optional[Dict[str, str]] = None
    screenshot_tos_urls: Optional[List[str]] = None
    cpu_profile_tos_url: Optional[str] = None
    status: Optional[int] = None
    version_type: Optional[int] = None  # 版本类型 1-实验组 2-对照组

class PerfSubTaskAllRes(PerfTaskOut):
    id: int
    task_id: int
    name: str
    platform: int
    case_list: List[CaseDetailItem]
    perf_account: Optional[PerfAccountItem] = None
    video_url: str
    perf_collect_mode: int
    app_install_type: int
    finish_notice: Union[list, None]
    perf_collect_type_list: List[int]
    perf_collect_duration: int
    perf_collect_interval: int
    perf_device_power_level: int
    case_run_count: int
    case_retry_count: int
    enabled: bool
    status: int
    result: int
    duration: int
    start_time: Union[datetime, None]
    end_time: Union[datetime, None]
    perf_device: PerfDeviceItem
    app_group: AddPerfAppGroupRes
    error_code: Optional[int] = None
    error_title: Optional[str] = None
    error_detail: Optional[str] = None
    create_time: datetime
    update_time: datetime

class PerfTaskAllRes(PerfTaskOut):
    id: int
    name: str = None
    experiment: Optional[ExperimentItem] = None
    owner: str = None
    type: int = None
    perf_tool_type: Optional[int] = None
    status: int = None
    result: int = None
    duration: int = None
    start_time: Union[datetime, None] = None
    end_time: Union[datetime, None] = None
    sub_tasks: List[PerfSubTaskAllRes] = None
    create_time: datetime
    update_time: datetime
    business: BusinessItemRes
    client: ClientItem
    config: PerfConfigItem
    app_group: AddPerfAppGroupRes

class PerfCaseRunDetailItem(PerfTaskOut):
    id: int
    sub_task_id: Optional[int]
    device_id: Optional[int] = None
    app_id: Optional[int] = None
    case_id: Optional[int] = None
    case_log_tos_urls: Optional[Dict[str, str]] = None
    perf_data_tos_urls: Optional[Dict[str, str]] = None
    screenshot_tos_urls: Optional[List[str]] = None
    cpu_profile_tos_url: Optional[str] = None
    status: Optional[int] = None
    version_type: Optional[int] = None  # 版本类型 1-实验组 2-对照组
    create_time: datetime
    update_time: datetime
    case: CaseDetailItem
    app: PerfAppListItemRes
    device: PerfDeviceItem

class PerfCaseRunDetailRes(PerfTaskOut):
    page: int
    page_size: int
    total: int
    items: List[PerfCaseRunDetailItem]

class CaseExecutionStatsItem(BaseSchema):
    """单个用例的执行统计"""
    case_id: int = Field(..., description="用例ID")
    total_count: int = Field(default=0, description="总执行次数")
    success_count: int = Field(default=0, description="成功次数")
    failed_count: int = Field(default=0, description="失败次数")
    timeout_count: int = Field(default=0, description="超时次数")
    canceled_count: int = Field(default=0, description="取消次数")
    retry_count: int = Field(default=0, description="重试次数")

class AppExecutionStatsItem(BaseSchema):
    """
    分组的执行统计
    - 版本回归时只用app_id
    - Libra实验时只用version_type
    """
    app_id: Optional[int] = Field(None, description="应用ID（版本回归时使用）")
    version_type: Optional[int] = Field(None, description="版本类型（Libra实验时使用，1=实验组，2=对照组）")
    cases: List[CaseExecutionStatsItem] = Field(default_factory=list, description="用例统计列表")

class CaseExecutionStatsListRes(BaseSchema):
    """用例执行统计列表响应"""
    total_cases: int = Field(default=0, description="用例总数")
    total_executions: int = Field(default=0, description="总执行次数")
    success_rate: float = Field(default=0.0, description="总体成功率")
    group_stats: List[AppExecutionStatsItem] = Field(default_factory=list, description="分组执行统计列表")
