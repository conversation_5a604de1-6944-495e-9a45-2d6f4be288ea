'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_data_res.py
Description: 性能数据响应模型定义
'''
from typing import List, Dict, Optional
from datetime import datetime
from schemas import BaseSchema

class PerfDataOut(BaseSchema):
    id: int
    case_run_detail_id: int
    is_avg: bool
    platform: int
    metrics_data: Dict[str, float]
    create_time: datetime
    update_time: datetime

class PerfCaseRunDetailRes(BaseSchema):
    id: int
    sub_task_id: int
    device_id: int
    app_id: int
    case_id: int
    case_log_tos_urls: Optional[Dict[str, str]]
    perf_data_tos_urls: Optional[Dict[str, str]]
    screenshot_tos_urls: List[str]
    status: int
    version_type: int
    create_time: datetime
    update_time: datetime
    app_type: int