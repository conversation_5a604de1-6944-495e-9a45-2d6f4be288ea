'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_experiment_res.py
Description: 实验相关响应模型
'''
from typing import List, Optional
from datetime import datetime
from schemas import BaseSchema

class ExperimentOut(BaseSchema):
    pass

class ExperimentItem(ExperimentOut):
    """实验列表项"""
    id: int
    name: str
    hit_type: int  # 1-DID命中，2-UID命中
    experiment_group_version_ids: List[int]
    control_group_version_ids: List[int]
    description: Optional[str]
    creator: Optional[str]
    create_time: datetime
    update_time: datetime

class ExperimentListResponse(BaseSchema):
    """实验列表响应"""
    page: int
    page_size: int
    total: int
    items: List[ExperimentItem]