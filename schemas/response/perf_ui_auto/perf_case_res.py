'''
Author: he<PERSON><PERSON><PERSON>.oxep <EMAIL>
Date: 2024-11-11 11:40:15
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_case_res.py
Description: 
'''
from typing import List, Optional
from datetime import datetime

from schemas import BaseSchema


class PerfCaseOut(BaseSchema):
    pass

class CaseItem(PerfCaseOut):
    name: str
    udid: str
    model: str
    sys_type: int
    sys_version: str
    brand: str
    resolution: str
    uuid: str
    connect_type: int
    state: int
    ip: str
    user: str
    owner: str
    
class UpdateCaseItem(PerfCaseOut):
    case_items: List[CaseItem]


class CaseListItem(PerfCaseOut):
    case_items: List[CaseItem]

class CaseDetailItem(PerfCaseOut):
    case_id: int
    business_id: int
    case_name: str
    case_level: Optional[str] = None
    owner: str
    timeout: int
    platform: str
    device_count: int
    title: str
    description: Optional[str] = None
    tags: Optional[str] = None
    dir: Optional[str] = None
    create_time: datetime
    update_time: datetime