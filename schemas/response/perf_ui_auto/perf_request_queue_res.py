from typing import Optional, List, Dict
from pydantic import BaseModel, Field

class PerfRequestData(BaseModel):
    """请求数据结构"""
    event_key: str = Field(..., description="事件类型")
    request_body: Optional[Dict] = Field(None, description="请求体")
    request_time: Optional[str] = Field(None, description="请求时间")

class GetAllRequestsRes(BaseModel):
    """获取所有请求的响应"""
    requests: List[PerfRequestData] = Field(..., description="请求列表") 