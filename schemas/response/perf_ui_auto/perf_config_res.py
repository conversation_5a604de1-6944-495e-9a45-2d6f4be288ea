'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_config_res.py
Description: 性能配置响应模型
'''
from typing import List, Optional, Dict
from datetime import datetime
from schemas import BaseSchema
from schemas.request.perf_ui_auto.perf_config_req import ComparisonType, ValueType


class PerfConfigOut(BaseSchema):
    """性能配置基础响应模型"""
    pass


class PerfMetricConfigItem(PerfConfigOut):
    """性能指标配置项"""
    metric_id: int
    metric_key: str
    metric_name: str
    metric_type: int  # 1-common 2-android 3-ios
    metric_category: int  # 指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标
    metric_unit: Optional[str]
    metric_desc: Optional[str]
    query_conditions: Optional[Dict]
    is_important: bool
    comparison_type: Optional[ComparisonType]
    value_type: Optional[ValueType]
    threshold_value: Optional[float]


class PerfConfigItem(PerfConfigOut):
    """性能配置项"""
    id: int
    business_id: int
    config_name: str
    metrics: List[PerfMetricConfigItem]
    creator: str
    create_time: datetime
    update_time: datetime


class PerfConfigListResponse(PerfConfigOut):
    """性能配置列表响应"""
    page: int
    page_size: int
    total: int
    items: List[PerfConfigItem]
