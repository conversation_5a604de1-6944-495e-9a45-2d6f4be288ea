from schemas import BaseSchema
from datetime import datetime
from typing import Union, List
class PerfAppOut(BaseSchema):
    pass
class PerfAppListItemRes(PerfAppOut):
    id: int
    business_id: int
    platform: int
    name: str
    version: str
    app_type: int
    url: str
    jen<PERSON>_build_result_url: Union[str, None]
    repackage_cert: Union[str, None]
    creator: str
    create_time: datetime
    update_time: datetime
    business_name: Union[str, None] = None

class PerfAppListRes(PerfAppOut):
    page: int
    page_size: int
    total: int
    items: List[PerfAppListItemRes]

class PerfAppGroupListItemRes(PerfAppOut):
    id: int
    business_id: int
    name: str
    version: str
    creator: str
    create_time: datetime
    update_time: datetime
    android_perf_app_id_list: list
    android_assist_app_id_list: list
    ios_perf_app_id_list: list
    ios_assist_app_id_list: list
    business_name: str


class PerfAppGroupListRes(PerfAppOut):
    page: int
    page_size: int
    total: int
    items: List[PerfAppGroupListItemRes]

class AddPerfAppGroupRes(PerfAppOut):
    id: int
    business_id: int
    name: str
    version: str
    android_perf_app_id_list: str
    android_assist_app_id_list: str
    ios_perf_app_id_list: str
    ios_assist_app_id_list: str
    create_time: datetime
    update_time: datetime


class PerfAppGroupDetailRes(PerfAppOut):
    id: int
    business_id: int
    name: str
    version: str
    create_time: datetime
    update_time: datetime
    android_perf_app_id_list: List[PerfAppListItemRes]
    android_assist_app_id_list: List[PerfAppListItemRes]
    ios_perf_app_id_list: List[PerfAppListItemRes]
    ios_assist_app_id_list: List[PerfAppListItemRes]

