'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_account_res.py
Description: 
'''
from schemas import BaseSchema
from datetime import datetime
from typing import Optional, List

class PerfAccountOut(BaseSchema):
    pass

class PerfAccountItem(PerfAccountOut):
    """账号列表项"""
    id: int
    business_id: int
    uid: str
    iphone: str
    username: str
    captcha: Optional[str]
    email: Optional[str]
    pwd: Optional[str]
    app: int
    owner: str
    is_occupied: int
    account_type: int
    country: Optional[str]
    country_code_alpha2: Optional[str]
    phone_area_code: Optional[str]
    create_time: datetime
    update_time: datetime

class PerfAccountListResponse(BaseSchema):
    """账号列表响应"""
    page: int
    page_size: int
    total: int
    items: List[PerfAccountItem]