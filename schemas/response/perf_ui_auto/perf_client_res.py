from typing import List, Optional

from schemas import BaseSchema
from schemas.response.business.business_res import BusinessItemRes


class PerfClientOut(BaseSchema):
    id: int


class ClientItem(PerfClientOut):
    business_id: int
    name: str
    sys_type: int
    mac_address: str
    ipv4: str
    ipv6: str
    state: int
    port: int
    owner: str
    business: Optional[BusinessItemRes] = None


class PerfClientList(BaseSchema):
    total: int
    client_items: List[ClientItem]
