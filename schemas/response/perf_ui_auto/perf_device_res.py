'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/schemas/response/perf_ui_auto/perf_device_res.py
Description: 
'''
from datetime import datetime
from pydantic import BaseModel
from typing import List, Optional


class PerfDeviceItem(BaseModel):
    """设备信息响应模型"""
    id: int
    client_id: int
    name: str
    udid: str
    model: str
    sys_type: int
    sys_version: str
    brand: str
    resolution: str
    connect_type: List[int]
    state: int
    ip: Optional[str] = None
    serial_port: Optional[str] = None
    is_occupied: int
    user: Optional[str] = None
    owner: Optional[str] = None
    create_time: datetime
    update_time: datetime

    class Config:
        from_attributes = True


class PerfDeviceListResponse(BaseModel):
    """设备列表响应模型"""
    page: int
    page_size: int
    total: int
    items: List[PerfDeviceItem]


class PerfDeviceDetailItem(BaseModel):
    """设备详情响应模型"""
    id: int
    name: str
    udid: str
    model: str
    sys_type: int
    sys_version: str
    brand: str
    resolution: str
    uuid: str
    connect_type: int
    state: int
    ip: str
    user: str
    owner: str