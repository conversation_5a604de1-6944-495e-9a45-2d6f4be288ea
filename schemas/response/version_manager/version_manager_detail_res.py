from datetime import datetime, time
from typing import List, Union
from schemas import BaseSchema

class VersionManagerDetailOut(BaseSchema):
    pass

class VersionStageRes(VersionManagerDetailOut):
    id: int
    version_id: int
    stage: int
    name: str
    stage_order: int
    start_time: datetime
    end_time: datetime

class VersionDetailRes(VersionManagerDetailOut):
    id: int
    version_name: str
    version_qa_owner: str
    # version_rd_owner: str
    platforms: list
    start_time: datetime
    end_time: datetime
    stage_list: List[VersionStageRes]
    current_stage: Union[VersionStageRes, None] = None

class ChecklistRes(VersionManagerDetailOut):
    id: int
    name: str
    checklist_desc: str
    stage: int
    remind_type: int
    remind_days: int
    reminder_type: int
    reminder: list
    remind_time: time

class VersionChecklistDetailItemRes(VersionManagerDetailOut):
    id: int
    version_id: int
    checklist_id: int
    start_time:  datetime
    is_finished: bool
    comment: Union[str, None] = None
    finish_time:  Union[datetime, None] = None
    operator:  Union[str, None] = None
    checklist: ChecklistRes


class VersionChecklistDetailListRes(VersionManagerDetailOut):
    items: List[VersionChecklistDetailItemRes]

class CheckListCardCallbackToastRes(VersionManagerDetailOut):
    type: str
    content: str
class CheckListCardCallbackRes(VersionManagerDetailOut):
    toast: CheckListCardCallbackToastRes


