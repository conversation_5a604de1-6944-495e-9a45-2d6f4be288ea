from datetime import datetime
from typing import List, Union
from schemas import BaseSchema
from schemas.response.version_manager.version_manager_detail_res import VersionStageRes

class VersionManagerListOut(BaseSchema):
    pass

class VersionListItemRes(VersionManagerListOut):
    id: int
    version_name: str
    version_qa_owner: str
    # version_rd_owner: str
    platforms: list
    start_time: datetime
    end_time: datetime
    current_stage: Union[VersionStageRes, None] = None

class VersionListRes(VersionManagerListOut):
    page: int
    page_size: int
    total: int
    items: List[VersionListItemRes]