from pydantic import BaseModel, Field
from typing import Optional, List

class StreamBaseResponse(BaseModel):
    """基础流响应模型"""
    status: str = Field(..., description="操作状态")
    message: str = Field(..., description="操作消息")
    pid: int = Field(..., description="推流进程ID")

class PushStreamResponse(StreamBaseResponse):
    """推流响应模型"""
    command: str = Field(..., description="执行的命令")

class StopStreamResponse(StreamBaseResponse):
    """停止推流响应模型"""
    pass

class StartupStreamingResponse(BaseModel):
    """批量启动推流响应模型"""
    pids: List[int] = Field(..., description="成功启动的推流进程ID列表")
    message: str = Field(..., description="操作结果消息")

class ShutdownStreamingResponse(BaseModel):
    """批量关闭推流响应模型"""
    success: bool = Field(..., description="是否全部成功关闭")
    message: str = Field(..., description="操作结果消息")
