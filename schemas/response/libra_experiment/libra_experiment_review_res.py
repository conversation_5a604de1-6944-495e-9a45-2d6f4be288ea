from datetime import datetime
from typing import List, Union
from schemas import BaseSchema

class LibraExperimentReviewOut(BaseSchema):
    pass

class LibraExperimentRes(LibraExperimentReviewOut):
    id: int
    business_id: int
    experiment_name: str
    experiment_creator: str
    experiment_reviewer: str
    experiment_reviewer_result: Union[str, None]
    experiment_app_ids: Union[str, None]
    layer_display_name: Union[str, None]
    experiment_description: Union[str, None]
    experiment_libra_id: int
    experiment_type: Union[str, None]
    experiment_status: Union[str, None]
    experiment_numerical_traffic: Union[float, None]
    experiment_tags: Union[str, None]
    experiment_start_time: Union[str, None]
    experiment_start_ts: Union[datetime, None]
    experiment_end_time: Union[str, None]
    experiment_end_ts: Union[datetime, None]
    experiment_libra_url: Union[str, None]
    experiment_create_time: Union[str, None]
    experiment_create_ts: Union[datetime, None]
    experiment_filter_rule: Union[str, None]
    experiment_filter_rule_device_platform: Union[str, None]
    experiment_filter_rule_version_code: Union[str, None]
    experiment_filter_rule_priority_region: Union[str, None]
    experiment_enable_gradual: Union[str, None]
    experiment_meego_info: Union[str, None]
    experiment_meego_url: Union[str, None]
    experiment_version_info: Union[str, None]
    experiment_metrics_info: Union[str, None]
    create_time: datetime
    update_time: datetime
    experiment_qa_reviewer: str
    experiment_product_info: Union[str, None]

class LibraExperimentLatestRuleRes(LibraExperimentReviewOut):
    field_name: str
    field_desc: str
    field_key: str
    field_type: int
    id: int
    business_id: int
    libra_experiment_field_meta_id: int
    operator_type: int
    is_check_value: int
    expect_value: str
    is_must: bool
    version_code: int
    is_using: bool
    create_time: datetime
    update_time: datetime

class LibraExperimentFieldAndRuleRes(LibraExperimentReviewOut):
    field_name: str
    field_desc: str
    field_key: str
    field_type: int
    id: int
    business_id: int
    libra_experiment_field_meta_id: int
    operator_type: int
    is_check_value: int
    expect_value: str
    is_must: bool
    version_code: int
    is_using: bool
    create_time: datetime
    update_time: datetime