

from datetime import datetime
from tokenize import String
from typing import Union
from schemas import BaseSchema
from pydantic import Field
from typing import Optional

class LibraExperimentReviewIn(BaseSchema):
    pass

class AddLibraExperimentParams(LibraExperimentReviewIn):
    business_name: str
    experiment_name: str
    experiment_creator: str
    experiment_reviewer_result: Union[str, None]
    experiment_app_ids: Union[str, None]
    layer_display_name: Union[str, None]
    experiment_description: Union[str, None]
    experiment_libra_id: int
    experiment_type: Union[str, None]
    experiment_status: Union[str, None]
    experiment_numerical_traffic: Union[float, None]
    experiment_tags: Union[str, None]
    experiment_start_time: Union[str, None]
    experiment_start_ts: Union[datetime, None]
    experiment_end_time: Union[str, None]
    experiment_end_ts: Union[datetime, None]
    experiment_libra_url: Union[str, None]
    experiment_create_time: Union[str, None]
    experiment_create_ts: Union[datetime, None]
    experiment_filter_rule: Union[str, None]
    experiment_enable_gradual: Union[str, None]
    experiment_meego_info: Union[str, None]
    experiment_version_info: Union[str, None]
    experiment_metrics_info: Union[str, None]
    experiment_qa_reviewer: str
    experiment_product_info: Union[str, None]


class AddLibraExperimentRuleParams(BaseSchema):
    business_id: int = Field(..., description="business id")
    libra_experiment_field_meta_id: int = Field(..., description="实验规则元数据id")
    operator_type: int = Field(..., description="规则类型")
    is_check_value: int = Field(..., description="是否检查值本身")
    expect_value: str = Field(..., description="期望的值")
    is_must: int = Field(..., description="是否必须通过")
    version_code: int = Field(..., description="规则版本号")
    is_using: int = Field(..., description="是否启用")


class UpdateLibraExperimentRuleParams(BaseSchema):
    id: int = Field(..., description="规则id")
    business_id: Optional[int] = Field(None, description="business id")
    libra_experiment_field_meta_id: Optional[int] = Field(None, description="实验规则元数据id")
    operator_type: Optional[int] = Field(None, description="规则类型")
    is_check_value: Optional[int] = Field(None, description="是否检查值本身")
    expect_value: Optional[str] = Field(None, description="期望的值")
    is_must: Optional[int] = Field(None, description="是否必须通过")
    version_code: Optional[int] = Field(None, description="规则版本号")
    is_using: Optional[int] = Field(None, description="是否启用")