'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_experiment_req.py
Description: 实验相关请求模型
'''
from typing import List, Optional
from pydantic import BaseModel, Field
from schemas.page import PageSchema

class CreateExperimentRequest(BaseModel):
    """创建实验请求"""
    name: str = Field(..., description="实验名称")
    hit_type: int = Field(..., description="命中类型：1-DID命中，2-UID命中")
    experiment_group_version_ids: List[int] = Field(..., description="实验组版本IDs")
    control_group_version_ids: List[int] = Field(..., description="对照组版本IDs")
    description: Optional[str] = Field(None, description="实验描述")
    creator: str = Field(..., description="创建者")

class ListExperimentsRequest(PageSchema):
    """查询实验列表请求"""
    name: Optional[str] = Field(None, description="实验名称")
    hit_type: Optional[int] = Field(None, description="命中类型：1-DID命中，2-UID命中")

class UpdateExperimentRequest(BaseModel):
    """更新实验请求"""
    id: int = Field(..., description="实验ID")
    name: Optional[str] = Field(None, description="实验名称")
    hit_type: Optional[int] = Field(None, description="命中类型：1-DID命中，2-UID命中")
    experiment_group_version_ids: Optional[List[int]] = Field(None, description="实验组版本IDs")
    control_group_version_ids: Optional[List[int]] = Field(None, description="对照组版本IDs")
    description: Optional[str] = Field(None, description="实验描述")

class DeleteExperimentRequest(BaseModel):
    """删除实验请求"""
    id: int = Field(..., description="实验ID")

class GetExperimentDetailRequest(BaseModel):
    """获取实验详情请求"""
    id: int = Field(..., description="实验ID")

class ViewFlightByVidRequest(BaseModel):
    """根据vid列表查询实验信息请求"""
    vids: List[int] = Field(..., description="实验vid列表")
