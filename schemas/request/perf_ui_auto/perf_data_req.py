'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-18 19:13:28
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_data_req.py
Description: 性能数据请求模型定义
'''
from typing import Optional, Dict
from schemas import BaseSchema


class PerfDataIn(BaseSchema):
    id: Optional[int] = None
    case_run_detail_id: int
    is_avg: bool = False
    platform: int
    metrics_data: Dict[str, float]

class PerfDataReportParams(BaseSchema):
    id: int


class PerfDataTosUrlParams(BaseSchema):
    sub_task_id: int
    case_id: int
    app_id: int