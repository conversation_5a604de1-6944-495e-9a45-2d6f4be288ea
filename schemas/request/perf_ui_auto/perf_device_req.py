'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_device_req.py
Description: 
'''
from typing import List, Optional
from pydantic import BaseModel, Field


class GetDeviceListParams(BaseModel):
    """获取设备列表的请求参数"""
    client_id: Optional[int] = Field(default=None, description="客户端ID")
    sys_type: Optional[int] = Field(default=None, description="系统类型 1-Android 2-iOS")
    is_occupied: Optional[int] = Field(default=None, description="是否被占用 0-未占用 1-已占用")

class UploadPerfDeviceParams(BaseModel):
    """上传性能测试设备的请求参数"""
    name: str = Field(..., description="设备名称")
    udid: str = Field(..., description="设备唯一标识符")
    model: str = Field(..., description="型号")
    sys_type: int = Field(..., description="系统类型 1-Android 2-iOS")
    sys_version: str = Field(..., description="系统版本")
    brand: str = Field(..., description="品牌")
    resolution: str = Field(..., description="分辨率")
    connect_type: List[int] = Field(..., description="连接类型列表 [1-USB, 2-WiFi]")
    state: int = Field(..., description="设备状态 0-离线 1-在线")
    ip: Optional[str] = Field(None, description="IP地址")
    serial_port: Optional[str] = Field(None, description="串口号")
    is_occupied: int = Field(0, description="是否被占用 0-未占用 1-已占用")
    user: Optional[str] = Field(None, description="使用者")
    owner: Optional[str] = Field(None, description="所有者")
    