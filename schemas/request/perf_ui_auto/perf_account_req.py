'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_account_req.py
Description: 
'''
from typing import Optional
from schemas import BaseSchema
from schemas.page import PageSchema
from pydantic import Field

class PerfAccountIn(BaseSchema):
    pass

class GetAccountListRequest(PageSchema):
    """获取账号列表的请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID")
    is_occupied: Optional[int] = Field(None, description="是否被占用 0-未占用 1-已占用")
    account_type: Optional[int] = Field(None, description="账号类型 1-性能测试账号 2-普通账号")

class AddAccountParams(BaseSchema):
    """添加账号的请求参数"""
    # 必填字段
    business_id: int = Field(..., description="业务线ID")
    uid: str = Field(..., description="用户ID")
    iphone: str = Field(..., description="虚拟手机号")
    username: str = Field(..., description="用户名")
    owner: str = Field(..., description="负责人")
    
    # 可选字段
    captcha: Optional[str] = Field(None, description="验证码")
    email: Optional[str] = Field(None, description="邮箱")
    pwd: Optional[str] = Field(None, description="密码")
    country: Optional[str] = Field(None, description="国家名称(例如:中国,美国)")
    country_code_alpha2: Optional[str] = Field(None, description="ISO 3166-1 国家二字码(例如:CN,US)")
    phone_area_code: Optional[str] = Field(None, description="电话区号(例如:86,1)")
    
    # 有默认值的字段
    app: int = Field(default=0, description="APP")
    is_occupied: int = Field(default=0, description="是否被占用 0-未占用 1-已占用")
    account_type: int = Field(default=2, description="账号类型 1-性能测试账号 2-普通账号")

class UpdateAccountParams(BaseSchema):
    """更新账号的请求参数"""
    # 必填字段
    id: int = Field(..., description="账号ID")
    
    # 可选字段
    business_id: Optional[int] = Field(None, description="业务线ID")
    uid: Optional[str] = Field(None, description="用户ID")
    iphone: Optional[str] = Field(None, description="虚拟手机号")
    username: Optional[str] = Field(None, description="用户名")
    owner: Optional[str] = Field(None, description="负责人")
    captcha: Optional[str] = Field(None, description="验证码")
    email: Optional[str] = Field(None, description="邮箱")
    pwd: Optional[str] = Field(None, description="密码")
    country: Optional[str] = Field(None, description="国家名称(例如:中国,美国)")
    country_code_alpha2: Optional[str] = Field(None, description="ISO 3166-1 国家二字码(例如:CN,US)")
    phone_area_code: Optional[str] = Field(None, description="电话区号(例如:86,1)")
    app: Optional[int] = Field(None, description="APP")
    is_occupied: Optional[int] = Field(None, description="是否被占用 0-未占用 1-已占用")
    account_type: Optional[int] = Field(None, description="账号类型 1-性能测试账号 2-普通账号")