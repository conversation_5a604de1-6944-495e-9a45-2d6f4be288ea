'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 19:13:28
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_metric_req.py
Description: 性能指标请求模型定义
'''
from typing import Optional, List, Dict
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
from defines.perf_ui_auto_define import METRIC_CATEGORY_TRACE, METRIC_CATEGORY_BYTEIO
from schemas.page import PageSchema

class TraceQueryConditions(BaseModel):
    """Trace采集指标查询条件"""
    namespace: str = Field(..., description="命名空间")
    start: int = Field(..., description="开始时间戳", ge=0)
    end: int = Field(..., description="结束时间戳", ge=0)
    offset: int = Field(0, description="偏移量", ge=0)
    limit: int = Field(200, description="限制数量", ge=1, le=1000)
    filter: List = Field(default_factory=list, description="过滤条件")
    query_string: str = Field(..., description="查询字符串")
    owner: str = Field(..., description="所有者")

    @field_validator('end')
    @classmethod
    def validate_time_range(cls, v, info):
        if 'start' in info.data and v <= info.data['start']:
            raise ValueError("end时间必须大于start时间")
        return v

class ByteIOQueryConditions(BaseModel):
    """ByteIO指标查询条件"""
    app_id: int = Field(..., description="应用ID")
    device_id: int = Field(..., description="设备ID")
    start_time: int = Field(..., description="开始时间戳", ge=0)
    end_time: int = Field(..., description="结束时间戳", ge=0)
    log_types: List[str] = Field(..., description="日志类型列表")
    event_names: List[str] = Field(..., description="事件名称列表")

    @field_validator('end_time')
    @classmethod
    def validate_time_range(cls, v, info):
        if 'start_time' in info.data and v <= info.data['start_time']:
            raise ValueError("end_time必须大于start_time")
        return v

class BaseMetricRequest(BaseModel):
    """基础性能指标请求模型"""
    metric_key: str = Field(..., description="指标key", min_length=1, max_length=64)
    metric_name: str = Field(..., description="指标名称", min_length=1, max_length=128)
    metric_type: int = Field(1, description="指标类型 1-common 2-android 3-ios", ge=1, le=3)
    metric_category: int = Field(1, description="指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标", ge=1, le=4)
    metric_unit: Optional[str] = Field(None, description="指标单位", max_length=32)
    metric_desc: Optional[str] = Field(None, description="指标描述", max_length=255)
    query_conditions: Optional[Dict] = Field(None, description="查询条件配置")

    @field_validator('query_conditions')
    @classmethod
    def validate_query_conditions(cls, v, info):
        if 'metric_category' not in info.data:
            return v
            
        metric_category = info.data['metric_category']
        
        # 对于Trace采集指标或ByteIO指标，必须提供查询条件
        if metric_category in [METRIC_CATEGORY_TRACE, METRIC_CATEGORY_BYTEIO]:
            if not v:
                raise ValueError(f"指标类型{metric_category}必须提供查询条件")
            
            try:
                if metric_category == METRIC_CATEGORY_TRACE:  # Trace采集指标
                    TraceQueryConditions.model_validate(v)
                elif metric_category == METRIC_CATEGORY_BYTEIO:  # ByteIO指标
                    ByteIOQueryConditions.model_validate(v)
            except Exception as e:
                raise ValueError(f"查询条件验证失败: {str(e)}")
        else:
            # 对于其他类型的指标，query_conditions应该为空
            if v:
                raise ValueError(f"指标类型{metric_category}不应该提供查询条件")
                
        return v

class CreateMetricRequest(BaseMetricRequest):
    """创建性能指标请求"""
    creator: str = Field(..., description="创建者", min_length=1, max_length=64)

class BatchCreateMetricRequest(BaseModel):
    """批量创建性能指标请求"""
    metrics: List[CreateMetricRequest] = Field(..., description="性能指标列表", min_items=1, max_items=100)

class UpdateMetricRequest(BaseModel):
    """更新性能指标请求"""
    id: int = Field(..., description="指标ID", gt=0)
    metric_key: Optional[str] = Field(None, description="指标key", min_length=1, max_length=64)
    metric_name: Optional[str] = Field(None, description="指标名称", min_length=1, max_length=128)
    metric_type: Optional[int] = Field(None, description="指标类型 1-common 2-android 3-ios", ge=1, le=3)
    metric_category: Optional[int] = Field(None, description="指标分类 1-基础性能采集指标 2-Trace采集指标 3-ByteIO指标", ge=1, le=3)
    metric_unit: Optional[str] = Field(None, description="指标单位", max_length=32)
    metric_desc: Optional[str] = Field(None, description="指标描述", max_length=255)
    query_conditions: Optional[Dict] = Field(None, description="查询条件配置")
    
    @field_validator('query_conditions')
    @classmethod
    def validate_query_conditions(cls, v, info):
        if v is None:
            return v
            
        # 确保query_conditions只能是TraceQueryConditions、ByteIOQueryConditions或者为空
        metric_category = info.data.get('metric_category')
        
        # 如果请求中没有指定metric_category，则无法验证query_conditions的类型
        # 此时应该在handler层获取原有metric_category进行验证
        if metric_category is None:
            return v
            
        if metric_category == METRIC_CATEGORY_TRACE:  # Trace采集指标
            try:
                TraceQueryConditions.model_validate(v)
            except Exception as e:
                raise ValueError(f"Trace查询条件验证失败: {str(e)}")
        elif metric_category == METRIC_CATEGORY_BYTEIO:  # ByteIO指标
            try:
                ByteIOQueryConditions.model_validate(v)
            except Exception as e:
                raise ValueError(f"ByteIO查询条件验证失败: {str(e)}")
        else:
            # 对于其他类型的指标，query_conditions应该为空
            raise ValueError(f"指标类型{metric_category}不应该提供查询条件")
                
        return v

class MetricListRequest(PageSchema):
    """性能指标列表请求"""
    metric_type: Optional[int] = Field(None, description="指标类型 1-common 2-android 3-ios", ge=1, le=3)
    metric_category: Optional[int] = Field(None, description="指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标", ge=1, le=4)
    metric_key: Optional[str] = Field(None, description="指标key")
    metric_name: Optional[str] = Field(None, description="指标名称")
    creator: Optional[str] = Field(None, description="创建者")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
