'''
Author: heji<PERSON><PERSON>.oxep <EMAIL>
Date: 2025-01-16 19:31:54
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_stats_req.py
Description: 性能统计请求模型定义
'''
from typing import Optional
from datetime import date
from pydantic import BaseModel, Field


class BusinessCaseStatsRequest(BaseModel):
    """业务用例统计请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID，为空时获取所有业务线")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class TaskExecutionStatsRequest(BaseModel):
    """任务执行统计请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID，为空时获取所有业务线")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class CaseExecutionStatsRequest(BaseModel):
    """用例执行统计请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID，为空时获取所有业务线")
    case_id: Optional[int] = Field(None, description="用例ID，为空时获取所有用例")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class BusinessCaseTrendRequest(BaseModel):
    """业务用例趋势请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID，为空时获取所有业务线")
    days: int = Field(30, description="统计天数", ge=1, le=365)


class TaskExecutionTrendRequest(BaseModel):
    """任务执行趋势请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID，为空时获取所有业务线")
    days: int = Field(30, description="统计天数", ge=1, le=365)


class CaseExecutionTrendRequest(BaseModel):
    """用例执行趋势请求参数"""
    business_id: Optional[int] = Field(None, description="业务线ID，为空时获取所有业务线")
    case_id: Optional[int] = Field(None, description="用例ID，为空时获取所有用例")
    days: int = Field(30, description="统计天数", ge=1, le=365)
