'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-11 19:39:36
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_task_req.py
Description: 
'''
from typing import List, Union, Optional
from datetime import datetime
from schemas import BaseSchema


class PerfTaskIn(BaseSchema):
    pass

class PerfTaskListParams(PerfTaskIn):
    business_id: Union[int, List[int]] = None
    client_id: Union[int, List[int]] = None
    status: Union[int, List[int]] = None
    perf_tool_type: Union[int, List[int]] = None
    task_name: Union[str, List[str]] = None
    owner: Union[str, List[str]] = None


class CreatePerfSubTaskParams(PerfTaskIn):
    name: str
    platform: int
    case_id_list: List[int]
    perf_device_id: int
    app_group_id: int
    perf_account_id: Optional[int] = None
    video_url: str
    app_install_type: int
    finish_notice:  Union[list, None] = None
    perf_collect_type_list: List[int]
    perf_collect_duration: int
    perf_collect_interval: int
    perf_collect_mode: int
    perf_device_power_level: int
    case_run_count: int
    case_retry_count: int
    enabled: bool

class CreatePerfTaskParams(PerfTaskIn):
    name: str
    business_id: int
    client_id: int
    owner: str
    type: int
    perf_tool_type: Optional[int] = 1
    config_id: Optional[int] = None
    experiment_id: Optional[int] = None
    sub_tasks: List[CreatePerfSubTaskParams]

class UpdatePerfSubTaskParams(CreatePerfSubTaskParams):
    id: Union[int, None] = None
    task_id: Union[int, None] = None
    status: Union[int, None] = None
    result: Union[int, None] = None
    duration: Union[int, None] = None
    start_time: Union[datetime, None] = None
    end_time: Union[datetime, None] = None
    create_time: Union[datetime, None] = None
    update_time: Union[datetime, None] = None

class CancelPerfTaskParams(BaseSchema):
    task_id: int
    status: int
    client_id: int

class UpdatePerfTaskParams(PerfTaskIn):
    id: int
    name: str = None
    business_id: int = None
    client_id: int = None
    owner: str = None
    type: int = None
    perf_tool_type: Optional[int] = None
    config_id: Optional[int] = None
    experiment_id: Optional[int] = None
    status: int = None
    duration: int = None
    sub_tasks: List[UpdatePerfSubTaskParams] = None
    start_time: Union[datetime, None] = None
    end_time: Union[datetime, None] = None

class UploadPerfCaseRunDetailParams(BaseSchema):
    id: Optional[int] = None
    sub_task_id: Optional[int] = None
    device_id: Optional[int] = None
    app_id: Optional[int] = None
    case_id: Optional[int] = None
    case_log_tos_url: Optional[str] = None
    perf_data_tos_url: Optional[str] = None
    screenshot_tos_urls: Optional[List[str]] = None
    cpu_profile_tos_url: Optional[str] = None
    trace_data_tos_url: Optional[str] = None
    byteio_data_tos_url: Optional[str] = None
    status: Optional[int] = None
    version_type: Optional[int] = None  # 版本类型 1-实验组 2-对照组

# 添加子任务相关的请求模型
class GetSubTaskListParams(BaseSchema):
    id: Optional[int] = None
    status: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

class UpdateSubTaskStatusParams(PerfTaskIn):
    id: int
    status: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_code: Optional[int] = None
    error_title: Optional[str] = None 
    error_detail: Optional[str] = None

class UpdateTaskStatusParams(PerfTaskIn):
    id: int
    status: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None