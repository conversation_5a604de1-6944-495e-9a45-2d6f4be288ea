'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_client_req.py
Description: 
'''
from schemas import BaseSchema
from pydantic import BaseModel, Field
from typing import Optional


class PerfClientIn(BaseSchema):
    pass


class RegisterPerfClientParams(PerfClientIn):
    business_id: int = Field(default=0, description="业务线id")
    name: Optional[str] = Field(default='默认节点(首次注册请修改节点名称)', description="客户端名称")
    sys_type: Optional[int] = Field(default=None, description="系统类型")
    mac_address: str = Field(..., description="Mac地址")
    ipv4: Optional[str] = Field(default='', description="ipv4")
    ipv6: Optional[str] = Field(default='', description="ipv6")
    port: Optional[int] = Field(default=None, description="端口")
    state: int = Field(default=0, description="状态 0-离线 1-在线 ")
    owner: str = Field(default=None, description="负责人")


class UpdateClientDetailParams(BaseSchema):
    id: int = Field(..., description="客户端ID")
    business_id: Optional[int] = Field(None, description="业务线id")
    name: Optional[str] = Field(None, description="客户端名称")
    sys_type: Optional[int] = Field(None, description="系统类型")
    mac_address: Optional[str] = Field(None, description="Mac地址")
    ipv4: Optional[str] = Field(None, description="ipv4")
    ipv6: Optional[str] = Field(None, description="ipv6")
    port: Optional[int] = Field(None, description="端口")
    state: Optional[int] = Field(None, description="状态 0-离线 1-在线")
    owner: Optional[str] = Field(None, description="负责人")
