from typing import List, Union
from schemas import BaseSchema

class PerfAppIn(BaseSchema):
    pass

class GetAppListParams(PerfAppIn):
    id: Union[int, List[int]] = None
    business_id: int = None
    platform: int = None
    name: str = None
    version: str = None
    app_type: Union[int, List[int]] = None

class GetAppListRequestParams(PerfAppIn):
    params: GetAppListParams

class AddAppParams(PerfAppIn):
    business_id: int
    platform: int
    version: str
    app_type: int
    url: str
    jenkins_build_result_url: Union[str, None] = None
    repackage_cert: Union[str, None] = None
    creator: str

class UpdateAppParams(AddAppParams):
    id: int

class AddAppGroupParams(PerfAppIn):
    business_id: int
    name: str
    version: str
    android_perf_app_id_list: list
    android_assist_app_id_list: list
    ios_perf_app_id_list: list
    ios_assist_app_id_list: list
    creator: str

class UpdateAppGroupParams(AddAppGroupParams):
    id: int

class GetAppGroupListParams(PerfAppIn):
    business_id: Union[int, List[int]] = None
    version: Union[str, List[str]] = None
    app_group_name: Union[str, List[str]] = None

class DeleteAppGroupParams(PerfAppIn):
    id: int
