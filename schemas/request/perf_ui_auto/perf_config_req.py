'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/schemas/request/perf_ui_auto/perf_config_req.py
Description: 性能配置请求模型
'''
from pydantic import BaseModel, Field, model_validator
from typing import List, Optional
from enum import IntEnum
from datetime import datetime
from schemas.page import PageSchema

class ComparisonType(IntEnum):
    """比对标准类型"""
    INCREASE = 1  # 增加
    DECREASE = 2  # 减少

class ValueType(IntEnum):
    """值类型"""
    PERCENTAGE = 1  # 百分比
    ABSOLUTE = 2    # 绝对值

class PerformanceMetric(BaseModel):
    """性能指标配置"""
    metric_id: int = Field(..., description="指标ID", gt=0)
    is_important: bool = Field(..., description="是否重要指标")
    comparison_type: Optional[ComparisonType] = Field(None, description="比对标准类型")
    value_type: Optional[ValueType] = Field(None, description="值类型")
    threshold_value: Optional[float] = Field(None, description="阈值", ge=0)

    @model_validator(mode='after')
    def validate_important_metric(self):
        """验证重要指标的必填字段"""
        if self.is_important:
            if not self.comparison_type or not self.value_type or self.threshold_value is None:
                raise ValueError("重要指标必须设置比对标准、值类型和阈值")
        return self

class CreateConfigRequest(BaseModel):
    """创建配置请求"""
    business_id: int = Field(..., description="业务线ID", gt=0)
    config_name: str = Field(..., description="配置名称", min_length=1, max_length=128)
    metrics: List[PerformanceMetric] = Field(..., description="性能指标列表", min_items=1)
    creator: str = Field(..., description="创建者", min_length=1, max_length=64)

class UpdateConfigRequest(BaseModel):
    """更新配置请求"""
    id: int = Field(..., description="配置ID", gt=0)
    business_id: Optional[int] = Field(None, description="业务线ID", gt=0)
    config_name: Optional[str] = Field(None, description="配置名称", min_length=1, max_length=128)
    metrics: Optional[List[PerformanceMetric]] = Field(None, description="性能指标列表", min_items=1)

class ConfigListRequest(PageSchema):
    """配置列表请求"""
    business_id: Optional[int] = Field(None, description="业务线ID", gt=0)
    config_name: Optional[str] = Field(None, description="配置名称")
    creator: Optional[str] = Field(None, description="创建者")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
