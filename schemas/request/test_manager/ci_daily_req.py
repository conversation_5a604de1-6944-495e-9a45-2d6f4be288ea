from typing import List, Union

from schemas import BaseSchema


class CiDailyIn(BaseSchema):
    pass


class GetCaseListParams(CiDailyIn):
    time: List[str] = None
    branch: Union[str, List[str]] = None
    pipeline_type: Union[str, List[str]] = None
    platform: Union[str, List[str]] = None
    case_id: Union[str, List[str]] = None
    module: Union[int, List[int]] = None
    result_code: Union[int, List[int]] = None
    qa_owner: Union[str, List[str]] = None
    fail_reason_type: Union[int, List[int]] = None
    is_closed: Union[int, List[bool]] = None
    attribution_type: int = None


class UpdateCaseDetailParams(CiDailyIn):
    id: int
    fail_reason_type: int = None
    fail_reason_str: str = None
    improvement_measure_str: str = None
    is_closed: bool = None
    meego_bug_url: str = None
    attribution_type: int = None


class GetCiDailyDataParams(CiDailyIn):
    pipeline_type: List[int]
    get_data_days: int
