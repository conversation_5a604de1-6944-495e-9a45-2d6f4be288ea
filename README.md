# Global Business 测试平台后端

## 项目介绍
Global Business测试平台是一个企业级的自动化测试和性能测试平台，为全球业务提供完整的测试解决方案。该平台支持移动端性能测试、版本管理、实验评审等核心功能，帮助团队提升测试效率和产品质量。

## 核心功能模块

### 🚀 性能自动化测试模块 (perf_ui_auto)
**全面的移动端性能测试解决方案**

#### 任务管理系统
- **任务生命周期管理**：创建、编辑、复制、删除、启动、监控性能测试任务
- **多类型任务支持**：版本回归测试、Libra实验测试
- **性能采集工具集成**：支持DS和GamePerf两种性能采集工具
- **任务队列机制**：基于Redis的分布式任务队列，支持任务调度和状态管理

#### 设备管理系统
- **多平台设备支持**：Android和iOS设备的统一管理
- **设备状态监控**：实时监控设备在线状态、占用情况、电量等
- **连接方式管理**：支持USB和WiFi两种连接方式
- **设备资源调度**：智能分配设备资源，避免冲突

#### 测试用例管理
- **用例生命周期**：用例创建、编辑、执行、结果分析
- **多平台用例支持**：Android/iOS平台用例的统一管理
- **用例分类标签**：支持用例等级、标签分类管理
- **执行结果追踪**：详细的用例执行日志和截图记录

#### 性能指标体系
- **多维度指标**：CPU、内存、GPU、网络、电量等基础性能指标
- **高级指标支持**：Trace数据采集、ByteIO指标分析
- **自定义指标配置**：支持指标阈值设置、比对标准配置
- **数据存储链路**：完整的性能数据存储和TOS链接管理

#### APP管理系统
- **APP版本管理**：支持多版本APP的管理和部署
- **安装策略配置**：支持覆盖安装、卸载重装等多种安装方式
- **APP分组管理**：按业务线、版本等维度进行APP分组

#### 账号管理
- **测试账号池**：统一管理性能测试所需的账号资源
- **账号占用状态**：实时跟踪账号使用情况，避免冲突
- **业务线隔离**：按业务线进行账号资源隔离

#### 实验管理
- **Libra实验集成**：与Libra实验平台深度集成
- **实验组对照组**：支持A/B测试的实验设计
- **实验数据分析**：提供实验结果的统计分析功能

### 📋 版本管理模块 (version_manager)
**完整的版本发布流程管理**

- **版本生命周期跟踪**：从开发到发布的完整版本管理
- **阶段管理**：版本各个阶段的时间节点和负责人管理
- **检查清单(Checklist)**：版本发布前的质量检查清单
- **文档链接管理**：集成MeeGo、API变更文档、发布说明等
- **自动化提醒**：基于Lark的版本状态通知和提醒

### 🧪 测试管理模块 (test_manager)
**CI/CD集成的测试管理**

- **CI每日构建测试**：自动化的每日构建测试流程
- **测试结果统计**：详细的测试结果统计和趋势分析
- **测试报告生成**：自动生成测试报告和质量分析

### 🔧 工具管理模块 (tool_manager)
**测试工具集成平台**

- **FFmpeg流媒体处理**：视频流处理和分析工具
- **工具链集成**：集成各种测试工具和第三方服务

### 🔬 Libra实验评审模块 (libra_experiment)
**实验评审和管理系统**

- **实验数据收集**：自动收集Libra实验的相关数据
- **评审流程管理**：实验评审的完整流程管理
- **MeeGo集成**：与MeeGo测试报告系统集成
- **Lark通知**：基于Lark的实验状态通知

### 🏢 业务管理模块 (business)
**多业务线支持**

- **业务线隔离**：不同业务线的资源和数据隔离
- **权限管理**：基于业务线的权限控制
- **资源分配**：按业务线进行资源分配和管理

## 技术架构

### 核心技术栈
- **Web框架**：FastAPI 0.111.0 - 高性能异步Web框架
- **数据库**：MySQL - 通过ByteDance内部PSM服务访问
- **缓存系统**：Redis - 分布式缓存和任务队列
- **消息队列**：RocketMQ - 异步消息处理
- **对象存储**：TOS - 测试数据和日志文件存储
- **任务调度**：APScheduler - 定时任务和后台任务
- **日志系统**：bytedlogger + loguru - 结构化日志记录

### 数据库设计
- **ORM框架**：SQLAlchemy 2.0.32 - 现代化的Python ORM
- **数据模型**：完整的性能测试、版本管理、实验管理数据模型
- **迁移管理**：Alembic - 数据库版本控制和迁移

### API设计
- **RESTful API**：基于FastAPI的现代化API设计
- **自动文档**：Swagger UI和ReDoc自动生成API文档
- **数据验证**：Pydantic 2.8.2 - 强类型数据验证
- **Thrift接口**：跨语言服务调用支持

## 项目架构

### 分层架构设计
```
global_rtc_test_platform/
├── apis/                    # API路由层 - RESTful接口定义
│   ├── perf_ui_auto/       # 性能测试相关API
│   ├── version_manager/    # 版本管理API
│   ├── test_manager/       # 测试管理API
│   ├── tool_manager/       # 工具管理API
│   ├── libra_experiment/   # Libra实验API
│   └── business/           # 业务管理API
├── models/                  # 数据库模型层 - SQLAlchemy ORM模型
│   ├── perf_ui_auto/       # 性能测试数据模型
│   ├── business/           # 业务数据模型
│   ├── libra_experiment/   # 实验数据模型
│   └── version_manager/    # 版本管理数据模型
├── schemas/                 # 数据传输对象层 - Pydantic模型
│   ├── request/            # 请求数据模型
│   ├── response/           # 响应数据模型
│   └── inner_model/        # 内部数据模型
├── dals/                    # 数据访问层 - 数据库、缓存、消息队列操作
│   ├── db/                 # 数据库访问层
│   ├── redis/              # Redis缓存操作
│   ├── mq/                 # 消息队列操作
│   └── tos/                # 对象存储操作
├── handlers/                # 业务处理层 - 业务逻辑处理
│   ├── perf_ui_auto/       # 性能测试业务处理
│   ├── version_manager/    # 版本管理业务处理
│   ├── test_manager/       # 测试管理业务处理
│   ├── tool_manager/       # 工具管理业务处理
│   └── libra_experiment/   # 实验管理业务处理
├── service/                 # 服务层 - 复杂业务逻辑和外部服务集成
│   ├── perf_ui_auto/       # 性能测试服务
│   └── libra_experiment/   # 实验管理服务
├── utils/                   # 工具类库
│   ├── common/             # 通用工具
│   ├── openapi/            # 外部API集成
│   └── perf_ui_auto/       # 性能测试专用工具
├── defines/                 # 常量定义
├── configs/                 # 配置文件
├── thrifts/                 # Thrift接口定义
├── scripts/                 # 脚本文件
├── static/                  # 静态文件
├── temp/                    # 临时文件目录
├── app.py                   # 应用入口文件
├── __init__.py              # 应用初始化
├── proj_settings.py         # 项目配置管理
├── run.sh                   # 启动脚本
└── requirements.txt         # 依赖包列表
```

### 核心组件说明

#### API层 (apis/)
- **路由管理**：基于FastAPI的模块化路由设计
- **参数验证**：自动的请求参数验证和类型转换
- **响应格式**：统一的API响应格式和错误处理
- **文档生成**：自动生成Swagger和ReDoc文档

#### 数据模型层 (models/)
- **ORM映射**：SQLAlchemy模型定义，映射数据库表结构
- **关系定义**：表间关系和外键约束定义
- **数据验证**：数据库层面的数据完整性验证

#### 数据传输层 (schemas/)
- **请求模型**：API请求参数的结构化定义
- **响应模型**：API响应数据的结构化定义
- **数据验证**：Pydantic模型的强类型验证

#### 数据访问层 (dals/)
- **数据库操作**：封装的数据库CRUD操作
- **缓存管理**：Redis缓存的读写操作
- **队列管理**：消息队列的生产和消费
- **文件存储**：TOS对象存储的文件操作

## 部署环境

### 支持的部署环境
- **CN_BOE**: 中国测试环境 (端口: 8800)
- **CN_ONLINE**: 中国生产环境 (端口: 9800)
- **VA_ONLINE**: 海外环境 (端口: 10800, 部分功能支持)

### 环境配置
每个环境都有独立的配置类，支持：
- 数据库连接配置
- Redis缓存配置
- TOS存储配置
- 消息队列配置
- 第三方服务集成配置

## 快速开始

### 环境要求
- **Python**: 3.9+ (推荐)
- **数据库**: MySQL 5.7+
- **缓存**: Redis 6.0+
- **操作系统**: Linux/macOS

### 安装依赖
```bash
# 使用内部PyPI源安装依赖
pip install -r requirements.txt -i https://bytedpypi.byted.org/simple/ -i https://shoots-pypi.bytedance.net/simple/
```

### 启动服务

#### 生产环境启动
```bash
# 使用启动脚本（推荐）
bash run.sh
```

#### 开发环境启动
```bash
# 直接启动
python app.py

# 或使用uvicorn（支持热重载）
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 访问服务

#### API文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

#### 健康检查
- **服务状态**: http://localhost:8000/api/health

## 核心功能使用指南

### 性能测试流程
1. **创建性能配置** - 定义性能指标和阈值
2. **管理测试设备** - 添加和配置测试设备
3. **创建测试任务** - 配置测试参数和用例
4. **启动任务执行** - 将任务推送到执行队列
5. **监控执行状态** - 实时查看任务执行进度
6. **分析测试结果** - 查看性能数据和分析报告

### 版本管理流程
1. **创建版本记录** - 添加新版本信息
2. **配置版本阶段** - 设置版本各个阶段
3. **管理检查清单** - 配置版本发布检查项
4. **跟踪版本进度** - 监控版本发布状态
5. **自动化通知** - 版本状态变更通知

### 实验评审流程
1. **接收实验数据** - 自动收集Libra实验信息
2. **数据验证检查** - 验证实验数据完整性
3. **生成评审报告** - 自动生成实验评审材料
4. **通知相关人员** - 发送评审通知和结果

## 开发指南

### 代码规范
- **Python代码风格**: 遵循PEP 8规范
- **API设计**: RESTful API设计原则
- **数据库设计**: 规范化的数据库表设计
- **错误处理**: 统一的错误处理和日志记录

### 新功能开发
1. **数据模型设计** - 在models/目录下定义数据模型
2. **API接口设计** - 在apis/目录下定义路由和接口
3. **业务逻辑实现** - 在handlers/目录下实现业务逻辑
4. **数据访问实现** - 在dals/目录下实现数据访问
5. **测试用例编写** - 编写单元测试和集成测试

### 数据库迁移
```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述信息"

# 执行迁移
alembic upgrade head
```

## 外部服务集成

### 第三方平台集成
- **MeeGo**: 测试管理平台集成，获取测试报告和项目信息
- **Libra**: 实验平台集成，获取实验数据和配置
- **Lark**: 企业通讯平台，发送通知和卡片消息
- **BITS**: 内部工具平台集成
- **VPass**: 用例管理平台集成

### 性能采集工具
- **DS性能采集工具**: 传统的性能数据采集工具
- **GamePerf**: 游戏性能专用采集工具
- **Trace数据采集**: 系统级性能追踪
- **ByteIO指标**: 字节内部IO性能指标

## 监控和日志

### 日志系统
- **结构化日志**: 使用bytedlogger进行结构化日志记录
- **日志分级**: DEBUG、INFO、WARNING、ERROR等级别
- **日志轮转**: 按时间和大小进行日志文件轮转
- **日志保留**: 配置日志保留策略，自动清理过期日志

### 监控指标
- **API性能监控**: 接口响应时间和成功率
- **数据库性能**: 查询性能和连接池状态
- **缓存性能**: Redis命中率和响应时间
- **任务队列状态**: 队列长度和处理速度

## 安全和权限

### 数据安全
- **业务线隔离**: 不同业务线的数据完全隔离
- **敏感数据保护**: 账号密码等敏感信息加密存储
- **访问日志**: 完整的API访问日志记录

### 权限控制
- **基于业务线的权限**: 用户只能访问所属业务线的资源
- **操作权限控制**: 不同角色的操作权限限制
- **API访问控制**: 基于token的API访问控制

## 故障排查

### 常见问题
1. **数据库连接失败**: 检查PSM配置和网络连接
2. **Redis连接超时**: 检查Redis服务状态和配置
3. **TOS上传失败**: 检查TOS配置和网络连接
4. **任务执行失败**: 检查设备状态和任务配置

### 日志查看
```bash
# 查看应用日志
tail -f logs/$(date +%Y-%m-%d).log

# 查看错误日志
grep "ERROR" logs/$(date +%Y-%m-%d).log

# 查看特定模块日志
grep "perf_ui_auto" logs/$(date +%Y-%m-%d).log
```

## 贡献指南

### 提交规范
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 分支管理
- **master**: 主分支，稳定版本
- **boe**: 测试环境分支
- **feature/***: 功能开发分支
- **bugfix/***: 问题修复分支

## 版本历史

### 最新更新 (v1.0)
- ✅ 新增性能采集工具类型支持 (DS/GamePerf)
- ✅ 扩展数据存储字段 (CPU Profile、Trace、ByteIO)
- ✅ 优化分页查询性能 (限制提升至500)
- ✅ 增强任务队列机制 (重复检查、错误处理)
- ✅ 完善实验管理功能
- ✅ 优化版本管理流程

## 联系方式

### 开发团队
- **项目负责人**: <EMAIL>
- **技术支持**: 通过Lark群组或邮件联系

### 相关链接
- **前端项目**: https://gstest.sre.bytedance.net (生产环境)
- **前端项目**: https://gstest.sre-boe.bytedance.net (测试环境)
- **API文档**: /docs (Swagger UI)
- **技术文档**: 内部Wiki

---

**注意**: 本项目为ByteDance内部项目，包含内部服务和配置信息，请勿外传。