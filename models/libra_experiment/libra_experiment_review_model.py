from sqlalchemy import Text, Integer, BigInteger, Column, String, DateTime, Boolean, func, ForeignKey, Numeric
from sqlalchemy.orm import relationship, Mapped

from models import Base

class LibraExperimentFieldMeta(Base):
    __tablename__ = "libra_experiment_field_meta"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    field_name = Column(String(255), nullable=False, comment="字段名称")
    field_desc = Column(String(255), comment="字段描述")
    field_key = Column(String(255), nullable=False, comment="字段在实验表/实验测试报告表的字段名")
    field_type = Column(Integer, nullable=False, comment="字段数据类型 1：int 2：string 3：float 4：list 5：map")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

class LibraExperimentFieldReviewRule(Base):
    __tablename__ = "libra_experiment_field_review_rule"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    business_id = Column(BigInteger, nullable=False, comment="业务表id")
    libra_experiment_field_meta_id = Column(BigInteger, nullable=False, comment="实验字段元数据表id")
    operator_type = Column(Integer, nullable=False, comment="检查规则类型 1：等于 2：大于 3：大于等于 4：小于 5：小于等于 6：正则 7：是某个列表的子集 8：与某个列表完全匹配 9：是某个列表的父集 10：是否为空 11：忽略大小写 12：评审人里至少有n个是RD")
    is_check_value = Column(Integer, nullable=False, default=0, comment="检查字段值本身还是检查个数还是是否有某个规则 0：值本身 1：个数 2：规则")
    expect_value = Column(String(255), nullable=False, comment="预期值，当operator_type、8、9时，预期值应该是一个list，如果不是也要转成list来检查；都是用string来存储的，但是字段类型本身会有int、string、float、list等多种，所以检查前会根据字段类型进行转换 当operator_type为10时，通过0、1来定义是否为空，0是为空，1是不为空")
    is_must = Column(Boolean, nullable=False, default=True, comment="是否必须通过（建议类or必须类）0：否，建议类 1：是，必须类")
    version_code = Column(Integer, nullable=False, default=1, comment="规则的版本号，当规则发生更新时，不直接修改原规则的值，而是新建一条数据，并让版本号+1，历史版本的数据用于关联历史检查记录")
    is_using = Column(Boolean, nullable=False, default=True, comment="是否启用，用于标识这条规则现在还是否在使用。如果规则更新了就要把就规则置为不启用")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

class LibraExperiment(Base):
    __tablename__ = "libra_experiment"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    business_id = Column(BigInteger, nullable=False, comment="业务表id")
    experiment_name = Column(String(255), nullable=False, comment="实验名称，对应原始事件的flight_info.flight_display_name")
    experiment_creator = Column(String(255), nullable=False, comment="实验创建者，对应原始事件的event.event_operator")
    experiment_reviewer = Column(String(1000), comment="实验reveiwer，list转string，格式[\"aaa\",\"bbb\"]，从experiment_reviewer_result里提取的reviewer列表")
    experiment_reviewer_result = Column(String(1000), comment="实验reveiwer的审批情况，list转string，格式：[{\"zhangsan\": \"同意\"}, {\"lisi\": \"不同意\"}]，对应原始事件的review_info.reviewer")
    experiment_app_ids = Column(String(1000), comment="产品注册appid，若有多个以逗号分隔，格式\"1371\"，对应原始事件的app_info.app_ids")
    layer_display_name = Column(String(1000), comment="实验层名称，对应原始事件的layer_info.layer_display_name")
    experiment_description = Column(String(2000), comment="实验描述，对应原始事件的flight_info.description")
    experiment_libra_id = Column(Integer, nullable=False, comment="实验id，对应原始事件的flight_info.flight_id")
    experiment_type = Column(String(255), nullable=False, comment="实验类型，对应原始事件的flight_info.flight_type")
    experiment_status = Column(String(255), nullable=False, comment="实验状态，目前有running、stopped、testing、suspended四种")
    experiment_numerical_traffic = Column(Numeric(10, 2), comment="实验流量，与traffic对应，取值范围[0,1]，对应[0%,100%]，对应原始事件的flight_info.numerical_traffic")
    experiment_tags = Column(String(1000), comment="实验标签，list，格式：[\"tag1\",\"tag2\"]，对应原始事件的flight_info.tags")
    experiment_start_time = Column(String(255), comment="实验开启时间，对应原始事件的flight_info.start_time")
    experiment_start_ts = Column(DateTime, comment="实验开启时间unix timestamp，对应原始事件的flight_info.start_ts")
    experiment_end_time = Column(String(255), comment="实验过期时间，对应原始事件的flight_info.end_time")
    experiment_end_ts = Column(DateTime, comment="实验结束时间unix timestamp，对应原始事件的flight_info.end_ts")
    experiment_libra_url = Column(String(500), comment="实验链接，对应原始事件的flight_info.flight_url")
    experiment_create_time = Column(String(255), comment="实验创建时间，对应原始事件的flight_info.create_time")
    experiment_create_ts = Column(DateTime, comment="实验创建时间unix timestamp，对应原始事件的flight_info.create_ts")
    experiment_filter_rule = Column(Text, comment="实验流量过滤条件，list转字符串，格式\"[]\"，对应原始事件的flight_info.filter_rule")
    experiment_filter_rule_device_platform = Column(String(1000), comment="实验流量过滤条件里的device_platform，map转字符串，对应原始事件的flight_info.filter_rule的key=device_platform")
    experiment_filter_rule_version_code = Column(String(1000), comment="实验流量过滤条件里的_version_code，map转字符串，对应原始事件的flight_info.filter_rule的key=_version_code")
    experiment_filter_rule_priority_region = Column(Text, comment="实验流量过滤条件里的priority_region，用户所在地区，map转字符串，对应原始事件的flight_info.filter_rule的key=priority_region")
    experiment_enable_gradual = Column(String(255), comment="实验流量生效方式 平滑生效/立刻生效，对应原始事件的flight_info.enable_gradual")
    experiment_meego_info = Column(String(1000), comment="关联Meego需求，字典转字符串，格式{\"meego_array\": []} ，对应原始事件的flight_info.meego_info")
    experiment_meego_url = Column(String(1000), comment="关联Meego需求链接，从experiment_meego_info里拆出来，便于处理")
    experiment_version_info = Column(Text, comment="实验组详情，list转字符串，格式：[{\"id\": 412342, // 实验组id \"config\": {}, // 实验组配置 \"type\": 0 // 实验组类型， 0：对照组 1：实验组 }]，对应原始事件的version_info")
    experiment_metrics_info = Column(Text, comment="实验已选指标组详情，list转字符串，根据实验id从libra openapi接口获取的结果")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")
    experiment_qa_reviewer = Column(String(1000), comment="实验QA reveiwer，list转string，格式[\"aaa\",\"bbb\"]，从experiment_reviewer_result里提取的reviewer列表，仅包含QA")
    experiment_product_info = Column(String(255), comment="实验功能模块信息，map转字符串，对应原始事件的product_info，格式{\"product_id\":\"69\",\"token\":\"ad_lianmeng\"}")

    def to_json(self):
        return {
            "id": self.id,
            "business_id": self.business_id,
            "experiment_name": self.experiment_name,
            "experiment_creator": self.experiment_creator,
            "experiment_reviewer": self.experiment_reviewer,
            "experiment_reviewer_result": self.experiment_reviewer_result,
            "experiment_app_ids": self.experiment_app_ids,
            "layer_display_name": self.layer_display_name,
            "experiment_description": self.experiment_description,
            "experiment_libra_id": self.experiment_libra_id,
            "experiment_type": self.experiment_type,
            "experiment_status": self.experiment_status,
            "experiment_numerical_traffic": self.experiment_numerical_traffic,
            "experiment_tags": self.experiment_tags,
            "experiment_start_time": self.experiment_start_time,
            "experiment_start_ts": self.experiment_start_ts,
            "experiment_end_time": self.experiment_end_time,
            "experiment_end_ts": self.experiment_end_ts,
            "experiment_libra_url": self.experiment_libra_url,
            "experiment_create_time": self.experiment_create_time,
            "experiment_create_ts": self.experiment_create_ts,
            "experiment_filter_rule": self.experiment_filter_rule,
            "experiment_filter_rule_device_platform": self.experiment_filter_rule_device_platform,
            "experiment_filter_rule_version_code": self.experiment_filter_rule_version_code,
            "experiment_filter_rule_priority_region": self.experiment_filter_rule_priority_region,
            "experiment_enable_gradual": self.experiment_enable_gradual,
            "experiment_meego_info": self.experiment_meego_info,
            "experiment_meego_url": self.experiment_meego_url,
            "experiment_version_info": self.experiment_version_info,
            "experiment_metrics_info": self.experiment_metrics_info,
            "create_time": self.create_time,
            "update_time": self.update_time,
            "experiment_qa_reviewer": self.experiment_qa_reviewer,
            "experiment_product_info": self.experiment_product_info
        }


class LibraExperimentTestReport(Base):
    __tablename__ = "libra_experiment_test_report"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    uniq_libra_experiment_id = Column(BigInteger, nullable=False, comment="实验表id")
    test_report_url = Column(String(1000), comment="测试报告链接")
    test_report_is_pass = Column(Boolean, nullable=False, default=False, comment="是否测试通过 0：fail 1：pass")
    test_report_tester = Column(String(255), comment="测试人员list，list转json")
    test_node_status_is_finish = Column(Boolean, nullable=False, default=False, comment="测试节点是否完成 0：未完成 1：已完成")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

class LibraExperimentReviewResult(Base):
    __tablename__ = "libra_experiment_review_result"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    libra_experiment_id = Column(BigInteger, nullable=False, comment="实验表id")
    is_pass = Column(Boolean, nullable=False, default=False, comment="是否通过 0：fail 1：pass")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

class LibraExperimentReviewResultRuleDetail(Base):
    __tablename__ = "libra_experiment_review_result_rule_detail"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    libra_experiment_review_result_id = Column(BigInteger, nullable=False, comment="实验检查结果表id")
    libra_experiment_field_review_rule_id = Column(BigInteger, nullable=False, comment="实验检查字段规则表id")
    is_pass = Column(Boolean, nullable=False, default=False, comment="是否通过 0：fail 1：pass")
    message = Column(String(255), nullable=False, default=False, comment="备注信息：建议类规则且检查通过：“通过”；建议类规则且检查不通过：具体原因；必须类规则且检查通过：“通过”；必须类规则且检查不通过：具体原因")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")