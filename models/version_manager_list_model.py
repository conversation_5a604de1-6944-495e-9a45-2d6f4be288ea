from sqlalchemy import Text, Integer, BigInteger, Column, String, DateTime, Time, Boolean, func, ForeignKey
from sqlalchemy.orm import relationship

from . import Base

class Version(Base):
    __tablename__ = 'version_manager_version'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    version_name = Column(String(255), nullable=False,comment='版本名')
    version_qa_owner = Column(String(255), comment='版本QA负责人，邮箱前缀')
    version_rd_owner = Column(String(255), comment='版本RD负责人，邮箱前缀')
    platforms = Column(String(255), comment='端类型，["android","ios","linux"]')
    start_time = Column(DateTime, nullable=False, comment='版本开始日期')
    end_time = Column(DateTime, nullable=False, comment='版本结束日期')
    meego_epic_url = Column(String(255), comment='Meego Epic链接')
    meego_version_url = Column(String(255), comment='Meego 版本链接')
    submit_to_test_doc_url = Column(String(255), comment='提测文档链接')
    api_change_doc_url = Column(String(255), comment='接口变更文档链接')
    release_note_doc_url = Column(String(255), comment='Release note文档链接')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), comment='更新时间')
    def init(self,id,version_name,version_qa_owner,version_rd_owner,platforms,start_time,end_time,
             meego_epic_url,meego_version_url,submit_to_test_doc_url,api_change_doc_url,release_note_doc_url,
             create_time,update_time):
        self.id = id
        self.version_name = version_name
        self.version_qa_owner = version_qa_owner
        self.version_rd_owner = version_rd_owner
        self.platforms = platforms
        self.start_time = start_time
        self.end_time = end_time
        self.meego_epic_url = meego_epic_url
        self.meego_version_url = meego_version_url
        self.submit_to_test_doc_url = submit_to_test_doc_url
        self.api_change_doc_url = api_change_doc_url
        self.release_note_doc_url = release_note_doc_url
        self.create_time = create_time
        self.update_time = update_time
        return self

