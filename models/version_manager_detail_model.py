from sqlalchemy import Text, Integer, BigInteger, Column, String, DateTime, Time, Boolean, func, ForeignKey
from sqlalchemy.orm import relationship
from models.version_manager_list_model import Version

from . import Base

class VersionDetail(Version):
    pass
    # test_task_platform = relationship("version_stages", back_populates="version_manager_version_stage")


class Checklist(Base):
    __tablename__ = 'version_manager_checklist'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    name = Column(String(255), nullable=False, comment='checklist名称')
    checklist_desc = Column(String(255), nullable=False, comment='描述')
    stage = Column(Integer, comment='版本阶段 1-需求封版 2-版本提测（冒烟）3-第一轮回归测试 4-RD解bug-1 5-第二轮回归测试 6-RD解bug-2 7-发版')
    remind_type = Column(Integer, comment='提醒类型 1-阶段开始时提醒 2-每日提醒 3-阶段结束时提醒')
    remind_days = Column(Integer, comment='连续提醒天数，提醒类型为1、3时有效；当提醒类型为 1-阶段开始时提醒 时，为正向天数，当提醒类型为 3-阶段结束时提醒 时，为倒数天数')
    reminder_type = Column(Integer, default=1, comment='提醒人类型 1-版本负责人 2-自定义')
    reminder = Column(Text, default='',nullable=False,comment='提醒人邮箱前缀，reminder_type时有效，json list格式，如["maweijia.1211"]')
    remind_time = Column(Time, comment='提醒时间')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), comment='更新时间')

class VersionStage(Base):
    __tablename__ = 'version_manager_version_stage'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    version_id = Column(BigInteger, nullable=False, comment='version_manager_version表id')
    stage = Column(Integer, comment='版本阶段 1-需求封版 2-版本提测（冒烟）3-第一轮回归测试 4-RD解bug-1 5-第二轮回归测试 6-RD解bug-2 7-发版')
    name = Column(String(255), comment='阶段名称')
    stage_order = Column(Integer, comment='阶段顺序，数字升序对应时间从前往后')
    start_time = Column(DateTime, nullable=False, comment='阶段开始日期')
    end_time = Column(DateTime, nullable=False, comment='阶段结束日期')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), comment='更新时间')

    def init(self,id,version_id,stage,name,stage_order,start_time,end_time,create_time,update_time):
        self.id = id
        self.version_id = version_id
        self.stage = stage
        self.name = name
        self.stage_order = stage_order
        self.start_time = start_time
        self.end_time = end_time
        self.create_time = create_time
        self.update_time = update_time
        return self


class VersionChecklist(Base):
    __tablename__ = 'version_manager_version_checklist'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    version_id = Column(BigInteger, nullable=False, comment='version_manager_version表id')
    checklist_id = Column(BigInteger, nullable=False, comment='version_manager_checklist表id')
    start_time = Column(DateTime, default=func.now(), comment='开始提醒时间')
    is_finished = Column(Boolean, comment='是否完成')
    comment = Column(String(255), comment='备注')
    finish_time = Column(DateTime, comment='完成时间')
    operator = Column(String(255), comment='操作人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), comment='更新时间')