from sqlalchemy import Text, Integer, BigInteger, Column, String, DateTime, Boolean, func, ForeignKey
from sqlalchemy.orm import relationship

from models import Base

class Business(Base):
    __tablename__ = "business"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    business_name = Column(String(255), comment="业务名称")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")
    business_dir = Column(String(255),nullable=True, comment="用例目录")

class Application(Base):
    __tablename__ = "application"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    business_id = Column(BigInteger, nullable=False, comment="business表id")
    app_name = Column(String(255), comment="应用名称")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

