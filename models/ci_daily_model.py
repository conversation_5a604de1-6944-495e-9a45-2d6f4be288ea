from sqlalchemy import Text, Integer, BigInteger, Column, String, DateTime, Boolean, func, ForeignKey
from sqlalchemy.orm import relationship

from . import Base


class MrTask(Base):
    __tablename__ = "mr_task"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    uniq_mr_id = Column(Integer, nullable=False, unique=True, comment="mr id")
    mr_state = Column(String(255), comment="mr状态")
    mr_create_time = Column(DateTime, comment="mr的创建时间")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

    test_tasks = relationship("TestTask", back_populates="mr_task")


class TestTask(Base):
    __tablename__ = "test_task"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    idx_mr_task_id = Column(Integer, ForeignKey("mr_task.id"), comment="mr任务表主键")
    pipeline_id = Column(Integer, nullable=False, comment="流水线id")
    branch = Column(String(255), comment="被测分支")
    pipeline_type = Column(String(64), nullable=False, comment="ci/daily")
    pipeline_user = Column(String(64), comment="触发人")
    pipeline_create_time = Column(DateTime, nullable=False, comment="开始时间")
    pipeline_finish_time = Column(DateTime, nullable=False, comment="结束时间")
    task_url = Column(Text, comment="pipeline任务链接")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

    mr_task = relationship("MrTask", back_populates="test_tasks")
    test_platforms = relationship("TestPlatform", back_populates="test_task")


class TestPlatform(Base):
    __tablename__ = "test_platform"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    idx_test_task_id = Column(Integer, ForeignKey("test_task.id"), nullable=False, comment="test_task表的主键")
    job_id = Column(Integer, comment="任务id")
    platform = Column(String(64), nullable=False, comment="端类型")
    watchcat_demo_url = Column(String(255), comment="待测包链接")
    case_log_url = Column(Text, comment="控制台日志zip的url")
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, nullable=False, comment="结束时间")
    job_url = Column(Text, comment="job任务链接")
    total_count = Column(Integer, default=0, comment="用例总数")
    pass_count = Column(Integer, default=0, comment="成功用例数")
    fail_count = Column(Integer, default=0, comment="失败用例数")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

    test_task = relationship("TestTask", back_populates="test_platforms")
    test_case_details = relationship("TestCaseDetail", back_populates="test_task_platform")


class TestCaseDetail(Base):
    __tablename__ = "test_case_detail"
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="ID")
    idx_test_task_platform_id = Column(Integer, ForeignKey("test_platform.id"), nullable=False,
                                       comment="test_task_platform表的主键")
    case_id = Column(String(255), nullable=False, comment="用例名称")
    result_code = Column(Integer, nullable=False, comment="用例执行结果码")
    result_message = Column(Text, comment="用例执行结果文字描述")
    module = Column(Integer, nullable=False, comment="所属模块- 1:视频- 2:音频- 3:房间- 4:网络")
    qa_owner = Column(String(255), nullable=False, comment="用例QA负责人")
    case_log_url = Column(Text, comment="控制台日志url")
    watchcat_log_url = Column(Text, comment="watchcat本地日志url")
    start_time = Column(DateTime, nullable=False, comment="用例开始时间")
    end_time = Column(DateTime, nullable=False, comment="用例结束时间")
    cost_time = Column(Integer, nullable=False, comment="耗时（s）")
    expect_assert = Column(Text, comment="预期断言信息")
    actual_assert = Column(Text, comment="实际断言信息")
    device_info_list = Column(Text, comment="执行设备信息（可能有多个设备，包括每个设备的did、sid、roomid、userid、trace等）")
    fail_reason_str = Column(String(255), comment="失败原因分析")
    fail_reason_type = Column(Integer, nullable=False, default=0,
                              comment="失败原因分类 0:未分类 1：bug 2：用例问题 3：框架问题 4：环境问题 5：机器问题 6：网络问题")
    improvement_measure_str = Column(String(255), comment="改进措施分析")
    is_closed = Column(Boolean, nullable=False, default=0, comment="是否闭环 0:未闭环 1:已闭环")
    meego_bug_url = Column(Text, comment="Bug链接")
    attribution_type = Column(Integer, nullable=False, default=0,
                              comment="归因类型 0：手动归因 1：自动归因")
    create_time = Column(DateTime, default=func.now(), comment="创建时间")
    update_time = Column(DateTime, default=func.now(), comment="更新时间")

    test_task_platform = relationship("TestPlatform", back_populates="test_case_details")
