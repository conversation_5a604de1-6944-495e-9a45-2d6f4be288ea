'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_account_model.py
Description: 
'''
from sqlalchemy import Integer, BigInteger, Column, String, DateTime, func
from sqlalchemy.orm import relationship
from ..__init__ import Base

class PerfUiAutoAccount(Base):
    """性能测试账号表"""
    __tablename__ = 'perf_ui_auto_account'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    business_id = Column(BigInteger, nullable=False, default=0, comment='业务线id')
    uid = Column(String(255), nullable=False, comment='user id')
    iphone = Column(String(255), nullable=False, comment='虚拟手机号')
    username = Column(String(255), nullable=False, comment='用户名')
    captcha = Column(String(255), nullable=True, comment='验证码')
    email = Column(String(255), nullable=True, comment='邮箱')
    pwd = Column(String(255), nullable=True, comment='密码')
    app = Column(Integer, nullable=False, default=0, comment='APP')
    owner = Column(String(255), nullable=False, comment='负责人')
    is_occupied = Column(Integer, nullable=False, default=0, comment='是否被占用 0-未占用 1-已占用')
    account_type = Column(Integer, nullable=False, default=2, comment='账号类型 1-性能测试账号 2-普通账号')
    country = Column(String(50), nullable=True, comment='国家名称')
    country_code_alpha2 = Column(String(2), nullable=True, comment='ISO 3166-1 国家二字码')
    phone_area_code = Column(String(10), nullable=True, comment='电话区号')
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='更新时间')
