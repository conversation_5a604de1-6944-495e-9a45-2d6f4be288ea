'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_experiment_model.py
Description: 实验相关数据库模型
'''
from typing import List
from sqlalchemy import Column, Integer, String, JSON, BigInteger
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class ExperimentModel(Base):
    """实验表"""
    __tablename__ = 'perf_experiment'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='实验ID')
    name = Column(String(128), nullable=False, comment='实验名称')
    hit_type = Column(Integer, nullable=False, comment='命中类型：1-DID命中，2-UID命中')
    experiment_group_version_ids = Column(JSON, nullable=False, comment='实验组版本IDs，List[int]类型')
    control_group_version_ids = Column(JSON, nullable=False, comment='对照组版本IDs，List[int]类型')
    description = Column(String(512), nullable=True, comment='实验描述')
    creator = Column(String(128), nullable=True, comment='创建者')
    create_time = Column(BigInteger, nullable=False, comment='创建时间')
    update_time = Column(BigInteger, nullable=False, comment='更新时间')
