'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_device_model.py
Description: 
'''
from sqlalchemy import Integer, BigInteger, Column, String, DateTime, func, ForeignKey, JSON
from sqlalchemy.orm import relationship
from ..__init__ import Base

class PerfUiAutoDevice(Base):
    """性能测试设备表"""
    __tablename__ = 'perf_ui_auto_device'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    client_id = Column(Integer, ForeignKey('perf_ui_auto_client.id'), nullable=False,
                               comment='性能客户端id')
    name = Column(String(255), nullable=False, comment='设备名称')
    udid = Column(String(255), nullable=False, comment='设备唯一标识符')
    model = Column(String(255), nullable=False, comment='型号')
    sys_type = Column(Integer, nullable=False, comment='系统类型 1-Android 2-iOS')
    sys_version = Column(String(255), nullable=False, comment='系统版本')
    brand = Column(String(255), nullable=False, comment='品牌')
    resolution = Column(String(255), nullable=False, comment='分辨率')
    connect_type = Column(JSON, nullable=False, comment='连接类型列表 [1-USB, 2-WiFi]')
    state = Column(Integer, nullable=False, comment='设备状态 0-离线 1-在线')
    ip = Column(String(255), nullable=True, comment='IP地址')
    serial_port = Column(String(255), nullable=True, comment='串口号')
    is_occupied = Column(Integer, nullable=False, comment='是否被占用 0-未占用 1-已占用')
    user = Column(String(255), nullable=True, comment='使用者')
    owner = Column(String(255), nullable=True, comment='所有者')
    create_time = Column(DateTime, nullable=False, comment='创建时间')
    update_time = Column(DateTime, nullable=False, comment='更新时间')

    # 关联关系
    perf_ui_auto_client = relationship("PerfUiAutoClient", back_populates="perf_ui_auto_devices")