'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_config_model.py
Description: 性能配置模型
'''
from sqlalchemy import Column, String, DateTime, JSON, ForeignKey, BigInteger
from sqlalchemy.sql import func
from sqlalchemy.dialects.mysql import LONGTEXT, JSON as MYSQL_JSON
from ..__init__ import Base

class PerformanceConfig(Base):
    """
    性能配置表
    存储性能测试的配置信息，包括配置名称、指标列表等
    """
    __tablename__ = "perf_ui_auto_config"
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="配置ID")
    
    # 业务信息
    business_id = Column(BigInteger, nullable=False, default=0, comment="业务线ID")
    
    # 配置信息
    config_name = Column(String(128), nullable=False, comment="配置名称")
    metrics = Column(MYSQL_JSON, nullable=False, comment="""性能指标配置列表，JSON格式：
    [{
        "metric_id": int,          # 指标ID
        "is_important": bool,      # 是否重要指标
        "comparison_type": int,    # 比对标准类型(1:增加,2:减少)
        "value_type": int,        # 值类型(1:百分比,2:绝对值)
        "threshold_value": float  # 阈值
    }]
    """)
    
    # 创建信息
    creator = Column(String(64), nullable=False, comment="创建者")
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment="创建时间")
    update_time = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="更新时间")
