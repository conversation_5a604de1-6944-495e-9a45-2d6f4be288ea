'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:57
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_task_model.py
Description: 
'''
from sqlalchemy import Integer, BigInteger, Column, String, DateTime, func, ForeignKey, Boolean, JSON
from ..__init__ import Base

class PerfUiAutoTask(Base):
    __tablename__ = 'perf_ui_auto_task'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    name = Column(String(255), nullable=False, comment='任务名称')
    business_id = Column(BigInteger, nullable=False, comment='业务线id')
    client_id = Column(BigInteger, nullable=False, comment='客户端id')
    config_id = Column(BigInteger, nullable=True, comment='性能配置ID')
    experiment_id = Column(BigInteger, nullable=True, comment='实验ID')
    owner = Column(String(255), nullable=False, comment='任务负责人邮箱前缀')
    type = Column(Integer, default=1, nullable=False, comment='任务类型 1-版本回归 2-Libra实验')
    perf_tool_type = Column(Integer, default=1, nullable=False, comment='性能采集工具类型 1-DS 2-GamePerf')
    status = Column(Integer, default=0, nullable=False, comment='任务状态 0-待执行 1-执行中 2-执行完成 3-执行失败')
    result = Column(Integer, default=0, nullable=False, comment='任务结果 0-无结果 1-通过 2-不通过')
    duration = Column(Integer, default=0, nullable=False, comment='任务运行时长（单位秒s）')
    start_time = Column(DateTime, comment='任务开始时间')
    end_time = Column(DateTime, comment='任务结束时间')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

class PerfUiAutoSubTask(Base):
    __tablename__ = 'perf_ui_auto_sub_task'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    task_id = Column(BigInteger, ForeignKey('perf_ui_auto_task.id'), nullable=False, comment='任务id')
    name = Column(String(255), nullable=False, comment='子任务名称')
    platform = Column(Integer, nullable=False, comment='平台 1-Android 2-ios')
    case_id_list = Column(JSON, nullable=False, comment='用例id列表')
    perf_device_id = Column(BigInteger, nullable=False, comment='性能设备id')
    perf_account_id = Column(BigInteger, nullable=True, comment='性能账号id')
    app_group_id = Column(BigInteger, nullable=False, comment='APP组id')
    video_url = Column(String(1000), nullable=False, comment='视频地址')
    perf_collect_mode = Column(Integer, nullable=False, comment='性能采集方式 1-有线连接采集 2-无线连接采集')
    app_install_type = Column(Integer, default=0, nullable=False, comment='安装方式 0-不安装 1-卸载安装 2-覆盖安装')
    finish_notice = Column(String(1000), comment='子任务完成通知')
    perf_collect_type_list = Column(JSON, nullable=False, comment='性能采集类型列表')
    perf_collect_duration = Column(Integer, default=0, nullable=False, comment='性能采集等待时长（s）')
    perf_collect_interval = Column(Integer, default=1000, nullable=False, comment='性能采集间隔（ms）')
    perf_device_power_level = Column(Integer, default=0, nullable=False, comment='性能设备电量最低值百分比（%）')
    case_run_count = Column(Integer, default=0, nullable=False, comment='单条用例运行次数')
    case_retry_count = Column(Integer, default=0, nullable=False, comment='用例失败重试次数')
    enabled = Column(Boolean, default=True, nullable=False, comment='是否启用 0-否 1-是（默认启用）')
    status = Column(Integer, default=0, nullable=False, comment='子任务状态 0-待执行 1-执行中 2-执行完成 3-执行失败')
    result = Column(Integer, default=0, nullable=False, comment='子任务结果 0-无结果 1-通过 2-不通过')
    duration = Column(Integer, default=0, nullable=False, comment='子任务运行时长（单位秒s）')
    start_time = Column(DateTime, comment='子任务开始时间')
    end_time = Column(DateTime, comment='子任务结束时间')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    error_code = Column(Integer, nullable=True, comment='错误码')
    error_title = Column(String(255), nullable=True, comment='错误码标题')
    error_detail = Column(String(1000), nullable=True, comment='错误码详情')


class PerfCaseRunDetail(Base):
    __tablename__ = 'perf_ui_auto_case_run_detail'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    sub_task_id = Column(BigInteger, nullable=False, comment='子任务id')
    device_id = Column(BigInteger, nullable=False, comment='设备id')
    app_id = Column(BigInteger, nullable=False, comment='app id')
    case_id = Column(BigInteger, nullable=False, comment='用例id')
    case_log_tos_urls = Column(JSON, nullable=True, comment='用例日志文件tos地址JSON格式')
    perf_data_tos_urls = Column(JSON, nullable=True, comment='性能数据文件tos地址JSON格式')
    screenshot_tos_urls = Column(JSON, nullable=True, comment='用例执行截图tos地址列表')
    cpu_profile_tos_url = Column(String(255), nullable=True, comment='CPU性能分析数据TOS链接')
    status = Column(Integer, nullable=False, comment='运行状态')
    version_type = Column(Integer, nullable=True, comment='版本类型 1-实验组 2-对照组')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')