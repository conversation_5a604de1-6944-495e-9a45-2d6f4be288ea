'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_data_model.py
Description: 性能数据模型定义
'''
from sqlalchemy import Integer, Column, String, DateTime, func, Boolean, JSON
from sqlalchemy.orm import Mapped
from ..__init__ import Base

class PerfUIAutoData(Base):
    __tablename__ = 'perf_ui_auto_data'

    id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True, comment='主键id')
    case_run_detail_id: Mapped[int] = Column(Integer, default=0, nullable=False,
                                                   comment='perf_ui_auto_case_run_detail表的主键')
    is_avg: Mapped[bool] = Column(Boolean, default=False, nullable=False, comment='是用例每次的数据还是平均值的数据')
    platform: Mapped[int] = Column(Integer, nullable=False, comment='平台(1:android/2:ios)')
    metrics_data: Mapped[dict] = Column(JSON, nullable=False, comment='性能指标数据,JSON格式')
    create_time: Mapped[DateTime] = Column(DateTime, server_default=func.now(), comment='创建时间')
    update_time: Mapped[DateTime] = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment='更新时间')
