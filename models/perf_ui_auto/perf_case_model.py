from sqlalchemy import Integer, BigInteger, Column, String, DateTime, func, ForeignKey, Boolean, DECIMAL
from sqlalchemy.orm import relationship, Mapped
from ..__init__ import Base

class PerfUiAutoCases(Base):
    __tablename__ = 'perf_ui_auto_cases'

    case_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    business_id = Column(Integer, nullable=False, comment='业务线id')
    case_name = Column(String(255), nullable=False, comment='用例名称')
    case_level = Column(String(255), nullable=True, comment='用例等级')
    owner = Column(String(255), nullable=False, comment='责任人')
    timeout = Column(BigInteger, nullable=False, comment='超时时间')
    platform = Column(String(255), nullable=False, comment='平台')
    device_count = Column(Integer, nullable=False, comment='占用设备数')
    title = Column(String(255), nullable=False, comment='用例名称')
    description = Column(String(255), nullable=True, comment='用例描述')
    tags = Column(String(255), nullable=True, comment='标签')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    dir = Column(String(255),nullable=True,comment='用例路径')