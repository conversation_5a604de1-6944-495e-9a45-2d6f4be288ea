from sqlalchemy import Integer, BigInteger, Column, String, DateTime, func, ForeignKey, Boolean, DECIMAL
from sqlalchemy.orm import relationship, Mapped
from ..__init__ import Base

class PerfUiAutoApp(Base):
    __tablename__ = 'perf_ui_auto_app'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    business_id = Column(BigInteger, nullable=False, comment='项目团队')
    platform = Column(Integer, nullable=False, comment='平台, 1-android, 2-ios')
    name = Column(String(255), nullable=False, comment='app 名称（拼接出来的，非app本名）')
    version = Column(String(255), nullable=False, comment='app 版本')
    app_type = Column(Integer, nullable=False, comment='包类型 1-性能包 2-基准包 3-辅助包')
    url = Column(String(2000), nullable=False, comment='app url')
    jen<PERSON>_build_result_url = Column(String(2000), comment='仅平台为android时使用，mapping文件链接')
    repackage_cert = Column(String(2000), comment='仅平台为ios时使用，重打包证书')
    creator = Column(String(255), nullable=False, comment='创建者')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')


class PerfUiAutoAppGroup(Base):
    __tablename__ = 'perf_ui_auto_app_group'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    business_id = Column(BigInteger, nullable=False, comment='项目团队')
    name = Column(String(255), comment='app组名称')
    version = Column(String(255), comment='app组版本')
    android_perf_app_id_list = Column(String(500), nullable=False, comment='android性能测试包id json list')
    android_assist_app_id_list = Column(String(500), nullable=False, comment='android辅助测试包id json list')
    ios_perf_app_id_list = Column(String(500), nullable=False, comment='ios性能测试包id json list')
    ios_assist_app_id_list = Column(String(500), nullable=False, comment='ios辅助测试包id json list')
    creator = Column(String(255), nullable=False, comment='创建者')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')