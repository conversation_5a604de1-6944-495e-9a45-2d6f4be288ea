'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/models/perf_ui_auto/perf_metric_model.py
Description: 性能指标模型
'''
from sqlalchemy import Column, String, DateTime, Integer, BigInteger, JSON
from sqlalchemy.sql import func
from ..__init__ import Base

class PerformanceMetric(Base):
    """
    性能指标表
    定义各种性能指标的基本信息
    """
    __tablename__ = "perf_ui_auto_metric"
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="指标ID")
    
    # 指标基本信息
    metric_key = Column(String(64), nullable=False, unique=False, comment="指标key")
    metric_name = Column(String(128), nullable=False, comment="指标名称")
    metric_type = Column(Integer, nullable=False, default=1, comment="指标类型 1-common 2-android 3-ios")
    metric_category = Column(Integer, nullable=False, default=1, comment="指标分类 1-DS基础性能 2-GamePerf基础性能指标 3-Trace指标 4-ByteIO指标")
    metric_unit = Column(String(32), nullable=True, comment="指标单位")
    metric_desc = Column(String(255), nullable=True, comment="指标描述")
    
    # 查询条件
    query_conditions = Column(JSON, nullable=True, comment="查询条件配置，JSON格式")
    
    # 创建/更新信息
    creator = Column(String(64), nullable=False, comment="创建者")
    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment="创建时间")
    update_time = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="更新时间")
