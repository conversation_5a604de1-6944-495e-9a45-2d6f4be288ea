from sqlalchemy import Integer, BigInteger, Column, String, DateTime, func, ForeignKey
from sqlalchemy.orm import relationship
from ..__init__ import Base

class PerfUiAutoClient(Base):
    """性能测试客户端表"""
    __tablename__ = 'perf_ui_auto_client'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键id')
    business_id = Column(Integer, nullable=False, default=0, comment='业务线id')
    name = Column(String(255), nullable=True, comment='性能客户端名称')
    sys_type = Column(Integer, nullable=True, comment='系统类型')
    mac_address = Column(String(255), nullable=False, unique=True, comment='Mac地址')
    ipv4 = Column(String(255), nullable=True, comment='ipv4')
    ipv6 = Column(String(255), nullable=True, comment='ipv6')
    port = Column(Integer, nullable=True, comment='端口')
    state = Column(Integer, nullable=False, comment='状态')
    owner = Column(String(255), default="", nullable=False, comment='负责人')
    create_time = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    # 关联关系
    perf_ui_auto_devices = relationship("PerfUiAutoDevice", back_populates="perf_ui_auto_client")