'''
Author: hejiabei.oxep <EMAIL>
Date: 2025-01-16 19:31:54
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_stats_db.py
Description: 性能统计数据访问层
'''
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, case, desc, text
from sqlalchemy.orm import aliased

from dals.db.base import SQLAlchemyDB
# 删除统计表模型导入，改为基于现有表的实时查询
from models.perf_ui_auto.perf_case_model import PerfUiAutoCases
from models.perf_ui_auto.perf_task_model import PerfUiAutoTask, PerfUiAutoSubTask, PerfCaseRunDetail
from models.business.business_application_model import Business
from defines.perf_ui_auto_define import (
    TASK_STATUS_SUCCESS,
    TASK_STATUS_FAILED,
    CASE_RUN_STATUS_SUCCESS,
    CASE_RUN_STATUS_FAILED
)
from utils.common.bytelog import byte_log


class PerfStatsDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfStatsDB, self).__init__()

    def get_business_case_stats(self, business_id: Optional[int] = None,
                               start_date: Optional[date] = None,
                               end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        获取业务用例统计数据 - 基于现有表实时查询
        @param business_id: 业务线ID，为None时获取所有业务线
        @param start_date: 开始日期（用于过滤用例创建时间）
        @param end_date: 结束日期（用于过滤用例创建时间）
        @return: 统计数据列表
        """
        try:
            with self.get_db() as session:
                # 基于现有表实时计算用例统计
                # 子查询：获取最近30天有执行记录的用例ID
                active_cases_subquery = session.query(
                    func.distinct(PerfCaseRunDetail.case_id).label('case_id')
                ).filter(
                    PerfCaseRunDetail.create_time >= func.date_sub(func.now(), text("INTERVAL 30 DAY"))
                ).subquery()

                query = session.query(
                    Business.id.label('business_id'),
                    Business.business_name.label('business_name'),
                    func.count(PerfUiAutoCases.case_id).label('total_cases'),
                    func.count(active_cases_subquery.c.case_id).label('active_cases')
                ).select_from(Business).outerjoin(
                    PerfUiAutoCases, Business.id == PerfUiAutoCases.business_id
                ).outerjoin(
                    active_cases_subquery, PerfUiAutoCases.case_id == active_cases_subquery.c.case_id
                )

                # 添加过滤条件
                if business_id:
                    query = query.filter(Business.id == business_id)

                if start_date:
                    query = query.filter(PerfUiAutoCases.create_time >= start_date)

                if end_date:
                    query = query.filter(PerfUiAutoCases.create_time <= end_date)

                query = query.group_by(Business.id, Business.business_name)

                results = query.all()

                return [
                    {
                        'business_id': result.business_id,
                        'business_name': result.business_name,
                        'total_cases': result.total_cases or 0,
                        'active_cases': result.active_cases or 0,
                        'stats_date': date.today()
                    }
                    for result in results
                ]
        except Exception as e:
            byte_log.error(f"获取业务用例统计失败: {str(e)}")
            return []

    def get_task_execution_stats(self, business_id: Optional[int] = None,
                                start_date: Optional[date] = None,
                                end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        获取任务执行统计数据 - 基于现有表实时查询
        @param business_id: 业务线ID，为None时获取所有业务线
        @param start_date: 开始日期（用于过滤任务创建时间）
        @param end_date: 结束日期（用于过滤任务创建时间）
        @return: 统计数据列表
        """
        try:
            with self.get_db() as session:
                # 基于现有表实时计算任务执行统计
                query = session.query(
                    Business.id.label('business_id'),
                    Business.business_name.label('business_name'),
                    func.count(PerfUiAutoTask.id).label('total_executions'),
                    func.count(
                        case(
                            (PerfUiAutoTask.status == TASK_STATUS_SUCCESS, 1),
                            else_=None
                        )
                    ).label('success_executions'),
                    func.count(
                        case(
                            (PerfUiAutoTask.status == TASK_STATUS_FAILED, 1),
                            else_=None
                        )
                    ).label('failed_executions'),
                    func.avg(PerfUiAutoTask.duration).label('avg_duration')
                ).select_from(Business).outerjoin(
                    PerfUiAutoTask, Business.id == PerfUiAutoTask.business_id
                )

                # 添加过滤条件
                if business_id:
                    query = query.filter(Business.id == business_id)

                if start_date:
                    query = query.filter(PerfUiAutoTask.create_time >= start_date)

                if end_date:
                    query = query.filter(PerfUiAutoTask.create_time <= end_date)

                query = query.group_by(Business.id, Business.business_name)

                results = query.all()

                return [
                    {
                        'business_id': result.business_id,
                        'business_name': result.business_name,
                        'total_executions': result.total_executions or 0,
                        'success_executions': result.success_executions or 0,
                        'failed_executions': result.failed_executions or 0,
                        'success_rate': round(
                            (result.success_executions or 0) * 100.0 / max(result.total_executions or 1, 1), 2
                        ),
                        'avg_duration': int(result.avg_duration or 0),
                        'stats_date': date.today()
                    }
                    for result in results
                ]
        except Exception as e:
            byte_log.error(f"获取任务执行统计失败: {str(e)}")
            return []

    def get_case_execution_stats(self, business_id: Optional[int] = None,
                                case_id: Optional[int] = None,
                                start_date: Optional[date] = None,
                                end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        获取用例执行统计数据 - 基于现有表实时查询
        @param business_id: 业务线ID，为None时获取所有业务线
        @param case_id: 用例ID，为None时获取所有用例
        @param start_date: 开始日期（用于过滤用例执行时间）
        @param end_date: 结束日期（用于过滤用例执行时间）
        @return: 统计数据列表
        """
        try:
            with self.get_db() as session:
                # 基于现有表实时计算用例执行统计
                query = session.query(
                    Business.id.label('business_id'),
                    Business.business_name.label('business_name'),
                    PerfUiAutoCases.case_id.label('case_id'),
                    PerfUiAutoCases.case_name.label('case_name'),
                    func.count(PerfCaseRunDetail.id).label('total_executions'),
                    func.count(
                        case(
                            (PerfCaseRunDetail.status == CASE_RUN_STATUS_SUCCESS, 1),
                            else_=None
                        )
                    ).label('success_executions'),
                    func.count(
                        case(
                            (PerfCaseRunDetail.status == CASE_RUN_STATUS_FAILED, 1),
                            else_=None
                        )
                    ).label('failed_executions')
                ).select_from(Business).join(
                    PerfUiAutoCases, Business.id == PerfUiAutoCases.business_id
                ).outerjoin(
                    PerfCaseRunDetail, PerfUiAutoCases.case_id == PerfCaseRunDetail.case_id
                )

                # 添加过滤条件
                if business_id:
                    query = query.filter(Business.id == business_id)

                if case_id:
                    query = query.filter(PerfUiAutoCases.case_id == case_id)

                if start_date:
                    query = query.filter(PerfCaseRunDetail.create_time >= start_date)

                if end_date:
                    query = query.filter(PerfCaseRunDetail.create_time <= end_date)

                query = query.group_by(
                    Business.id, Business.business_name,
                    PerfUiAutoCases.case_id, PerfUiAutoCases.case_name
                )

                results = query.all()

                return [
                    {
                        'business_id': result.business_id,
                        'business_name': result.business_name,
                        'case_id': result.case_id,
                        'case_name': result.case_name,
                        'total_executions': result.total_executions or 0,
                        'success_executions': result.success_executions or 0,
                        'failed_executions': result.failed_executions or 0,
                        'success_rate': round(
                            (result.success_executions or 0) * 100.0 / max(result.total_executions or 1, 1), 2
                        ),
                        'avg_duration': 0,  # 基于现有表结构，暂时设为0
                        'stats_date': date.today()
                    }
                    for result in results
                ]
        except Exception as e:
            byte_log.error(f"获取用例执行统计失败: {str(e)}")
            return []

    def get_business_case_trend(self, business_id: Optional[int] = None,
                               days: int = 30) -> List[Dict[str, Any]]:
        """
        获取业务用例趋势数据 - 基于现有表实时查询
        @param business_id: 业务线ID，为None时获取所有业务线
        @param days: 统计天数
        @return: 趋势数据列表
        """
        try:
            with self.get_db() as session:
                end_date = date.today()
                start_date = end_date - timedelta(days=days)

                # 基于现有表实时计算用例趋势，按日期分组统计用例创建情况
                query = session.query(
                    Business.id.label('business_id'),
                    Business.business_name.label('business_name'),
                    func.date(PerfUiAutoCases.create_time).label('stats_date'),
                    func.count(PerfUiAutoCases.case_id).label('daily_new_cases')
                ).select_from(Business).join(
                    PerfUiAutoCases, Business.id == PerfUiAutoCases.business_id
                ).filter(
                    and_(
                        PerfUiAutoCases.create_time >= start_date,
                        PerfUiAutoCases.create_time <= end_date
                    )
                )

                if business_id:
                    query = query.filter(Business.id == business_id)

                query = query.group_by(
                    Business.id, Business.business_name,
                    func.date(PerfUiAutoCases.create_time)
                ).order_by(func.date(PerfUiAutoCases.create_time))

                results = query.all()

                # 计算累计用例数
                business_totals = {}
                trend_data = []

                for result in results:
                    business_key = result.business_id
                    if business_key not in business_totals:
                        # 获取该日期之前的总用例数
                        prev_count_query = session.query(
                            func.count(PerfUiAutoCases.case_id)
                        ).filter(
                            and_(
                                PerfUiAutoCases.business_id == result.business_id,
                                PerfUiAutoCases.create_time < result.stats_date
                            )
                        )
                        business_totals[business_key] = prev_count_query.scalar() or 0

                    business_totals[business_key] += result.daily_new_cases or 0

                    trend_data.append({
                        'business_id': result.business_id,
                        'business_name': result.business_name,
                        'total_cases': business_totals[business_key],
                        'stats_date': result.stats_date
                    })

                return trend_data
        except Exception as e:
            byte_log.error(f"获取业务用例趋势失败: {str(e)}")
            return []

    def get_task_execution_trend(self, business_id: Optional[int] = None,
                                days: int = 30) -> List[Dict[str, Any]]:
        """
        获取任务执行趋势数据 - 基于现有表实时查询
        @param business_id: 业务线ID，为None时获取所有业务线
        @param days: 统计天数
        @return: 趋势数据列表
        """
        try:
            with self.get_db() as session:
                end_date = date.today()
                start_date = end_date - timedelta(days=days)

                # 基于现有表实时计算任务执行趋势，按日期分组统计
                query = session.query(
                    Business.id.label('business_id'),
                    Business.business_name.label('business_name'),
                    func.date(PerfUiAutoTask.create_time).label('stats_date'),
                    func.count(PerfUiAutoTask.id).label('total_executions'),
                    func.count(
                        case(
                            (PerfUiAutoTask.status == TASK_STATUS_SUCCESS, 1),
                            else_=None
                        )
                    ).label('success_executions'),
                    func.count(
                        case(
                            (PerfUiAutoTask.status == TASK_STATUS_FAILED, 1),
                            else_=None
                        )
                    ).label('failed_executions'),
                    func.avg(PerfUiAutoTask.duration).label('avg_duration')
                ).select_from(Business).join(
                    PerfUiAutoTask, Business.id == PerfUiAutoTask.business_id
                ).filter(
                    and_(
                        PerfUiAutoTask.create_time >= start_date,
                        PerfUiAutoTask.create_time <= end_date
                    )
                )

                if business_id:
                    query = query.filter(Business.id == business_id)

                query = query.group_by(
                    Business.id, Business.business_name,
                    func.date(PerfUiAutoTask.create_time)
                ).order_by(func.date(PerfUiAutoTask.create_time))

                results = query.all()

                return [
                    {
                        'business_id': result.business_id,
                        'business_name': result.business_name,
                        'total_executions': result.total_executions or 0,
                        'success_executions': result.success_executions or 0,
                        'failed_executions': result.failed_executions or 0,
                        'success_rate': round(
                            (result.success_executions or 0) * 100.0 / max(result.total_executions or 1, 1), 2
                        ),
                        'avg_duration': int(result.avg_duration or 0),
                        'stats_date': result.stats_date
                    }
                    for result in results
                ]
        except Exception as e:
            byte_log.error(f"获取任务执行趋势失败: {str(e)}")
            return []

    def get_case_execution_trend(self, business_id: Optional[int] = None,
                                case_id: Optional[int] = None,
                                days: int = 30) -> List[Dict[str, Any]]:
        """
        获取用例执行趋势数据 - 基于现有表实时查询
        @param business_id: 业务线ID，为None时获取所有业务线
        @param case_id: 用例ID，为None时获取所有用例
        @param days: 统计天数
        @return: 趋势数据列表
        """
        try:
            with self.get_db() as session:
                end_date = date.today()
                start_date = end_date - timedelta(days=days)

                # 基于现有表实时计算用例执行趋势，按日期分组统计
                query = session.query(
                    Business.id.label('business_id'),
                    Business.business_name.label('business_name'),
                    PerfUiAutoCases.case_id.label('case_id'),
                    PerfUiAutoCases.case_name.label('case_name'),
                    func.date(PerfCaseRunDetail.create_time).label('stats_date'),
                    func.count(PerfCaseRunDetail.id).label('total_executions'),
                    func.count(
                        case(
                            (PerfCaseRunDetail.status == CASE_RUN_STATUS_SUCCESS, 1),
                            else_=None
                        )
                    ).label('success_executions'),
                    func.count(
                        case(
                            (PerfCaseRunDetail.status == CASE_RUN_STATUS_FAILED, 1),
                            else_=None
                        )
                    ).label('failed_executions')
                ).select_from(Business).join(
                    PerfUiAutoCases, Business.id == PerfUiAutoCases.business_id
                ).join(
                    PerfCaseRunDetail, PerfUiAutoCases.case_id == PerfCaseRunDetail.case_id
                ).filter(
                    and_(
                        PerfCaseRunDetail.create_time >= start_date,
                        PerfCaseRunDetail.create_time <= end_date
                    )
                )

                if business_id:
                    query = query.filter(Business.id == business_id)

                if case_id:
                    query = query.filter(PerfUiAutoCases.case_id == case_id)

                query = query.group_by(
                    Business.id, Business.business_name,
                    PerfUiAutoCases.case_id, PerfUiAutoCases.case_name,
                    func.date(PerfCaseRunDetail.create_time)
                ).order_by(func.date(PerfCaseRunDetail.create_time))

                results = query.all()

                return [
                    {
                        'business_id': result.business_id,
                        'business_name': result.business_name,
                        'case_id': result.case_id,
                        'case_name': result.case_name,
                        'total_executions': result.total_executions or 0,
                        'success_executions': result.success_executions or 0,
                        'failed_executions': result.failed_executions or 0,
                        'success_rate': round(
                            (result.success_executions or 0) * 100.0 / max(result.total_executions or 1, 1), 2
                        ),
                        'avg_duration': 0,  # 基于现有表结构，暂时设为0
                        'stats_date': result.stats_date
                    }
                    for result in results
                ]
        except Exception as e:
            byte_log.error(f"获取用例执行趋势失败: {str(e)}")
            return []
