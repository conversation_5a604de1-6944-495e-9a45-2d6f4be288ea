import json
from copy import deepcopy

from dals.db.base import SQLAlchemyDB
from defines.perf_ui_auto_define import PERF_APP_TYPE_MAP, PLATFORM_MAP
from models.business.business_application_model import Business
from models.perf_ui_auto.perf_app_model import PerfUiAutoApp, PerfUiAutoAppGroup
from schemas.page import PageSchema
from schemas.request.perf_ui_auto.perf_app_req import AddAppParams, GetAppGroupListParams, AddAppGroupParams, \
    GetAppListParams, UpdateAppParams, UpdateAppGroupParams


class PerfAppDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfAppDB, self).__init__()

    def get_app_list(self, page: PageSchema, params: GetAppListParams):
        model = [
                 PerfUiAutoApp.id, PerfUiAutoApp.business_id, PerfUiAutoApp.platform, PerfUiAutoApp.name,
                 PerfUiAutoApp.version, PerfUiAutoApp.app_type, PerfUiAutoApp.url, PerfUiAutoApp.jenkins_build_result_url,
                 PerfUiAutoApp.repackage_cert, PerfUiAutoApp.creator, PerfUiAutoApp.create_time, PerfUiAutoApp.update_time,
                 Business.business_name]
        joins = [(Business, PerfUiAutoApp.business_id == Business.id)]
        conditions = []
        conditions_dict = {
            "id": PerfUiAutoApp.id,
            "business_id": PerfUiAutoApp.business_id,
            "platform": PerfUiAutoApp.platform,
            "name": PerfUiAutoApp.name,
            "version": PerfUiAutoApp.version,
            "app_type": PerfUiAutoApp.app_type,
        }
        for param_key, param_value in params:
            if not param_value:
                continue
            for condition_key, condition_value in conditions_dict.items():
                if isinstance(param_value, list):
                    # 添加筛选项多选
                    if param_key == condition_key:
                        conditions.append(condition_value.in_(param_value))
                else:
                    # 添加筛选项单选
                    if param_key == condition_key:
                        conditions.append(condition_value == param_value)
        order = [PerfUiAutoApp.id.desc()]
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        total, results = self.query(model, filters=conditions, order_by=order, joins=joins, offset=offset, limit=limit)
        return total, results

    def insert_app(self, params:AddAppParams):
        # 数据处理
        data = vars(deepcopy(params))
        data["name"] = f"{params.version}-{PLATFORM_MAP[params.platform]}-{PERF_APP_TYPE_MAP[params.app_type]}"
        item = PerfUiAutoApp(**data)
        self.add_one(item)
        # 插入失败
        if not item.id:
            return None
        # 返回插入以后的数据
        return item

    def update_app(self, params:UpdateAppParams):
        model = PerfUiAutoApp
        conditions = [PerfUiAutoApp.id == params.id]
        results = self.query(model, filters=conditions, return_first=True)
        if results:
            data = vars(deepcopy(params))
            data["name"] = f"{params.version}-{PLATFORM_MAP[params.platform]}-{PERF_APP_TYPE_MAP[params.app_type]}"
            return self.update_one(results, data)
        else:
            return None

    def get_app_group_list(self, page: PageSchema, params: GetAppGroupListParams):
        model = [PerfUiAutoAppGroup.id, PerfUiAutoAppGroup.business_id, PerfUiAutoAppGroup.name,
                 PerfUiAutoAppGroup.version, PerfUiAutoAppGroup.creator, PerfUiAutoAppGroup.create_time, PerfUiAutoAppGroup.update_time,
                 PerfUiAutoAppGroup.android_perf_app_id_list, PerfUiAutoAppGroup.android_assist_app_id_list,
                 PerfUiAutoAppGroup.ios_perf_app_id_list, PerfUiAutoAppGroup.ios_assist_app_id_list,
                 Business.business_name]
        joins = [(Business, PerfUiAutoAppGroup.business_id == Business.id)]
        conditions = []
        conditions_dict = {
            "business_id": PerfUiAutoAppGroup.business_id,
            "version": PerfUiAutoAppGroup.version,
            "app_group_name": PerfUiAutoAppGroup.name
        }
        for param_key, param_value in params:
            if not param_value:
                continue
            for condition_key, condition_value in conditions_dict.items():
                if condition_key in ("app_group_name") and param_key == condition_key:
                    conditions.append(condition_value.like(f"%{param_value}%"))
                elif isinstance(param_value, list):
                    # 添加筛选项多选
                    if param_key == condition_key:
                        conditions.append(condition_value.in_(param_value))
                else:
                    # 添加筛选项单选
                    if param_key == condition_key:
                        conditions.append(condition_value == param_value)
        order = [PerfUiAutoAppGroup.id.desc()]
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        total, results = self.query(model, filters=conditions, order_by=order, offset=offset,
                                    joins=joins, limit=limit)
        return total, results

    def insert_app_group(self, params: AddAppGroupParams):
        # 数据处理
        data = vars(deepcopy(params))
        data["android_perf_app_id_list"] = json.dumps(data["android_perf_app_id_list"])
        data["android_assist_app_id_list"] = json.dumps(data["android_assist_app_id_list"])
        data["ios_perf_app_id_list"] = json.dumps(data["ios_perf_app_id_list"])
        data["ios_assist_app_id_list"] = json.dumps(data["ios_assist_app_id_list"])
        item = PerfUiAutoAppGroup(**data)
        self.add_one(item)
        # 插入失败
        if not item.id:
            return None
        # 返回插入以后的数据
        return item
    
    def update_app_group(self, params: UpdateAppGroupParams):
        model = PerfUiAutoAppGroup
        conditions = [PerfUiAutoAppGroup.id == params.id]
        results = self.query(model, filters=conditions, return_first=True)
        if results:
            data = vars(deepcopy(params))
            data["android_perf_app_id_list"] = json.dumps(data["android_perf_app_id_list"])
            data["android_assist_app_id_list"] = json.dumps(data["android_assist_app_id_list"])
            data["ios_perf_app_id_list"] = json.dumps(data["ios_perf_app_id_list"])
            data["ios_assist_app_id_list"] = json.dumps(data["ios_assist_app_id_list"])
            return self.update_one(results, data)
        else:
            return None

    def get_app_group_detail(self, id):
        model = [PerfUiAutoAppGroup]
        conditions = [PerfUiAutoAppGroup.id == id]
        result = self.query(model, filters=conditions, return_first=True)
        return result

    def get_version_list(self, page: PageSchema, business_id: int = None, version: str = None):
        """
        获取版本列表
        @param page: 分页参数
        @param business_id: 业务ID
        @param version: 版本号
        @return: 总数和结果列表
        """
        model = [PerfUiAutoApp.version.distinct()]
        conditions = []
        
        # 添加 business_id 筛选条件
        if business_id:
            conditions.append(PerfUiAutoApp.business_id == business_id)
            
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        order = [PerfUiAutoApp.version.desc()]
        
        if version:
            version_conditions = conditions.copy()
            version_conditions.append(PerfUiAutoApp.version == version)
            full_match_total, full_match_results = self.query(model, filters=version_conditions,
                                                          order_by=order, offset=offset,
                                                          limit=limit)
                                                          
            version_conditions = conditions.copy()
            version_conditions.append(PerfUiAutoApp.version.like(f"%{version}%"))
            partial_match_total, partial_match_results = self.query(model,
                                                                filters=version_conditions,
                                                                order_by=order, offset=offset,
                                                                limit=limit)
            results = list(full_match_results) + [i for i in partial_match_results if i not in full_match_results]
            total = full_match_total + partial_match_total
            return total, [i[0] for i in results]
            
        total, results = self.query(model, filters=conditions, order_by=order, offset=offset, limit=limit)
        return total, [i[0] for i in results]

    def delete_app(self, id: int):
        """
        删除应用
        @param id: 应用ID
        """
        model = PerfUiAutoApp
        conditions = [PerfUiAutoApp.id == id]
        app = self.query(model, filters=conditions, return_first=True)
        if not app:
            raise Exception("应用不存在")
        self.delete_one(app)

    def delete_app_group(self, id: int):
        """
        删除应用组
        @param id: 应用组ID
        """
        model = PerfUiAutoAppGroup
        conditions = [PerfUiAutoAppGroup.id == id]
        app_group = self.query(model, filters=conditions, return_first=True)
        if not app_group:
            raise Exception("应用组不存在")
        self.delete_one(app_group)

perf_app_db = PerfAppDB()
