'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_client_db.py
Description: 
'''
from dals.db.base import SQLAlchemyDB
from models.business.business_application_model import Business
from models.perf_ui_auto.perf_client_model import PerfUiAutoClient
from schemas.request.perf_ui_auto.perf_client_req import RegisterPerfClientParams, UpdateClientDetailParams
from typing import Optional


class PerfClientDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfClientDB, self).__init__()

    def get_perf_client_list(self):
        """
        获取性能客户端列表
        @return:
        """
        model_class = [PerfUiAutoClient, Business]
        conditions = [PerfUiAutoClient.business_id == Business.id]
        order = [PerfUiAutoClient.state.desc(), PerfUiAutoClient.id.desc()]
        total, results = self.query(model_class, filters=conditions, order_by=order)
        return total, results

    def register_perf_client(self, params: RegisterPerfClientParams):
        """
        注册或更新性能测试客户端。
        如果客户端已存在（根据MAC地址判断），则更新现有记录；
        如果不存在，则注册新客户端。

        @param params: RegisterPerfClientParams实例，包含注册或更新客户端所需的信息
        @return: 注册或更新后的客户端实例
        """
        model_class = [PerfUiAutoClient]
        conditions = [PerfUiAutoClient.mac_address == params.mac_address]
        return_first = self.query(model_class, filters=conditions, return_first=True)
        params_dict = params.model_dump()
        if return_first:
            new_dict = {key: value for key, value in params_dict.items() if value is not None}
            return self.update_one(return_first, new_dict)
        else:
            item = PerfUiAutoClient(**params_dict)
            self.add_one(item)
            return item

    def update_client(self, client_detail: UpdateClientDetailParams):
        """
        更新客户端信息，只更新非空字段
        
        @param client_detail: UpdateClientDetailParams实例，包含要更新的字段
        @return: 更新后的客户端信息
        """
        model_class = [PerfUiAutoClient]
        conditions = [PerfUiAutoClient.id == client_detail.id]
        client = self.query(model_class, filters=conditions, return_first=True)
        
        if not client:
            raise ValueError(f"未找到ID为{client_detail.id}的客户端")
            
        # 只更新非空字段
        update_data = {k: v for k, v in client_detail.model_dump().items() 
                      if v is not None and k != 'id'}
        
        if update_data:
            self.update_one(client, update_data)
            
        return client

    def del_perf_client(self, client_id):
        """
        删除性能客户端
        @param client_id:
        @return:
        """
        model_class = [PerfUiAutoClient]
        conditions = [PerfUiAutoClient.id == client_id]
        result = self.query(model_class, filters=conditions, return_first=True)
        self.delete_one(result)

    def get_client_detail(self, client_id: Optional[int] = None, mac_address: Optional[str] = None):
        """
        通过客户端ID或MAC地址获取客户端详情

        @param client_id: 客户端ID
        @param mac_address: MAC地址
        @return: 客户端详情
        """
        model_class = [PerfUiAutoClient, Business]
        conditions = [PerfUiAutoClient.business_id == Business.id]
        
        if client_id:
            conditions.append(PerfUiAutoClient.id == client_id)
        elif mac_address:
            conditions.append(PerfUiAutoClient.mac_address == mac_address)
            
        return self.query(model_class, filters=conditions, return_first=True)


perf_client_db = PerfClientDB()
