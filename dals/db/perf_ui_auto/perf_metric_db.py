'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_metric_db.py
Description: 性能指标数据库操作
'''
from typing import Optional, List, Tuple, Dict
from defines.perf_ui_auto_define import METRIC_CATEGORY_TRACE, METRIC_CATEGORY_BYTEIO
from dals.db.base import SQLAlchemyDB
from models.perf_ui_auto.perf_metric_model import PerformanceMetric
from schemas.request.perf_ui_auto.perf_metric_req import CreateMetricRequest, UpdateMetricRequest, MetricListRequest, BatchCreateMetricRequest

class PerfMetricDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfMetricDB, self).__init__()

    def create_metric(self, params: CreateMetricRequest) -> PerformanceMetric:
        """
        创建性能指标
        @param params: 创建指标参数
        @return: PerformanceMetric
        """
        try:
            # 检查metric_key是否已存在
            existing = self.query(
                [PerformanceMetric], 
                filters=[PerformanceMetric.metric_key == params.metric_key],
                return_first=True
            )
            if existing:
                raise Exception(f"指标key[{params.metric_key}]已存在")

            # 验证查询条件
            if params.metric_category in [METRIC_CATEGORY_TRACE, METRIC_CATEGORY_BYTEIO]:  # Trace采集指标或ByteIO指标
                if not params.query_conditions:
                    raise Exception(f"指标类型{params.metric_category}必须提供查询条件")
                
                if params.metric_category == METRIC_CATEGORY_TRACE:  # Trace采集指标
                    required_fields = ["namespace", "start", "end", "offset", "limit", "filter", "query_string", "owner"]
                    if not all(field in params.query_conditions for field in required_fields):
                        raise Exception("Trace采集指标缺少必要的查询条件字段")
                
                if params.metric_category == METRIC_CATEGORY_BYTEIO:  # ByteIO指标
                    required_fields = ["app_id", "device_id", "start_time", "end_time", "log_types", "event_names"]
                    if not all(field in params.query_conditions for field in required_fields):
                        raise Exception("ByteIO指标缺少必要的查询条件字段")

            # 创建新指标
            params_dict = params.model_dump(exclude_none=True)
            item = PerformanceMetric(**params_dict)
            self.add_one(item)
            return item
        except Exception as e:
            raise Exception(f"创建性能指标失败: {str(e)}")

    def batch_create_metrics(self, params: BatchCreateMetricRequest) -> Tuple[List[PerformanceMetric], List[Dict]]:
        """
        批量创建性能指标
        @param params: 批量创建指标参数
        @return: Tuple[成功创建的指标列表, 创建失败的指标信息列表]
        """
        success_metrics = []
        failed_metrics = []

        # 获取所有metric_key
        metric_keys = [metric.metric_key for metric in params.metrics]
        
        try:
            # 批量检查metric_key是否已存在
            existing_keys = self.query(
                [PerformanceMetric.metric_key],
                filters=[PerformanceMetric.metric_key.in_(metric_keys)]
            )[1]
            existing_keys = [item.metric_key for item in existing_keys]

            # 批量创建指标
            for metric in params.metrics:
                try:
                    if metric.metric_key in existing_keys:
                        failed_metrics.append({
                            "metric_key": metric.metric_key,
                            "error_msg": f"指标key[{metric.metric_key}]已存在"
                        })
                        continue

                    params_dict = metric.model_dump(exclude_none=True)
                    item = PerformanceMetric(**params_dict)
                    self.add_one(item)
                    success_metrics.append(item)
                except Exception as e:
                    failed_metrics.append({
                        "metric_key": metric.metric_key,
                        "error_msg": str(e)
                    })

            return success_metrics, failed_metrics
        except Exception as e:
            raise Exception(f"批量创建性能指标失败: {str(e)}")

    def get_metrics(
        self,
        params: MetricListRequest
    ) -> Tuple[int, List[PerformanceMetric]]:
        """
        获取性能指标列表
        @param params: 查询参数
        @return: Tuple[总数, 指标列表]
        """
        try:
            model = [PerformanceMetric]
            conditions = []

            # 添加查询条件
            if params.metric_type:
                conditions.append(PerformanceMetric.metric_type == params.metric_type)
            if params.metric_category:
                conditions.append(PerformanceMetric.metric_category == params.metric_category)
            if params.metric_key:
                conditions.append(PerformanceMetric.metric_key.like(f"%{params.metric_key}%"))
            if params.metric_name:
                conditions.append(PerformanceMetric.metric_name.like(f"%{params.metric_name}%"))
            if params.creator:
                conditions.append(PerformanceMetric.creator == params.creator)
            if params.start_time:
                conditions.append(PerformanceMetric.create_time >= params.start_time)
            if params.end_time:
                conditions.append(PerformanceMetric.create_time <= params.end_time)

            # 计算分页
            offset = (params.page - 1) * params.page_size

            return self.query(
                model,
                filters=conditions,
                offset=offset,
                limit=params.page_size,
                order_by=[PerformanceMetric.id.desc()]
            )
        except Exception as e:
            raise Exception(f"获取性能指标列表失败: {str(e)}")

    def get_metric(self, metric_id: int) -> Optional[PerformanceMetric]:
        """
        获取单个性能指标
        @param metric_id: 指标ID
        @return: Optional[PerformanceMetric]
        """
        try:
            return self.query(
                [PerformanceMetric],
                filters=[PerformanceMetric.id == metric_id],
                return_first=True
            )
        except Exception as e:
            raise Exception(f"获取性能指标失败: {str(e)}")

    def update_metric(self, params: UpdateMetricRequest) -> Optional[PerformanceMetric]:
        """
        更新性能指标
        @param params: 更新参数，包含指标ID和更新内容
        @return: Optional[PerformanceMetric]
        """
        try:
            # 获取指标
            metric = self.get_metric(params.id)
            if not metric:
                return None

            # 如果更新metric_key,检查是否已存在
            if params.metric_key and params.metric_key != metric.metric_key:
                existing = self.query(
                    [PerformanceMetric],
                    filters=[
                        PerformanceMetric.metric_key == params.metric_key,
                        PerformanceMetric.id != params.id
                    ],
                    return_first=True
                )
                if existing:
                    raise Exception(f"指标key[{params.metric_key}]已存在")

            # 更新指标
            update_dict = params.model_dump(exclude_unset=True, exclude={"id"})
            self.update_one(metric, update_dict)
            return metric
        except Exception as e:
            raise Exception(f"更新性能指标失败: {str(e)}")

    def delete_metric(self, metric_id: int) -> bool:
        """
        删除性能指标
        @param metric_id: 指标ID
        @return: bool
        """
        try:
            metric = self.get_metric(metric_id)
            if not metric:
                return False
            self.delete_one(metric)
            return True
        except Exception as e:
            raise Exception(f"删除性能指标失败: {str(e)}")

perf_metric_db = PerfMetricDB()
