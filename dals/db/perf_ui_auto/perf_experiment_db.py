'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_experiment_db.py
Description: 实验相关数据库操作
'''
import time
from typing import List, Optional, Tuple

from sqlalchemy import select, update, delete
from dals.db.business.business_db import SQLAlchemyDB
from models.perf_ui_auto.perf_experiment_model import ExperimentModel
from schemas.request.perf_ui_auto.perf_experiment_req import (
    CreateExperimentRequest,
    ListExperimentsRequest,
    UpdateExperimentRequest,
    DeleteExperimentRequest,
    GetExperimentDetailRequest,
)

class PerfExperimentDB(SQLAlchemyDB):
    """实验数据库操作类"""
    
    def __init__(self):
        super(PerfExperimentDB, self).__init__()

    def create_experiment(
        self,
        params: CreateExperimentRequest,
    ) -> ExperimentModel:
        """创建实验"""
        now = int(time.time())
        experiment = ExperimentModel(
            name=params.name,
            hit_type=params.hit_type,
            experiment_group_version_ids=params.experiment_group_version_ids,
            control_group_version_ids=params.control_group_version_ids,
            description=params.description,
            creator=params.creator,
            create_time=now,
            update_time=now,
        )
        self.add_one(experiment)
        return experiment

    def get_experiment_list(
        self,
        params: ListExperimentsRequest,
    ) -> Tuple[int, List[ExperimentModel]]:
        """获取实验列表"""
        model_class = [ExperimentModel]
        conditions = []
        
        if params.name:
            conditions.append(ExperimentModel.name.like(f"%{params.name}%"))
        if params.hit_type is not None:
            conditions.append(ExperimentModel.hit_type == params.hit_type)
        
        # 计算分页偏移量
        offset = (params.page - 1) * params.page_size
        
        return self.query(
            model_class,
            filters=conditions,
            offset=offset,
            limit=params.page_size,
            order_by=[ExperimentModel.id.desc()]
        )

    def update_experiment(
        self,
        params: UpdateExperimentRequest,
    ) -> Optional[ExperimentModel]:
        """更新实验"""
        # 查找实验
        experiment = self.query(
            [ExperimentModel], 
            filters=[ExperimentModel.id == params.id], 
            return_first=True
        )
        if not experiment:
            return None

        # 构建更新数据
        update_data = {"update_time": int(time.time())}
        if params.name is not None:
            update_data["name"] = params.name
        if params.hit_type is not None:
            update_data["hit_type"] = params.hit_type
        if params.experiment_group_version_ids is not None:
            update_data["experiment_group_version_ids"] = params.experiment_group_version_ids
        if params.control_group_version_ids is not None:
            update_data["control_group_version_ids"] = params.control_group_version_ids
        if params.description is not None:
            update_data["description"] = params.description

        self.update_one(experiment, update_data)
        return experiment

    def delete_experiment(
        self,
        params: DeleteExperimentRequest,
    ) -> bool:
        """删除实验"""
        experiment = self.query(
            [ExperimentModel], 
            filters=[ExperimentModel.id == params.id], 
            return_first=True
        )
        if not experiment:
            return False
            
        self.delete_one(experiment)
        return True

    def get_experiment_detail(
        self,
        params: GetExperimentDetailRequest,
    ) -> Optional[ExperimentModel]:
        """获取实验详情"""
        return self.query(
            [ExperimentModel],
            filters=[ExperimentModel.id == params.id],
            return_first=True
        )

perf_experiment_db = PerfExperimentDB()