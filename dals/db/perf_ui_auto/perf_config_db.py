'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_config_db.py
Description: 性能配置数据库操作
'''
from dals.db.business.business_db import *
from models.perf_ui_auto.perf_config_model import PerformanceConfig
from models.perf_ui_auto.perf_metric_model import PerformanceMetric

from schemas.request.perf_ui_auto.perf_config_req import CreateConfigRequest, UpdateConfigRequest, ConfigListRequest
import json
from datetime import datetime
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy import Index, and_, or_, select
from sqlalchemy.orm import aliased


class PerformanceConfigDB(SQLAlchemyDB):
    def __init__(self):
        super(PerformanceConfigDB, self).__init__()

    def get_metrics_by_ids(self, metric_ids: List[int]) -> List[PerformanceMetric]:
        """
        根据指标ID列表获取指标详情
        @param metric_ids: 指标ID列表
        @return: 指标详情列表
        """
        try:
            if not metric_ids:
                return []
            
            # 使用基类的query方法查询
            model = [PerformanceMetric]
            conditions = [PerformanceMetric.id.in_(metric_ids)]
            query_result = self.query(model, filters=conditions)
            if query_result is None:
                return []
            _, metrics = query_result
            return metrics if metrics is not None else []
        except Exception as e:
            raise Exception(f"获取性能指标详情失败: {str(e)}")

    def create_config(self, request: CreateConfigRequest) -> PerformanceConfig:
        """
        创建性能配置
        @param request: 创建配置请求
        @return: 创建的配置
        """
        try:
            # 验证必填字段
            required_fields = ['business_id', 'config_name', 'metrics']
            for field in required_fields:
                if not getattr(request, field):
                    raise Exception(f"缺少必填字段: {field}")

            # 验证指标ID是否存在
            metric_ids = [metric.metric_id for metric in request.metrics]
            metrics_info = self.get_metrics_by_ids(metric_ids)
            if len(metrics_info) != len(metric_ids):
                found_ids = [m["id"] for m in metrics_info]
                missing_ids = [mid for mid in metric_ids if mid not in found_ids]
                raise Exception(f"以下指标ID不存在: {missing_ids}")

            # 转换参数为字典
            params_dict = request.model_dump(exclude={"metrics"})
            
            # 将指标转换为JSON格式保存
            metrics_dict = []
            for metric in request.metrics:
                metric_dict = metric.model_dump()
                metrics_dict.append(metric_dict)
                
            params_dict['metrics'] = metrics_dict
            
            # 创建配置对象
            item = PerformanceConfig(**params_dict)
            self.add_one(item)
            return item
        except Exception as e:
            raise e

    def get_config(self, config_id: int) -> Optional[PerformanceConfig]:
        """
        获取配置详情
        @param config_id: 配置ID
        @return: 配置详情
        """
        if not config_id:
            return None

        model = [PerformanceConfig]
        conditions = [PerformanceConfig.id == config_id]
        return self.query(model, filters=conditions, return_first=True)

    def get_all_configs(
        self,
        params: ConfigListRequest
    ) -> Tuple[int, List[PerformanceConfig]]:
        """
        获取配置列表
        Args:
            params: 查询参数
                - page: 页码，默认1
                - page_size: 每页数量，默认10，最大100
                - business_id: 可选，业务线ID
                - config_name: 可选，配置名称
                - creator: 可选，创建者
                - start_time: 可选，开始时间
                - end_time: 可选，结束时间
        Returns:
            Tuple[int, List[PerformanceConfig]]: 总数和配置列表
        """
        try:
            model_class = [PerformanceConfig]
            conditions = []

            # 业务线ID筛选
            if params.business_id is not None:
                conditions.append(PerformanceConfig.business_id == params.business_id)

            # 配置名称筛选
            if params.config_name:
                conditions.append(PerformanceConfig.config_name.like(f"%{params.config_name}%"))

            # 创建者筛选
            if params.creator:
                conditions.append(PerformanceConfig.creator == params.creator)

            # 时间范围筛选
            if params.start_time:
                conditions.append(PerformanceConfig.create_time >= params.start_time)
            if params.end_time:
                conditions.append(PerformanceConfig.create_time <= params.end_time)

            # 计算分页偏移量
            offset = (params.page - 1) * params.page_size

            query_result = self.query(
                model_class,
                filters=conditions,
                offset=offset,
                limit=params.page_size,
                order_by=[PerformanceConfig.id.desc()]
            )

            if query_result is None or not isinstance(query_result, tuple) or len(query_result) != 2:
                return 0, []
            total, configs = query_result
            return total, configs if configs is not None else []
        except Exception as e:
            raise Exception(f"获取配置列表失败: {str(e)}")

    def update_config(self, request: UpdateConfigRequest) -> Optional[PerformanceConfig]:
        """
        更新配置
        @param request: 更新配置请求
        @return: 更新后的配置
        """
        try:
            # 验证配置是否存在
            config = self.query([PerformanceConfig], filters=[PerformanceConfig.id == request.id], return_first=True)
            if not config:
                raise Exception("配置不存在")

            # 转换参数为字典，只包含已设置的字段
            update_dict = request.model_dump(exclude_unset=True, exclude={"metrics"})
            
            # 如果更新字段包含必填字段，需要验证其值
            if 'config_name' in update_dict and not update_dict['config_name']:
                raise Exception("配置名称不能为空")
                
            if request.metrics:
                # 验证指标ID是否存在
                metric_ids = [metric.metric_id for metric in request.metrics]
                metrics_info = self.get_metrics_by_ids(metric_ids)
                if len(metrics_info) != len(metric_ids):
                    found_ids = [m["id"] for m in metrics_info]
                    missing_ids = [mid for mid in metric_ids if mid not in found_ids]
                    raise Exception(f"以下指标ID不存在: {missing_ids}")
                
                # 将指标转换为JSON格式保存
                metrics_dict = []
                for metric in request.metrics:
                    metric_dict = metric.model_dump()
                    metrics_dict.append(metric_dict)
                    
                update_dict['metrics'] = metrics_dict

            self.update_one(config, update_dict)
            return config
        except Exception as e:
            raise e

    def delete_config(self, config_id: int) -> bool:
        """
        删除配置
        @param config_id: 配置ID
        @return: 是否删除成功
        """
        try:
            model = PerformanceConfig
            conditions = [model.id == config_id]
            config = self.query(model, filters=conditions, return_first=True)
            if not config:
                raise Exception("配置不存在")
            self.delete_one(config)
            return True
        except Exception as e:
            raise e

perf_config_db = PerformanceConfigDB()
