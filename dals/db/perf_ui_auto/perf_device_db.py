'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_device_db.py
Description: 性能测试设备数据库操作类
'''
from typing import List, Tuple, Optional
from datetime import datetime

from sqlalchemy import or_

from dals.db.base import SQLAlchemyDB
from defines.perf_ui_auto_define import (
    DEVICE_SYS_TYPE_MAP, 
    DEVICE_STATE_MAP, 
    DEVICE_CONNECT_TYPE_MAP,
    DEVICE_STATE_OFFLINE
)
from models.perf_ui_auto.perf_client_model import PerfUiAutoClient
from models.perf_ui_auto.perf_device_model import PerfUiAutoDevice
from schemas.request.perf_ui_auto.perf_device_req import (
    UploadPerfDeviceParams,
    GetDeviceListParams
)


class PerfDeviceDB(SQLAlchemyDB):
    """性能测试设备数据库操作类"""
    
    def __init__(self):
        super(PerfDeviceDB, self).__init__()

    def get_devices_by_client_id(self, client_id: int) -> List[PerfUiAutoDevice]:
        """
        获取指定客户端ID下的所有设备
        @param client_id: 客户端ID
        @return: 设备列表
        """
        model_class = [PerfUiAutoDevice]
        conditions = [
            PerfUiAutoDevice.client_id == client_id
        ]
        _, results = self.query(model_class, filters=conditions)
        return results if results else []

    def get_device_detail(self, device_id: int):
        """
        获取设备详情
        :param device_id: 设备ID
        :return: 设备详细信息
        """
        model_class = [PerfUiAutoDevice]
        conditions = [PerfUiAutoDevice.id == device_id]
        result = self.query(model=model_class, filters=conditions, return_first=True)
        if result:
            device_dict = {k: v for k, v in result.__dict__.items() if not k.startswith('_')}
            return device_dict
        return None

    def get_device_list(self, params: GetDeviceListParams, page: int, page_size: int) -> Tuple[int, List[PerfUiAutoDevice]]:
        """
        获取设备列表（支持分页）
        @param params: 查询参数
        @param page: 页码
        @param page_size: 每页数量
        @return: (总数, 设备列表)
        """
        model_class = [PerfUiAutoDevice]
        conditions = []
        
        # 添加过滤条件
        if params.client_id is not None:
            conditions.append(PerfUiAutoDevice.client_id == params.client_id)
        if params.sys_type is not None:
            conditions.append(PerfUiAutoDevice.sys_type == params.sys_type)
        if params.is_occupied is not None:
            conditions.append(PerfUiAutoDevice.is_occupied == params.is_occupied)
        
        # 计算分页偏移量
        offset = (page - 1) * page_size
                
        return self.query(
            model_class,
            filters=conditions,
            offset=offset,
            limit=page_size,
            order_by=[PerfUiAutoDevice.state.desc(), PerfUiAutoDevice.is_occupied.desc(), PerfUiAutoDevice.id.desc()]
        )

    def get_available_devices(self, sys_type: int, count: int, exclude_device_ids: list = None) -> List[PerfUiAutoDevice]:
        """
        获取指定数量的可用设备
        @param sys_type: 系统类型
        @param count: 需要的设备数量
        @param exclude_device_ids: 需要排除的设备ID列表
        @return: 可用设备列表
        """
        model_class = [PerfUiAutoDevice]
        conditions = [
            PerfUiAutoDevice.sys_type == sys_type,
            PerfUiAutoDevice.is_occupied == 0,
            PerfUiAutoDevice.state != DEVICE_STATE_OFFLINE
        ]
        
        if exclude_device_ids:
            conditions.append(PerfUiAutoDevice.id.notin_(exclude_device_ids))
            
        _, results = self.query(
            model_class, 
            filters=conditions, 
            order_by=[PerfUiAutoDevice.model], 
            limit=count
        )
        return results

    def update_device_occupied(self, device_id: int, is_occupied: int) -> None:
        """
        更新设备占用状态
        @param device_id: 设备ID
        @param is_occupied: 是否被占用 0-未占用 1-已占用
        @return: None
        """
        model_class = [PerfUiAutoDevice]
        conditions = [PerfUiAutoDevice.id == device_id]
        device = self.query(model_class, filters=conditions, return_first=True)
        if not device:
            raise Exception("设备不存在")
        self.update_one(device, {"is_occupied": is_occupied})

    def update_perf_device(self, client_id: int, device_list: List[UploadPerfDeviceParams], 
                          offline_device_ids: List[int]) -> None:
        """
        更新设备信息并设置离线设备
        @param client_id: 客户端ID
        @param device_list: 要更新的设备参数列表
        @param offline_device_ids: 需要设置为离线的设备ID列表
        @return: None
        """
        try:
            # 获取client信息
            model_class = [PerfUiAutoClient]
            conditions = [PerfUiAutoClient.id == client_id]
            client = self.query(model_class, filters=conditions, return_first=True)
            
            if not client:
                raise Exception(f"客户端不存在: {client_id}")
            
            # 批量处理设备更新
            for device in device_list:
                # 转换设备参数
                device_dict = self._convert_device_params(device, client.id)
                
                # 查找已存在的设备
                model_class = [PerfUiAutoDevice]
                conditions = [
                    PerfUiAutoDevice.udid == device.udid,
                    PerfUiAutoDevice.client_id == client.id
                ]
                existing_device = self.query(model_class, filters=conditions, return_first=True)
                
                if existing_device:
                    # 更新已存在的设备
                    device_dict['update_time'] = datetime.now()  # 更新时间
                    self.update_one(existing_device, device_dict)
                else:
                    # 添加新设备
                    new_device = PerfUiAutoDevice(**device_dict)
                    self.add_one(new_device)
            
            # 批量处理离线设备
            if offline_device_ids:
                model_class = PerfUiAutoDevice
                conditions = [PerfUiAutoDevice.id.in_(offline_device_ids)]
                offline_devices = self.query(model_class, filters=conditions, return_query=True)
                
                # 批量更新离线设备状态
                for device in offline_devices:
                    self.update_one(device, {
                        'state': DEVICE_STATE_OFFLINE,
                        'update_time': datetime.now()
                    })
            
        except Exception as e:
            raise Exception(f"更新设备信息失败: {str(e)}")

    def _convert_device_params(self, device: UploadPerfDeviceParams, client_id: int) -> dict:
        """
        转换设备参数为数据库格式
        @param device: 设备参数
        @param client_id: 客户端ID
        @return: 转换后的设备参数字典
        """
        device_dict = device.model_dump()
        
        # 转换枚举值
        for key, value in DEVICE_SYS_TYPE_MAP.items():
            if device.sys_type in value:
                device_dict['sys_type'] = key
                
        for key, value in DEVICE_STATE_MAP.items():
            if device.state in value:
                device_dict['state'] = key
                
        # connect_type 已经是列表，不需要转换
        
        # 添加必需字段的默认值
        device_dict.update({
            'client_id': client_id,
            'is_occupied': 0,
            'create_time': datetime.now(),
            'update_time': datetime.now()
        })
        
        return device_dict

    def delete_device(self, device_id: int) -> None:
        """
        删除设备
        @param device_id: 设备ID
        @return: None
        """
        model_class = [PerfUiAutoDevice]
        conditions = [PerfUiAutoDevice.id == device_id]
        device = self.query(model_class, filters=conditions, return_first=True)
        if not device:
            raise Exception("设备不存在")
        self.delete_one(device)


perf_device_db = PerfDeviceDB()
