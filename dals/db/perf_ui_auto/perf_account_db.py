'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_account_db.py
Description: 
'''
from dals.db.business.business_db import *
from models.business.business_application_model import Business
from models.perf_ui_auto.perf_account_model import PerfUiAutoAccount

from schemas.request.perf_ui_auto.perf_account_req import *
from utils.common.depends import PageQuery
from typing import List, Optional, Tuple
from sqlalchemy import select, and_, func


class PerfAccountDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfAccountDB, self).__init__()

    def get_account_list(
        self, 
        params: GetAccountListRequest
    ) -> Tuple[int, List[PerfUiAutoAccount]]:
        """
        获取账号列表
        Args:
            params: 查询参数
                - page: 页码，默认1
                - page_size: 每页大小，默认10
                - business_id: 可选，业务线ID
                - is_occupied: 可选，是否被占用 0-未占用 1-已占用
                - account_type: 可选，账号类型 1-性能测试账号 2-普通账号
        Returns:
            Tuple[int, List[PerfUiAutoAccount]]: 总数和账号列表
        """
        model_class = [PerfUiAutoAccount]
        conditions = []
        
        if params.business_id is not None:
            conditions.append(PerfUiAutoAccount.business_id == params.business_id)
        if params.is_occupied is not None:
            conditions.append(PerfUiAutoAccount.is_occupied == params.is_occupied)
        if params.account_type is not None:
            conditions.append(PerfUiAutoAccount.account_type == params.account_type)
        
        # 计算分页偏移量
        offset = (params.page - 1) * params.page_size
                
        return self.query(
            model_class,
            filters=conditions,
            offset=offset,
            limit=params.page_size,
            order_by=[PerfUiAutoAccount.is_occupied.desc(), PerfUiAutoAccount.id.desc()]
        )

    def get_account_detail(self, account_id: int) -> Optional[PerfUiAutoAccount]:
        """
        获取账号详情
        @param account_id: 账号ID
        @return: 账号详情
        """
        model = [PerfUiAutoAccount]
        joins = [(Business, PerfUiAutoAccount.business_id == Business.id)]
        conditions = [PerfUiAutoAccount.id == account_id]
        return self.query(model, joins=joins, filters=conditions, return_first=True)

    def add_account(self, params: AddAccountParams):
        """
        添加账号
        @param params: 账号参数
        @return: 添加的账号
        """
        # 验证必填字段
        required_fields = ['business_id', 'uid', 'iphone', 'username', 'owner']
        for field in required_fields:
            if not getattr(params, field):
                raise Exception(f"缺少必填字段: {field}")

        # 转换参数为字典，保留None值
        params_dict = params.model_dump()
        
        # 创建账号对象
        item = PerfUiAutoAccount(**params_dict)
        self.add_one(item)
        return item

    def update_account(self, account_id: int, params: UpdateAccountParams):
        """
        更新账号
        @param account_id: 账号ID
        @param params: 更新参数
        @return: 更新后的账号
        """
        # 验证账号是否存在
        account = self.query([PerfUiAutoAccount], filters=[PerfUiAutoAccount.id == account_id], return_first=True)
        if not account:
            raise Exception("账号不存在")

        # 转换参数为字典，只包含已设置的字段
        update_dict = params.model_dump(exclude_unset=True)
        
        # 如果更新字段包含必填字段，需要验证其值
        required_fields = ['business_id', 'uid', 'iphone', 'username', 'owner']
        for field in required_fields:
            if field in update_dict and not update_dict[field]:
                raise Exception(f"字段 {field} 不能为空")

        self.update_one(account, update_dict)
        return account

    def update_account_occupied(self, account_id: int, is_occupied: int):
        """
        更新账号占用状态
        @param account_id: 账号ID
        @param is_occupied: 是否被占用
        """
        model = PerfUiAutoAccount
        conditions = [model.id == account_id]
        account = self.query(model, filters=conditions, return_first=True)
        if not account:
            raise Exception("账号不存在")
        self.update_one(account, {"is_occupied": is_occupied})

    def delete_account(self, account_id: int):
        """
        删除账号
        @param account_id: 账号ID
        """
        model = PerfUiAutoAccount
        conditions = [model.id == account_id]
        account = self.query(model, filters=conditions, return_first=True)
        if not account:
            raise Exception("账号不存在")
        self.delete_one(account)


perf_account_db = PerfAccountDB()
