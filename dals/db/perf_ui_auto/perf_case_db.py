from typing import List
from dals.db.base import SQLAlchemyDB
from models.business.business_application_model import Business
from models.perf_ui_auto.perf_case_model import PerfUiAutoCases
from dals.db.business.business_db import business_db
from schemas.request.perf_ui_auto.perf_case_req import CaseListParams
from sqlalchemy import func


class PerfCaseDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfCaseDB, self).__init__()

    def update_case_list_db(self, new_case_info_list):
        """
    更新用例
    :param new_case_info_list: 新的用例信息列表
        """
        # 先拉取当前表里用例，和最新拉取仓库的用例比较，分辨要更新的，要新增的和要删除的
        update_list = list()
        add_list = list()
        delete_list = list()
        model_class = [PerfUiAutoCases]
        order = [PerfUiAutoCases.create_time]
        full_match_total, full_match_results = self.query(model_class, order_by=order)

        old_cases_list = list()
        for case in full_match_results:
            case_dict = {k: v for k, v in case.__dict__.items() if not k.startswith('_')}
            # 如果老表中某记录不在新表中，需删除
            if case_dict['dir'] not in [new_case['dir'] for new_case in new_case_info_list]:
                delete_list.append(case_dict)
            old_cases_list.append(case_dict)

        for new_case in new_case_info_list:
            # 如果新表中的记录不在旧表，需新增
            if new_case['dir'] not in [old_case['dir'] for old_case in old_cases_list]:
                add_list.append(new_case)
            # 如果新表中的记录在旧表，可能需更新
            else:
                update_list.append(new_case)

        # 更新已存在的用例
        if update_list:
            self.update_more_by_keys(
                model=PerfUiAutoCases,
                data_list=update_list,
                key_fields=['dir']  # 使用dir字段作为匹配键
            )

        # 添加新用例
        if add_list:
            model_list = list()
            for case in add_list:
                item = PerfUiAutoCases(**case)
                model_list.append(item)
            # print(case["case_name"]for case in add_list)
            self.add_more(model_list)

        # 删除不存在的用例
        if delete_list:
            model2 = PerfUiAutoCases
            conditions2 = [PerfUiAutoCases.dir.in_([item['dir'] for item in delete_list])]
            delete_query = self.query(model2, filters=conditions2, return_query=True)
            self.delete_more(delete_query)

    def get_business_id_by_dir(self, dir_name):
        """
    通过路径中的名称获取business id
    :param dir_name: 目录名
        """
        model_class = [Business]
        conditions = [Business.business_dir == dir_name]
        results = self.query(model=model_class, filters=conditions, return_first=True)
        return results

    def get_case_list_by_page(self, filters_param: CaseListParams, page, page_size):
        business_id = filters_param.business_id
        platform_list = filters_param.platform
        conditions = list()
        if business_id != -1:
            conditions.append(PerfUiAutoCases.business_id == business_id)
        if platform_list != [-1]:
            for platform in platform_list:
                conditions.append(func.LOCATE(str(platform),PerfUiAutoCases.platform) > 0)
        offset = (page - 1) * page_size
        if offset < 0:
            offset = 0
        model_class = [PerfUiAutoCases]
        order = [PerfUiAutoCases.create_time]
        # if business_id == -1 and platform_str != -1:
        full_match_total, full_match_results = self.query(model_class,
                                                          filters=conditions,order_by=order, offset=offset, limit=page_size)
        if full_match_total > 0:
            count = 0
            cases_list = list()
            for case in full_match_results:
                case_dict = {k: v for k, v in case.__dict__.items() if not k.startswith('_')}
                cases_list.append(case_dict)
                count+=1
            return count, cases_list
        else:
            return 0, []

    def get_case_detail(self, case_id: int):
        """
        获取用例详情
        :param case_id: 用例ID
        """
        model_class = [PerfUiAutoCases]
        conditions = [PerfUiAutoCases.case_id == case_id]
        result = self.query(model=model_class, filters=conditions, return_first=True)
        if result:
            case_dict = {k: v for k, v in result.__dict__.items() if not k.startswith('_')}
            return case_dict
        return None

    def get_cases_by_ids(self, case_ids: List[int]):
        """
        根据用例ID列表批量获取用例详情
        :param case_ids: 用例ID列表
        :return: 用例详情列表
        """
        if not case_ids:
            return []

        try:
            model_class = [PerfUiAutoCases]
            conditions = [PerfUiAutoCases.case_id.in_(case_ids)]
            _, results = self.query(model=model_class, filters=conditions)

            if results:
                cases_list = []
                for case in results:
                    case_dict = {k: v for k, v in case.__dict__.items() if not k.startswith('_')}
                    cases_list.append(case_dict)
                return cases_list
            return []
        except Exception as e:
            from utils.common.bytelog import byte_log
            byte_log.error(f"Failed to get cases by ids: {str(e)}")
            return []


perf_case_db = PerfCaseDB()
