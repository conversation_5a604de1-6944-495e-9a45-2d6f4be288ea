from copy import deepcopy
import json
from defines import perf_ui_auto_define
from models.perf_ui_auto.perf_app_model import PerfUiAutoAppGroup, PerfUiAutoApp
from models.perf_ui_auto.perf_case_model import PerfUiAutoCases
from models.perf_ui_auto.perf_client_model import PerfUiAutoClient
from models.perf_ui_auto.perf_device_model import PerfUiAutoDevice
from models.perf_ui_auto.perf_config_model import PerformanceConfig
from models.perf_ui_auto.perf_experiment_model import ExperimentModel
from models.perf_ui_auto.perf_account_model import PerfUiAutoAccount
from service.perf_ui_auto import perf_task_service
from dals.db.base import SQLAlchemyDB
from models.business.business_application_model import Business
from models.perf_ui_auto.perf_task_model import PerfCaseRunDetail
from schemas.page import PageSchema
from schemas.request.perf_ui_auto.perf_task_req import (
    UploadPerfCaseRunDetailParams, 
    PerfTaskListParams,
    UpdateSubTaskStatusParams, 
    UpdateTaskStatusParams,
    CreatePerfTaskParams, 
    UpdatePerfTaskParams
)
from schemas.response.perf_ui_auto.perf_task_res import (
    AppExecutionStatsItem,
    CaseExecutionStatsItem,
    CaseExecutionStatsListRes
)
from models.perf_ui_auto.perf_task_model import PerfUiAutoTask, PerfUiAutoSubTask
from utils.common.bytelog import byte_log
from sqlalchemy import func, case, and_

class PerfTaskDB(SQLAlchemyDB):

    def upload_perf_case_run_detail(self, params: UploadPerfCaseRunDetailParams):
        """
        上传用例运行详情
        @param params:
        @return:
        """
        model = PerfCaseRunDetail
        conditions = [model.id == params.id]
        params_dict = params.model_dump(exclude_none=True)
        return_first = self.query(model, filters=conditions, return_first=True)
        if return_first:
            self.update_one(return_first, params_dict)
            item = params
        else:
            item = PerfCaseRunDetail(**params_dict)
            self.add_one(item)
        return item

    def get_sub_task_case_run_detail(self, page: PageSchema, sub_task_id: int):
        """
        获取用例运行详情
        @param params:
        @return:
        """
        model = [PerfCaseRunDetail, PerfUiAutoCases, PerfUiAutoApp, PerfUiAutoDevice]
        joins = [(PerfUiAutoCases, PerfCaseRunDetail.case_id == PerfUiAutoCases.case_id),
                 (PerfUiAutoApp, PerfCaseRunDetail.app_id == PerfUiAutoApp.id),
                 (PerfUiAutoDevice, PerfCaseRunDetail.device_id == PerfUiAutoDevice.id)]
        conditions = [PerfCaseRunDetail.sub_task_id == sub_task_id]
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        order = [PerfCaseRunDetail.id.desc()]

        total, results = self.query(model, joins=joins, filters=conditions, order_by=order, offset=offset,
                                    limit=limit)

        return total, results
    def get_task_list(self, page: PageSchema, params: PerfTaskListParams):
        model = [PerfUiAutoTask.id, PerfUiAutoTask.name, PerfUiAutoTask.business_id, PerfUiAutoTask.client_id,PerfUiAutoTask.config_id, PerfUiAutoTask.experiment_id,
                 PerfUiAutoTask.owner, PerfUiAutoTask.type, PerfUiAutoTask.perf_tool_type, PerfUiAutoTask.status, PerfUiAutoTask.result,
                 PerfUiAutoTask.duration, PerfUiAutoTask.start_time, PerfUiAutoTask.end_time, PerfUiAutoTask.create_time,
                 PerfUiAutoTask.update_time, Business.business_name]
        joins = [(Business, PerfUiAutoTask.business_id == Business.id)]
        conditions = []
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        conditions_dict = {
            "business_id": PerfUiAutoTask.business_id,
            "client_id": PerfUiAutoTask.client_id,
            "status": PerfUiAutoTask.status,
            "perf_tool_type": PerfUiAutoTask.perf_tool_type,
            "task_name": PerfUiAutoTask.name,
            "owner": PerfUiAutoTask.owner
        }
        for param_key, param_value in params:
            if param_value == None:
                continue
            for condition_key, condition_value in conditions_dict.items():
                if condition_key in("task_name","owner")  and param_key == condition_key:
                    conditions.append(condition_value.like(f"%{param_value}%"))
                elif isinstance(param_value, list):
                    # 添加筛选项多选
                    if param_key == condition_key:
                        conditions.append(condition_value.in_(param_value))
                else:
                    # 添加筛选项单选
                    if param_key == condition_key:
                        conditions.append(condition_value == param_value)
        order = [PerfUiAutoTask.id.desc()]
        total, results = self.query(model, joins=joins, filters=conditions, order_by=order, offset=offset,
                                    limit=limit)
        return total, results
    def get_task(self, task_id):
        try:
            model = PerfUiAutoTask
            conditions = [PerfUiAutoTask.id == task_id]
            result = self.query(model, filters=conditions, return_first=True)
            return result
        except Exception as e:
            byte_log.error(f"Failed to create task: {str(e)}")
            return None
    def get_task_all(self, task_id):
        try:
            model = [PerfUiAutoTask, Business, PerfUiAutoClient, PerformanceConfig, ExperimentModel]
            joins = [(Business, PerfUiAutoTask.business_id == Business.id),
                     (PerfUiAutoClient, PerfUiAutoTask.client_id == PerfUiAutoClient.id),
                     (PerformanceConfig, PerfUiAutoTask.config_id == PerformanceConfig.id)]
            left_joins = [(ExperimentModel, PerfUiAutoTask.experiment_id == ExperimentModel.id)]
            conditions = [PerfUiAutoTask.id == task_id]
            result = self.query(model, filters=conditions, joins=joins, left_joins=left_joins, return_first=True)
            return result
        except Exception as e:
            byte_log.error(f"Failed to create task: {str(e)}")
            return None

    def get_sub_tasks_by_task_id(self, task_id):
        try:
            model = PerfUiAutoSubTask
            conditions = [PerfUiAutoSubTask.task_id == task_id]
            order = [PerfUiAutoSubTask.id.asc()]
            _, results = self.query(model, filters=conditions, order_by=order)
            return results
        except Exception as e:
            byte_log.error(f"Failed to create task: {str(e)}")
            return None

    def get_sub_tasks_by_task_id_all(self, task_id):
        try:
            model = [PerfUiAutoSubTask, PerfUiAutoDevice, PerfUiAutoAppGroup, PerfUiAutoAccount]
            joins = [(PerfUiAutoDevice, PerfUiAutoSubTask.perf_device_id == PerfUiAutoDevice.id),
                     (PerfUiAutoAppGroup, PerfUiAutoSubTask.app_group_id == PerfUiAutoAppGroup.id)]
            left_joins = [(PerfUiAutoAccount, PerfUiAutoSubTask.perf_account_id == PerfUiAutoAccount.id)]
            conditions = [PerfUiAutoSubTask.task_id == task_id]
            order = [PerfUiAutoSubTask.id.asc()]
            _, results = self.query(model, joins=joins, left_joins=left_joins, filters=conditions, order_by=order)
            return results
        except Exception as e:
            byte_log.error(f"Failed to create task: {str(e)}")
            return None

    def create_task(self, params: CreatePerfTaskParams):
        try:
            # 数据处理
            data = vars(deepcopy(params))
            data.pop("sub_tasks")

            item = PerfUiAutoTask(**data)
            self.add_one(item)
            # 插入失败
            if not item.id:
                return None
            # 返回插入以后的数据
            return item
        except Exception as e:
            byte_log.error(f"Failed to create task: {str(e)}")
            return None

    def create_sub_tasks(self, params: CreatePerfTaskParams, task_item: PerfUiAutoTask):
        try:
            model_list = []
            for sub_task in params.sub_tasks:
                data = vars(deepcopy(sub_task))
                data["task_id"] = task_item.id
                item = PerfUiAutoSubTask(**data)
                model_list.append(item)
            self.add_more(model_list)
            return model_list
        except Exception as e:
            byte_log.error(f"Failed to create sub task: {str(e)}")
            return None

    def update_task(self, params: UpdatePerfTaskParams):
        try:
            model = PerfUiAutoTask
            conditions = [PerfUiAutoTask.id == params.id]
            results = self.query(model, filters=conditions, return_first=True)
            if results:
                # 数据处理
                data = vars(deepcopy(params))
                data.pop("sub_tasks")
                return self.update_one(results, data)
            else:
                return None
        except Exception as e:
            byte_log.error(f"Failed to update task: {str(e)}")
            return None

    def update_sub_tasks(self, params: UpdatePerfTaskParams):
        try:
            add_model_list = []
            update_model_list = []

            model1 = PerfUiAutoSubTask
            conditions1 = [PerfUiAutoSubTask.task_id == params.id]
            _, sub_task_in_db_list = self.query(model1, filters=conditions1)

            for sub_task in params.sub_tasks:
                # sub task为新增
                if sub_task.id is None:
                    data = vars(deepcopy(sub_task))
                    data["task_id"] = params.id
                    add_model_list.append(PerfUiAutoSubTask(**data))
                # sub task为修改
                else:
                    data = vars(deepcopy(sub_task))
                    update_model_list.append(data)

            # 处理删除的子任务
            delete_model_list = perf_task_service.find_delete_list(sub_task_in_db_list, update_model_list)

            # 新增
            if add_model_list:
                add_result_list = self.add_more(add_model_list)
            else:
                add_result_list = []
                
            # 变更
            if update_model_list:
                self.update_more(PerfUiAutoSubTask, update_model_list)
                
            # 删除
            if delete_model_list:
                model2 = PerfUiAutoSubTask
                conditions2 = [PerfUiAutoSubTask.id.in_([item.id for item in delete_model_list])]
                delete_query = self.query(model2, filters=conditions2, return_query=True)
                self.delete_more(delete_query)

            return update_model_list + add_result_list
        except Exception as e:
            byte_log.error(f"Failed to update sub tasks: {str(e)}")
            return None

    def copy_task(self, task_id: int, operator: str):
        try:
            # 根据 task_id 查询 task
            model = PerfUiAutoTask
            conditions = [PerfUiAutoTask.id == task_id]
            results = self.query(model, filters=conditions, return_first=True)
            if results:
                # 数据处理
                data = vars(deepcopy(results))
                data.pop("_sa_instance_state")
                data.pop("id")
                data.pop("status")
                data.pop("result")
                data.pop("create_time")
                data.pop("update_time")
                data.pop("start_time")
                data.pop("end_time")
                data.pop("duration")
                data["owner"] = operator
                data["name"] = data["name"] + ' - 副本'

                item = PerfUiAutoTask(**data)
                self.add_one(item)
                # 插入失败
                if not item.id:
                    return None
                # 返回插入以后的数据
                return item
        except Exception as e:
            byte_log.error(f"Failed to copy task: {str(e)}")
            return None

    def copy_sub_tasks(self, origin_task_id: int, new_task_id: int):
        try:
            # 查询待复制的子任务
            model = PerfUiAutoSubTask
            conditions = [PerfUiAutoSubTask.task_id == origin_task_id]
            _, sub_task_in_db_list = self.query(model, filters=conditions)

            model_list = []
            for sub_task in sub_task_in_db_list:
                # 数据处理
                data = vars(deepcopy(sub_task))
                data["task_id"] = new_task_id
                data.pop("_sa_instance_state")
                data.pop("id")
                data.pop("status")
                data.pop("result")
                data.pop("create_time")
                data.pop("update_time")
                data.pop("start_time")
                data.pop("end_time")
                data.pop("duration")
                item = PerfUiAutoSubTask(**data)
                model_list.append(item)
            self.add_more(model_list)
            return model_list
        except Exception as e:
            byte_log.error(f"Failed to create sub task: {str(e)}")
            return None

    def delete_task(self, task_id: int):
        try:
            model = PerfUiAutoTask
            conditions = [PerfUiAutoTask.id == task_id]
            delete_query = self.query(model, filters=conditions, return_query=True)
            self.delete_more(delete_query)
        except Exception as e:
            byte_log.error(f"Failed to delete task: {str(e)}")
            return None

    def delete_sub_tasks(self, task_id: int):
        try:
            model = PerfUiAutoSubTask
            conditions = [PerfUiAutoSubTask.task_id == task_id,
                          PerfUiAutoSubTask.status == perf_ui_auto_define.TASK_STATUS_PENDING]
            delete_query = self.query(model, filters=conditions, return_query=True)
            self.delete_more(delete_query)
        except Exception as e:
            byte_log.error(f"Failed to delete sub tasks: {str(e)}")
            return None
        
    def update_sub_task_status_by_id(self, params: UpdateSubTaskStatusParams):
        """更新子任务执行状态"""
        try:
            model = PerfUiAutoSubTask
            conditions = [PerfUiAutoSubTask.id == params.id]
            result = self.query(model, filters=conditions, return_first=True)

            if not result:
                raise Exception("子任务不存在")
            
            # 使用 model_dump 来获取所有非空字段
            update_dict = params.model_dump(exclude_unset=True)
            
            # 如果设置了结束时间，计算持续时间
            if params.end_time and result.start_time:
                duration = int((params.end_time - result.start_time).total_seconds())
                update_dict['duration'] = duration
            
            # 如果状态是失败相关状态且没有提供错误信息，设置默认值
            if params.status in [perf_ui_auto_define.TASK_STATUS_FAILED, perf_ui_auto_define.TASK_STATUS_TIMEOUT] and not params.error_code:
                update_dict['error_code'] = -1
                update_dict['error_title'] = '未知错误'
                update_dict['error_detail'] = '任务执行失败，未提供具体错误信息'
            
            return self.update_one(result, update_dict)
        except Exception as e:
            byte_log.error(f"Failed to update_sub_task_status_by_id: {str(e)}")
            return None

    def update_task_status_by_id(self, params: UpdateTaskStatusParams):
        """更新任务状态"""
        try:
            model = PerfUiAutoTask
            conditions = [PerfUiAutoTask.id == params.id]
            result = self.query(model, filters=conditions, return_first=True)

            if not result:
                raise Exception("任务不存在")
            update_dict = params.model_dump(exclude_unset=True)
            return self.update_one(result, update_dict)
        except Exception as e:
            byte_log.error(f"Failed to update_task_status_by_id: {str(e)}")
            return None
        
    def get_case_execution_stats(self, sub_task_id: int) -> CaseExecutionStatsListRes:
        """获取子任务用例执行统计"""
        try:
            # 获取子任务和应用组信息
            sub_task = self.query(PerfUiAutoSubTask, filters=[PerfUiAutoSubTask.id == sub_task_id], return_first=True)
            if not sub_task:
                return CaseExecutionStatsListRes()
            # 获取主任务，判断类型
            task = self.query(PerfUiAutoTask, filters=[PerfUiAutoTask.id == sub_task.task_id], return_first=True)
            if not task:
                return CaseExecutionStatsListRes()
            byte_log.info(f"[get_case_execution_stats] sub_task_id={sub_task_id}, task_id={sub_task.task_id}, task.type={task.type} ({type(task.type)})")
            if int(task.type) == int(perf_ui_auto_define.TASK_TYPE_VERSION_REGRESSION):
                # ... existing code ...
                app_group = self.query(PerfUiAutoAppGroup, filters=[PerfUiAutoAppGroup.id == sub_task.app_group_id], return_first=True)
                if not app_group:
                    return CaseExecutionStatsListRes()
                try:
                    app_id_list = json.loads(
                        app_group.android_perf_app_id_list if sub_task.platform == perf_ui_auto_define.PLATFORM_ANDROID 
                        else app_group.ios_perf_app_id_list if sub_task.platform == perf_ui_auto_define.PLATFORM_IOS
                        else "[]"
                    )
                except json.JSONDecodeError:
                    return CaseExecutionStatsListRes()
                if not app_id_list:
                    return CaseExecutionStatsListRes()
                case_ids = sub_task.case_id_list
                if not case_ids:
                    return CaseExecutionStatsListRes()
                model = [
                    PerfCaseRunDetail.case_id.label('case_id'),
                    PerfCaseRunDetail.app_id.label('app_id'), 
                    func.count(PerfCaseRunDetail.id).label('total_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_SUCCESS, 1), else_=0)).label('success_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_FAILED, 1), else_=0)).label('failed_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_TIMEOUT, 1), else_=0)).label('timeout_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_CANCELED, 1), else_=0)).label('canceled_count')
                ]
                _, results = self.query(
                    model, 
                    filters=[PerfCaseRunDetail.sub_task_id == sub_task_id],
                    group_by=[PerfCaseRunDetail.case_id, PerfCaseRunDetail.app_id],
                    order_by=[PerfCaseRunDetail.case_id, PerfCaseRunDetail.app_id]
                )
                executed_cases = {}
                for row in (results or []):
                    case_id = int(row.case_id)
                    app_id = int(row.app_id)
                    executed_cases[(case_id, app_id)] = {
                        'total_count': int(row.total_count),
                        'success_count': int(row.success_count),
                        'failed_count': int(row.failed_count),
                        'timeout_count': int(row.timeout_count),
                        'canceled_count': int(row.canceled_count)
                    }
                group_stats = {int(app_id): [] for app_id in app_id_list}
                for app_id in app_id_list:
                    app_id = int(app_id)
                    for case_id in case_ids:
                        stats = executed_cases.get((case_id, app_id))
                        if stats:
                            retry_count = max(0, sub_task.case_run_count - stats['success_count'])
                            case_stat = CaseExecutionStatsItem(
                                case_id=case_id,
                                total_count=stats['total_count'],
                                success_count=stats['success_count'],
                                failed_count=stats['failed_count'],
                                timeout_count=stats['timeout_count'],
                                canceled_count=stats['canceled_count'],
                                retry_count=retry_count
                            )
                        else:
                            case_stat = CaseExecutionStatsItem(
                                case_id=case_id,
                                total_count=0,
                                success_count=0,
                                failed_count=0,
                                timeout_count=0,
                                canceled_count=0,
                                retry_count=sub_task.case_run_count
                            )
                        group_stats[app_id].append(case_stat)
                group_stats_list = [AppExecutionStatsItem(app_id=app_id, cases=cases) for app_id, cases in group_stats.items()]
                total_cases = len(case_ids)
                total_executions = sum(sum(case.total_count for case in item.cases) for item in group_stats_list)
                total_success = sum(sum(case.success_count for case in item.cases) for item in group_stats_list)
                success_rate = total_success / total_executions if total_executions > 0 else 0.0
                return CaseExecutionStatsListRes(
                    total_cases=total_cases,
                    total_executions=total_executions,
                    success_rate=success_rate,
                    group_stats=group_stats_list
                )
            elif task.type == perf_ui_auto_define.TASK_TYPE_LIBRA_EXPERIMENT:
                # Libra实验，按version_type分组
                case_ids = sub_task.case_id_list
                if not case_ids:
                    return CaseExecutionStatsListRes()
                model = [
                    PerfCaseRunDetail.case_id.label('case_id'),
                    PerfCaseRunDetail.version_type.label('version_type'),
                    func.count(PerfCaseRunDetail.id).label('total_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_SUCCESS, 1), else_=0)).label('success_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_FAILED, 1), else_=0)).label('failed_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_TIMEOUT, 1), else_=0)).label('timeout_count'),
                    func.sum(case((PerfCaseRunDetail.status == perf_ui_auto_define.CASE_RUN_STATUS_CANCELED, 1), else_=0)).label('canceled_count')
                ]
                _, results = self.query(
                    model,
                    filters=[PerfCaseRunDetail.sub_task_id == sub_task_id, PerfCaseRunDetail.version_type.in_([perf_ui_auto_define.VERSION_TYPE_EXPERIMENT, perf_ui_auto_define.VERSION_TYPE_CONTROL])],
                    group_by=[PerfCaseRunDetail.case_id, PerfCaseRunDetail.version_type],
                    order_by=[PerfCaseRunDetail.case_id, PerfCaseRunDetail.version_type]
                )
                executed_cases = {}
                for row in (results or []):
                    case_id = int(row.case_id)
                    version_type = int(row.version_type)
                    executed_cases[(case_id, version_type)] = {
                        'total_count': int(row.total_count),
                        'success_count': int(row.success_count),
                        'failed_count': int(row.failed_count),
                        'timeout_count': int(row.timeout_count),
                        'canceled_count': int(row.canceled_count)
                    }
                version_type_list = [perf_ui_auto_define.VERSION_TYPE_EXPERIMENT, perf_ui_auto_define.VERSION_TYPE_CONTROL]
                group_stats = {vt: [] for vt in version_type_list}
                for vt in version_type_list:
                    for case_id in case_ids:
                        stats = executed_cases.get((case_id, vt))
                        if stats:
                            retry_count = max(0, sub_task.case_run_count - stats['success_count'])
                            case_stat = CaseExecutionStatsItem(
                                case_id=case_id,
                                total_count=stats['total_count'],
                                success_count=stats['success_count'],
                                failed_count=stats['failed_count'],
                                timeout_count=stats['timeout_count'],
                                canceled_count=stats['canceled_count'],
                                retry_count=retry_count
                            )
                        else:
                            case_stat = CaseExecutionStatsItem(
                                case_id=case_id,
                                total_count=0,
                                success_count=0,
                                failed_count=0,
                                timeout_count=0,
                                canceled_count=0,
                                retry_count=sub_task.case_run_count
                            )
                        group_stats[vt].append(case_stat)
                group_stats_list = [AppExecutionStatsItem(version_type=vt, cases=cases) for vt, cases in group_stats.items()]
                total_cases = len(case_ids)
                total_executions = sum(sum(case.total_count for case in item.cases) for item in group_stats_list)
                total_success = sum(sum(case.success_count for case in item.cases) for item in group_stats_list)
                success_rate = total_success / total_executions if total_executions > 0 else 0.0
                return CaseExecutionStatsListRes(
                    total_cases=total_cases,
                    total_executions=total_executions,
                    success_rate=success_rate,
                    group_stats=group_stats_list
                )
            else:
                return CaseExecutionStatsListRes()
        except Exception as e:
            byte_log.error(f"获取用例执行统计失败: {str(e)}")
            return CaseExecutionStatsListRes()

    def search_tasks_by_task_name(self, task_name:str):
        """
        根据任务名称搜索任务,模糊搜索。暂时不用
        """
        model = [PerfUiAutoTask.id, PerfUiAutoTask.name, PerfUiAutoTask.business_id, PerfUiAutoTask.client_id,
                 PerfUiAutoTask.owner, PerfUiAutoTask.type, PerfUiAutoTask.status, PerfUiAutoTask.result,
                 PerfUiAutoTask.duration, PerfUiAutoTask.start_time, PerfUiAutoTask.end_time,
                 PerfUiAutoTask.create_time,
                 PerfUiAutoTask.update_time, Business.business_name]
        conditions = []

        if task_name:
            full_match_total, full_match_results = self.query(model, filters=[PerfUiAutoTask.name == task_name])
            partial_match_total, partial_match_results = self.query(model, filters=[PerfUiAutoTask.name.like(f"%{task_name}%")])
            results = list(full_match_results) + [i for i in partial_match_results if i not in full_match_results]
            total = full_match_total + partial_match_total
            return total, [i[0] for i in results]
        total, results = self.query(model, filters_or=conditions)
        return total, [i[0] for i in results]


perf_task_db = PerfTaskDB()
