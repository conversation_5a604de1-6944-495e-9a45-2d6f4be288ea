'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/dals/db/perf_ui_auto/perf_data_db.py
Description: 性能数据数据库操作
'''
import decimal
from typing import Union, List, Dict, Any, Tuple, Optional

from dals.db.base import SQLAlchemyDB
from defines.perf_ui_auto_define import CASE_RUN_STATUS_SUCCESS
from models.perf_ui_auto.perf_app_model import PerfUiAutoApp
from models.perf_ui_auto.perf_case_model import PerfUiAutoCases
from models.perf_ui_auto.perf_data_model import PerfUIAutoData
from models.perf_ui_auto.perf_task_model import PerfUiAutoTask, PerfUiAutoSubTask, PerfCaseRunDetail
from schemas.page import PageSchema
from schemas.request.perf_ui_auto.perf_data_req import PerfDataIn, PerfDataTosUrlParams

def get_decimal_to_float(value: Any) -> Any:
    """
    将decimal类型转换为float类型
    @param value: 任意值
    @return: 如果是decimal类型则转换为float，否则原样返回
    """
    if isinstance(value, decimal.Decimal):
        return float(value)
    return value

class PerfDataDB(SQLAlchemyDB):
    def __init__(self):
        super(PerfDataDB, self).__init__()

    def upload_perf_data(self, params: PerfDataIn) -> PerfUIAutoData:
        """
        添加性能数据
        @return: PerfUIAutoData
        """
        model = PerfUIAutoData
        conditions = [model.id == params.id] if params.id else None
        params_dict = params.model_dump(exclude_none=True)
        
        if conditions:
            return_first = self.query(model, filters=conditions, return_first=True)
            if return_first:
                return self.update_one(return_first, params_dict)
        
        item = PerfUIAutoData(**params_dict)
        self.add_one(item)
        return item

    def get_data_by_run_id(self, run_id: int) -> List[Dict[str, Any]]:
        """
        根据run_id获取性能数据
        @param run_id: 用例运行ID
        @return: List[Dict]
        """
        model = PerfUIAutoData
        conditions = [
            model.case_run_detail_id == run_id,
            model.is_avg == True
        ]
        _, items = self.query(model, filters=conditions)
        return [self._convert_data_to_dict(item) for item in items]

    def get_data_by_run_id_list(self, run_id_list: List[int]) -> List[Dict[str, Any]]:
        """
        根据run_id列表获取性能数据
        @param run_id_list: 用例运行ID列表
        @return: List[Dict]
        """
        model = PerfUIAutoData
        conditions = [model.case_run_detail_id.in_(run_id_list)]
        _, items = self.query(model, filters=conditions)
        return [self._convert_data_to_dict(item) for item in items if item.is_avg]

    def get_run_id_by_subtask(self, subtask_id: int) -> List[Dict[str, Any]]:
        """
        通过子任务id查询runid
        @param subtask_id: 子任务ID
        @return: List[Dict]
        """
        model = PerfCaseRunDetail
        conditions = [model.sub_task_id == subtask_id]
        _, items = self.query(model, filters=conditions)
        return [{'id': item.id, 'case_id': item.case_id} for item in items]

    def get_sub_task_by_task(self, task_id: int) -> List[Dict[str, Any]]:
        """
        获取任务下的子任务列表
        @param task_id: 任务ID
        @return: List[Dict]
        """
        model = PerfUiAutoSubTask
        conditions = [model.task_id == task_id]
        order = [PerfUiAutoSubTask.create_time]
        _, items = self.query(model, filters=conditions, order_by=order)
        return [{'id': item.id, 'name': item.name} for item in items]

    def get_data_by_task(self, task_id: int) -> List[Dict[str, Any]]:
        """
        获取任务的性能数据
        @param task_id: 任务ID
        @return: List[Dict]
        """
        model_class = [
            PerfUIAutoData,
            PerfCaseRunDetail.id,
            PerfUiAutoSubTask.id,
            PerfUiAutoSubTask.name,
            PerfUiAutoSubTask.platform,
            PerfUiAutoSubTask.task_id,
            PerfCaseRunDetail.case_id,
            PerfCaseRunDetail.app_id,
            PerfUiAutoApp.app_type,
            PerfCaseRunDetail.version_type
        ]
        joins = [
            (PerfCaseRunDetail, PerfUIAutoData.case_run_detail_id == PerfCaseRunDetail.id),
            (PerfUiAutoSubTask, PerfCaseRunDetail.sub_task_id == PerfUiAutoSubTask.id),
            (PerfUiAutoApp, PerfCaseRunDetail.app_id == PerfUiAutoApp.id)
        ]
        conditions = [PerfUiAutoSubTask.task_id == task_id]
        order = [PerfCaseRunDetail.id.desc()]
        
        _, results = self.query(model_class, filters=conditions, joins=joins, order_by=order)
        
        # 获取用例信息
        _, cases = self.query([PerfUiAutoCases])
        case_dict = {case.case_id: case.title for case in cases}
        
        data_list = []
        for result in results:
            if result[0].is_avg:
                data_dict = self._convert_data_to_dict(result[0])
                data_dict.update({
                    'subtask_id': result[2],
                    'subtask_name': result[3],
                    'platform': result[4],
                    'task_id': result[5],
                    'case_id': result[6],
                    'app_id': result[7],
                    'app_type': result[8],
                    'version_type': result[9],
                    'case_title': case_dict.get(result[6], '暂未找到用例')
                })
                data_list.append(data_dict)
        
        return sorted(data_list, key=lambda x: (x['case_id'], x['app_type']))

    def get_perf_data_tos_url(self, params: PerfDataTosUrlParams) -> List[Dict[str, Any]]:
        """
        获取性能数据TOS URL
        @param params: 请求参数
        @return: List[Dict]
        """
        model = [PerfCaseRunDetail, PerfUiAutoApp.app_type]
        joins = [(PerfUiAutoApp, PerfCaseRunDetail.app_id == PerfUiAutoApp.id)]
        conditions = [
            PerfCaseRunDetail.sub_task_id == params.sub_task_id,
            PerfCaseRunDetail.case_id == params.case_id,
            PerfCaseRunDetail.status == CASE_RUN_STATUS_SUCCESS,
            PerfCaseRunDetail.perf_data_tos_urls != None
        ]
        order = [PerfCaseRunDetail.create_time.asc()]
        _, results = self.query(model, filters=conditions, joins=joins, order_by=order, limit=6)
        
        return [
            {**self._convert_data_to_dict(result[0]), 'app_type': result[1]}
            for result in results
        ]

    def _convert_data_to_dict(self, data: Any) -> Dict[str, Any]:
        """
        将数据对象转换为字典,处理decimal类型
        """
        data_dict = {
            k: get_decimal_to_float(v)
            for k, v in data.__dict__.items()
            if not k.startswith('_')
        }
        return data_dict

perf_data_db = PerfDataDB()
