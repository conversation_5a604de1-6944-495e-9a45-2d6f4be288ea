from sqlalchemy import func
from sqlalchemy.orm import aliased

from dals.db.base import SQLAlchemyDB
from models.ci_daily_model import TestTask, TestPlatform, TestCaseDetail
from schemas.page import PageSchema
from schemas.request.test_manager import ci_daily_req
from utils.common.time_util import dt_str_convert_dt


class CiDailyDB(SQLAlchemyDB):
    def __init__(self):
        super(CiDailyDB, self).__init__()

    def get_case_list(self, page: PageSchema, params: ci_daily_req.GetCaseListParams):
        """
        获取用例列表
        @param page:
        @param params:
        @return:
        """
        model = [
            TestCaseDetail.id, TestTask.pipeline_type, TestTask.pipeline_create_time, TestTask.branch,
            TestPlatform.platform, TestCaseDetail.case_id, TestCaseDetail.module, TestCaseDetail.qa_owner,
            TestCaseDetail.result_code, TestCaseDetail.result_message, TestCaseDetail.fail_reason_type,
            TestCaseDetail.fail_reason_str, TestCaseDetail.improvement_measure_str, TestCaseDetail.is_closed,
            TestCaseDetail.meego_bug_url, TestCaseDetail.attribution_type
        ]
        joins = [TestTask.test_platforms, TestPlatform.test_case_details]
        conditions = []
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        conditions_dict = {
            "pipeline_create_time": TestTask.pipeline_create_time,
            "branch": TestTask.branch,
            "pipeline_type": TestTask.pipeline_type,
            "platform": TestPlatform.platform,
            "case_id": TestCaseDetail.case_id,
            "module": TestCaseDetail.module,
            "result_code": TestCaseDetail.result_code,
            "qa_owner": TestCaseDetail.qa_owner,
            "fail_reason_type": TestCaseDetail.fail_reason_type,
            "is_closed": TestCaseDetail.is_closed,
            "attribution_type": TestCaseDetail.attribution_type
        }
        for param_key, param_value in params:
            if not param_value:
                continue
            for condition_key, condition_value in conditions_dict.items():
                if isinstance(param_value, list):
                    # 添加筛选项多选
                    if param_key == condition_key:
                        conditions.append(condition_value.in_(param_value))
                        # 添加时间端筛选
                    elif param_key == "time" and condition_key == "pipeline_create_time":
                        conditions.extend([
                            condition_value > dt_str_convert_dt(param_value[0]),
                            condition_value < dt_str_convert_dt(param_value[1])
                        ])
                else:
                    # 用例ID模糊搜索
                    if param_key == "case_id" and condition_key == "case_id":
                        conditions.append(condition_value.like(f"%{param_value}%"))
                    # 添加筛选项单选
                    elif param_key == condition_key:
                        conditions.append(condition_value == param_value)
        order = [TestPlatform.start_time.desc()]
        total, results = self.query(model, joins=joins, filters=conditions, order_by=order, offset=offset,
                                    limit=limit)
        return total, results

    def get_branch_list(self, page: PageSchema, branch: str):
        """
        获取分支列表
        @return:
        """
        model = [TestTask.branch.distinct()]
        conditions = []
        order = [TestTask.branch.desc()]
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        if branch:
            full_match_total, full_match_results = self.query(model, filters=[TestTask.branch == branch], order_by=order, offset=offset,
                                          limit=limit)
            partial_match_total, partial_match_results = self.query(model, filters=[TestTask.branch.like(f"%{branch}%")], order_by=order, offset=offset,
                                             limit=limit)
            results = list(full_match_results) + [i for i in partial_match_results if i not in full_match_results]
            total = full_match_total + partial_match_total
            return total, [i[0] for i in results]
        total, results = self.query(model, filters_or=conditions, order_by=order, offset=offset, limit=limit)
        return total, [i[0] for i in results]

    def get_result_code_list(self, page: PageSchema, result_code: int):
        """
        获取错误码
        @return:
        """
        model = [TestCaseDetail.result_code.distinct()]
        conditions = []
        order = [TestCaseDetail.result_code]
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        if result_code:
            full_match_total, full_match_results = self.query(model, filters=[TestCaseDetail.result_code == result_code],
                                                              order_by=order, offset=offset,
                                                              limit=limit)
            partial_match_total, partial_match_results = self.query(model,
                                                                    filters=[TestCaseDetail.result_code.like(f"%{result_code}%")],
                                                                    order_by=order, offset=offset,
                                                                    limit=limit)
            results = list(full_match_results) + [i for i in partial_match_results if i not in full_match_results]
            total = full_match_total + partial_match_total
            return total, [i[0] for i in results]
        total, results = self.query(model, filters=conditions, order_by=order, offset=offset, limit=limit)
        return total, [res[0] for res in results]

    def get_attribute_type_list(self, page: PageSchema):
        """
        获取归因类型
        @return:
        """
        model = [TestCaseDetail.attribution_type.distinct()]
        conditions = []
        order = [TestCaseDetail.attribution_type]
        offset = (page.page - 1) * page.page_size
        limit = page.page_size
        total, results = self.query(model, filters=conditions, order_by=order, offset=offset, limit=limit)
        return total, [res[0] for res in results]

    def get_case_detail(self, test_case_detail_id: int):
        """
        获取用例详情
        @param test_case_detail_id:
        @return:
        """
        model = [TestTask, TestPlatform, TestCaseDetail]
        joins = [TestTask.test_platforms, TestPlatform.test_case_details]
        conditions = [TestCaseDetail.id == test_case_detail_id]
        results = self.query(model, joins=joins, filters=conditions, return_first=True)
        return results

    def update_case_detail(self, params: ci_daily_req.UpdateCaseDetailParams):
        """
        更新用例详情
        @param params:
        @return:
        """
        model = TestCaseDetail
        conditions = [TestCaseDetail.id == params.id]
        results = self.query(model, filters=conditions, return_first=True)
        if results:
            self.update_one(results, params.model_dump())
        return params

    def del_repeat_case(self):
        """
        删除重复用例
        @return:
        """
        # 创建别名以便在子查询中引用TestCaseDetail表
        test_case_detail_alias = aliased(TestCaseDetail)
        subquery_model = [
            func.min(test_case_detail_alias.id).label('id'),
            test_case_detail_alias.start_time,
            func.count(test_case_detail_alias.start_time).label('count')
        ]
        subquery_group_by = test_case_detail_alias.start_time
        subquery_having = func.count(test_case_detail_alias.start_time) != 1
        # 子查询找出每个start_time出现的次数
        subquery = self.query(subquery_model, group_by=subquery_group_by, query_having=subquery_having,
                              return_sub_query=True)
        duplicates_model = TestCaseDetail
        conditions = [
            TestCaseDetail.start_time == subquery.c.start_time,
            TestCaseDetail.id != subquery.c.id
        ]
        model_query = self.query(duplicates_model, filters=conditions, return_query=True)
        duplicates = self.delete_more(model_query)
        return duplicates


ci_daily_db = CiDailyDB()

if __name__ == "__main__":
    ci_daily_db.del_repeat_case()
