from dals.db.base import SQLAlchemyDB
from schemas.page import PageSchema
from models.version_manager_list_model import Version
from models.version_manager_detail_model import VersionStage
from datetime import datetime
from sqlalchemy import select, func, desc, and_
from sqlalchemy.orm import aliased
from utils.common.bytelog import byte_log

class VersionManagerListDB(SQLAlchemyDB):
    def __init__(self):
        super(VersionManagerListDB, self).__init__()

    def get_list(self, page: PageSchema):
        """
        获取版本列表
        @return: tuple(total: int, items: list)
        """
        try:
            with self.get_db() as session:
                # 构建最新阶段子查询
                latest_stages = (
                    select(
                        VersionStage.version_id,
                        func.max(VersionStage.start_time).label('max_start_time')
                    )
                    .where(VersionStage.start_time < datetime.now())
                    .group_by(VersionStage.version_id)
                    .subquery()
                )

                # 获取最新阶段详情
                stage = aliased(VersionStage)
                current_stages = (
                    select(
                        stage.version_id,
                        stage.name.label('stage_name'),
                        stage.stage.label('stage_owner'),
                        stage.start_time
                    )
                    .join(
                        latest_stages,
                        and_(
                            stage.version_id == latest_stages.c.version_id,
                            stage.start_time == latest_stages.c.max_start_time
                        )
                    )
                    .subquery()
                )

                # 主查询
                query = (
                    select(
                        Version,
                        current_stages.c.stage_name,
                        current_stages.c.stage_owner,
                        current_stages.c.start_time.label('stage_start_time')
                    )
                    .outerjoin(
                        current_stages,
                        Version.id == current_stages.c.version_id
                    )
                    .order_by(desc(Version.start_time))
                )

                # 获取总数
                total = session.execute(
                    select(func.count()).select_from(Version)
                ).scalar() or 0

                # 分页查询
                results = session.execute(
                    query
                    .offset((page.page - 1) * page.page_size)
                    .limit(page.page_size)
                ).fetchall()

                # 处理结果
                items = []
                for row in results:
                    version = row[0]
                    item = {
                        'id': version.id,
                        'version_name': version.version_name,
                        'version_qa_owner': version.version_qa_owner,
                        'version_rd_owner': version.version_rd_owner,
                        'platforms': version.platforms,
                        'start_time': version.start_time,
                        'end_time': version.end_time,
                        'meego_epic_url': version.meego_epic_url,
                        'meego_version_url': version.meego_version_url,
                        'submit_to_test_doc_url': version.submit_to_test_doc_url,
                        'api_change_doc_url': version.api_change_doc_url,
                        'release_note_doc_url': version.release_note_doc_url,
                        'create_time': version.create_time,
                        'update_time': version.update_time,
                        'current_stage': {
                            'stage_name': row.stage_name,
                            'stage_owner': row.stage_owner,
                            'start_time': row.stage_start_time
                        } if row.stage_name else None
                    }
                    items.append(item)

                return total, items

        except Exception as e:
            byte_log.error(f"获取版本列表失败: {str(e)}")
            return 0, []


version_manager_list_db = VersionManagerListDB()

if __name__ == '__main__':
    pass