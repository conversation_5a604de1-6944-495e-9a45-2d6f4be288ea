from dals.db.base import SQLAlchemyDB
from models.version_manager_detail_model import *
from schemas.request.version_manager.version_manager_detail_req import *
from sqlalchemy import text

from utils.common.bytelog import byte_log
from utils.common.time_util import *

class VersionManagerDetailDB(SQLAlchemyDB):
    def __init__(self):
        super(VersionManagerDetailDB, self).__init__()

    def get_version_detail_by_id(self, version_id: int):
        """
        根据id获取版本详情
        @return:
        """
        model = [VersionDetail]
        conditions = [VersionDetail.id == version_id]
        result = self.query(model, filters=conditions, return_first=True)
        return result

    def get_stages_by_version_id(self, version_id: int):
        """
        根据id获取版本stage
        @return:
        """
        model = [VersionStage]
        conditions = [VersionStage.version_id == version_id]
        order = [VersionStage.stage_order]
        total, results = self.query(model, filters=conditions,order_by=order)
        return results
    def get_current_stage_by_version_id(self, version_id: int):
        """
        根据id获取版本当前所在stage
        @return:
        """
        current_time = get_current_time()
        model = [VersionStage]
        conditions = [VersionStage.version_id == version_id, VersionStage.start_time < current_time]
        order = [VersionStage.start_time.desc()]
        result = self.query(model, filters=conditions,order_by=order, return_first=True)
        return result

    def get_version_checklist_by_version_id_and_stage(self, version_id, stage):
        """
        根据版本id、阶段获取版本checklist
        @return:
        """
        model = [Checklist, VersionChecklist]
        # joins = [Checklist]
        conditions = [VersionChecklist.checklist_id == Checklist.id,
                      VersionChecklist.version_id == version_id,
                      Checklist.stage == stage]
        order = [Checklist.id]
        total, results = self.query(model, filters=conditions, order_by=order)
        return total, results

    def update_version_checklist_by_version_checklist_id(self, params: UpdateVersionChecklistParams):
        """
        根据版本checklist id对其进行更新
        @return:
        """
        model = [VersionChecklist]
        conditions = [VersionChecklist.id == params.id]
        result_query = self.query(model, filters=conditions, return_query=True)
        results = result_query.first()
        if results:
            if results.is_finished == params.is_finished and params.is_finished == True:
                return -2, '重复提交，任务已完成', None
            else:
                self.update_one(results, params.model_dump())

            model2 = [Checklist, VersionChecklist]
            conditions2 = [VersionChecklist.checklist_id == Checklist.id,
                          VersionChecklist.id == params.id]
            results2 = self.query(model2, filters=conditions2, return_first=True)
            return 200, '', results2
        return -1, f'未查询到符合条件的checkist, id={params.id}', None

    def get_todo_checklist_before_today(self):
        sql = 'select ' \
            'version_manager_version.id as version_id, ' \
            'version_manager_version.version_name as version_name, ' \
            'version_manager_version.version_qa_owner as version_qa_owner, ' \
            'version_manager_version_checklist.id as version_checklist_id, ' \
            'version_manager_version_checklist.start_time as version_checklist_start_time, ' \
            'version_manager_version_stage.name as stage_name, ' \
            'version_manager_checklist.id, ' \
            'version_manager_checklist.name, ' \
            'version_manager_checklist.checklist_desc, ' \
            'version_manager_checklist.stage, ' \
            'version_manager_checklist.remind_type, ' \
            'version_manager_checklist.remind_days, ' \
            'version_manager_checklist.remind_time, ' \
            'version_manager_checklist.reminder_type, ' \
            'version_manager_checklist.reminder, ' \
            'version_manager_checklist.create_time, ' \
            'version_manager_checklist.update_time from ' \
            'version_manager_version,version_manager_checklist, ' \
            'version_manager_version_stage,version_manager_version_checklist ' \
            'where version_manager_version_stage.start_time < CURRENT_TIME ' \
            'and version_manager_version.id = version_manager_version_stage.version_id ' \
            'and version_manager_version.id = version_manager_version_checklist.version_id ' \
            'and version_manager_checklist.stage = version_manager_version_stage.stage ' \
            'and version_manager_checklist.id = version_manager_version_checklist.checklist_id ' \
            'and version_manager_version_checklist.start_time < CURRENT_TIME  ' \
            'and version_manager_version_checklist.is_finished = false ' \
            'order by version_name,stage,version_checklist_start_time '
        with self.get_db() as session:
            try:
                results = session.execute(text(sql)).fetchall()
                return results
            except Exception as e:
                byte_log.error(f"db get_todo_checklist_before_today data fail. General exception message: {e}")
        return None

version_manager_detail_db = VersionManagerDetailDB()

if __name__ == '__main__':
    # total, results = version_manager_detail_db.get_version_checklist_by_version_id_and_stage(1, 1)
    # result = version_manager_detail_db.get_current_stage_by_version_id(2)
    # print(result)
    result = version_manager_detail_db.get_todo_checklist_before_today()
    print(result)
