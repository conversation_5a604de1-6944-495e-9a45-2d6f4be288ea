from bytedmysql import sqlalchemy_init
from sqlalchemy import create_engine, and_, or_, QueuePool
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import scoped_session
from sqlalchemy.orm import sessionmaker
from proj_settings import settings
from utils.common.bytelog import byte_log
# from utils.common.log import custom_logger
from contextlib import contextmanager
max_tries = 60 * 5  # 5 minutes
wait_seconds = 1


class SQLAlchemyDB:
    def __init__(self):
        super(SQLAlchemyDB, self).__init__()
        sqlalchemy_init()
        self._init_engine()

    def _init_engine(self):
        try:
            engine = create_engine(
                url=settings.DATABASE_URL,
                poolclass=QueuePool,
                pool_size=10,  # 链接池中保持数据库连接的数量，默认是5
                max_overflow=20,  # 当链接池中的连接数不够用的时候，允许额外再创建的最大链接数量，默认是10
                pool_timeout=30,  # 排队等数据库链接时的超时时间
                # isolation_level="READ COMMITTED",  # 设置事务的隔离界别
                connect_args={
                    "connect_timeout": 30,  # 设置连接超时时间为 30 秒
                    "read_timeout": 60,  # 设置读取超时时间为 60 秒
                },
                pool_recycle=3600,  # 设置回收链接的时间，单位毫秒
                pool_pre_ping=True,  # 每次连接前预先ping一下
            )
            self.Session = scoped_session(sessionmaker(bind=engine))  # scoped_session创建的session是线程安全的
        except SQLAlchemyError as e:
            byte_log.error(f"db create engine fail. exception message: {e}")
        except Exception as e:
            byte_log.error(f"db create engine fail. General exception message: {e}")

    @contextmanager
    def get_db(self):
        session = None
        try:
            session = self.Session()
            yield session
        except Exception as e:
            byte_log.error(f"Database session error: {e}")
            if session:
                session.rollback()
        finally:
            if session:
                session.close()

    def query(self, model, **kwargs):
        """
        查看
        @param model: 模型类或者模型类列表
        @param kwargs: 查询参数
            - joins: 内连接列表，格式为 [(table, condition), ...] 或 (table, condition)
            - left_joins: 左连接列表，格式为 [(table, condition), ...] 或 (table, condition)
            - filters: AND 过滤条件列表
            - filters_or: OR 过滤条件列表
            - order_by: 排序条件
            - group_by: 分组条件
            - query_having: HAVING 条件
            - offset: 偏移量
            - limit: 限制数量
            - return_query: 是否返回查询对象
            - return_sub_query: 是否返回子查询
            - return_first: 是否只返回第一条记录
            - return_all: 是否返回所有记录（默认True）
        @return: 根据参数返回不同类型的结果
        """
        with self.get_db() as session:
            try:
                if isinstance(model, list):
                    query = session.query(*model)
                else:
                    query = session.query(model)

                # 解包 kwargs 并应用相应的查询条件
                query_joins = kwargs.get("joins", [])
                query_left_joins = kwargs.get("left_joins", [])
                query_filters = kwargs.get("filters", {})
                query_filters_or = kwargs.get("filters_or", [])
                query_order_by = kwargs.get("order_by")
                query_group_by = kwargs.get("group_by")
                query_having = kwargs.get("query_having")
                query_offset = kwargs.get("offset")
                query_limit = kwargs.get("limit")
                return_query = kwargs.get("return_query")
                return_sub_query = kwargs.get("return_sub_query")
                return_first = kwargs.get("return_first")
                return_all = kwargs.get("return_all", True)

                # 应用连表查询
                if query_joins:
                    if isinstance(query_joins, list):
                        for join_table in query_joins:
                            if isinstance(join_table, tuple):
                                query = query.join(*join_table)
                            else:
                                query = query.join(join_table)
                    elif isinstance(query_joins, tuple):
                        query = query.join(*query_joins)

                # 应用左连接查询
                if query_left_joins:
                    if isinstance(query_left_joins, list):
                        for left_join_table in query_left_joins:
                            if isinstance(left_join_table, tuple):
                                query = query.outerjoin(*left_join_table)
                            else:
                                query = query.outerjoin(left_join_table)
                    elif isinstance(query_left_joins, tuple):
                        query = query.outerjoin(*query_left_joins)
                # 应用 AND 条件
                if query_filters:
                    query = query.filter(and_(True, *query_filters))
                # 应用 OR 条件
                if query_filters_or:
                    query = query.filter(or_(*query_filters_or))
                count = query.count()
                # 应用排序
                if query_order_by:
                    if isinstance(query_order_by, list):
                        query = query.order_by(*query_order_by)
                    else:
                        query = query.order_by(query_order_by)
                # 应用分组
                if query_group_by:
                    if isinstance(query_group_by, list):
                        for group_by in query_group_by:
                            query = query.group_by(group_by)
                    else:
                        query = query.group_by(query_group_by)
                # 应用 HAVING 条件
                if query_having:
                    query = query.having(query_having)
                # 应用偏移量
                if query_offset:
                    query = query.offset(query_offset)
                # 应用限制
                if query_limit:
                    query = query.limit(query_limit)
                # 返回查询对象或子查询
                if return_query:
                    return query
                if return_sub_query:
                    return query.subquery()

                # 执行查询
                if return_first:
                    return query.first()
                if return_all:
                    return count, query.all()
            except SQLAlchemyError as e:
                byte_log.error(f"db query data fail. exception message: {e}")
            except Exception as e:
                byte_log.error(f"db query data fail. General exception message: {e}")

    def add_one(self, model_instance):
        """
        增加单条
        @param model_instance: 模型实例
        @return:
        """
        with self.get_db() as session:
            if not model_instance:
                byte_log.debug(f"db add_one data is null.")
                return
            try:
                session.add(model_instance)
                session.commit()
                session.refresh(model_instance)
                byte_log.debug(f"db add_one {model_instance} success.")
                return model_instance
            except SQLAlchemyError as e:
                byte_log.error(f"db add_one {model_instance} fail. exception message: {e}")
            except Exception as e:
                byte_log.error(f"db add_one {model_instance} fail. General exception message: {e}")

    def add_more(self, model_instance_list):
        """
        批量增加
        @param model_instance_list: 模型实例列表
        @return:
        """
        with self.get_db() as session:
            if not model_instance_list:
                byte_log.debug(f"db add_more data is null.")
                return
            try:
                session.add_all(model_instance_list)
                session.flush()
                session.commit()
                [session.refresh(model_instance) for model_instance in model_instance_list]
                byte_log.debug(f"db add_more {model_instance_list} success.")
                return model_instance_list
            except SQLAlchemyError as e:
                byte_log.error(f"db add_more {model_instance_list} fail. exception message: {e}")
            except Exception as e:
                byte_log.error(f"db add_more {model_instance_list} fail. General exception message: {e}")

    def update_one(self, model_instance, data: dict):
        """
        更新单条
        @param model_instance: 模型实例
        @param data: 字典类型，包含要更的字段及其新值
        @return:
        """
        with self.get_db() as session:
            if not model_instance:
                byte_log.debug(f"db update_one data is null.")
                return
            try:
                for key, value in data.items():
                    setattr(model_instance, key, value)
                session.add(model_instance)
                session.commit()
                session.refresh(model_instance)
                byte_log.debug(f"db update_one data success.")
                return model_instance
            except SQLAlchemyError as e:
                byte_log.error(f"db update_one data fail. exception message: {e}")
            except Exception as e:
                byte_log.error(f"db update_one data fail. General exception message: {e}")

    def update_more(self, model, data_list):
        """
        批量更新
        @param model: 模型类
        @param data_list: 字典列表类型，包含要更新的字段及其新值
        @return:
        """
        with self.get_db() as session:
            if not model or not data_list:
                byte_log.debug(f"db update_more data is null.")
                return
            try:
                session.bulk_update_mappings(model, data_list)
                session.commit()
                [session.refresh(model_instance) for model_instance in model]
                byte_log.debug(f"db update_more data success.")
                return len(data_list)
            except SQLAlchemyError as e:
                byte_log.error(f"db update_more data fail. exception message: {e}")
            except Exception as e:
                byte_log.error(f"db update_more data fail. General exception message: {e}")

    def update_more_by_keys(self, model, data_list, key_fields=None):
        """
        通过指定的一个或多个键字段批量更新数据
        @param model: 模型类
        @param data_list: 字典列表类型，包含要更新的字段及其新值
        @param key_fields: 用于匹配的键字段名列表，如果为None则默认使用['id']
        @return: 更新的记录数量
        """
        with self.get_db() as session:
            if not model or not data_list:
                byte_log.debug("db update_more data is null.")
                return 0
            
            try:
                # 如果没有指定key_fields，默认使用id
                if key_fields is None:
                    key_fields = ['id']
                elif isinstance(key_fields, str):
                    key_fields = [key_fields]
                    
                # 构建查询条件
                conditions = []
                for data in data_list:
                    # 构建单条记录的匹配条件
                    record_conditions = []
                    for key_field in key_fields:
                        if key_field not in data:
                            raise ValueError(f"Key field '{key_field}' not found in data")
                        record_conditions.append(
                            getattr(model, key_field) == data[key_field]
                        )
                    # 将多个键字段的条件用AND连接
                    conditions.append(and_(*record_conditions))
                
                # 查询所有需要更新的记录
                records = session.query(model).filter(or_(*conditions)).all()
                
                # 创建复合键到record的映射
                record_map = {}
                for record in records:
                    key_tuple = tuple(getattr(record, key_field) for key_field in key_fields)
                    record_map[key_tuple] = record
                
                # 更新每条记录
                updated_count = 0
                for data in data_list:
                    key_tuple = tuple(data[key_field] for key_field in key_fields)
                    record = record_map.get(key_tuple)
                    if record:
                        # 更新记录的属性
                        for key, value in data.items():
                            if key not in key_fields:  # 不更新键字段
                                setattr(record, key, value)
                        updated_count += 1
                
                session.commit()
                byte_log.debug(f"db update_more data success. Updated {updated_count} records")
                return updated_count
                
            except SQLAlchemyError as e:
                session.rollback()
                byte_log.error(f"db update_more data fail. SQLAlchemy exception: {e}")
                raise
            except Exception as e:
                session.rollback()
                byte_log.error(f"db update_more data fail. General exception: {e}")
                raise

    def delete_one(self, model_instance):
        """
        删除单条
        @param model_instance: 模型实例
        @return:
        """
        with self.get_db() as session:
            if not model_instance:
                byte_log.debug(f"db delete_one data is null.")
                return
            try:
                session.delete(model_instance)
                session.commit()
                byte_log.debug(f"db delete_one data success.")
                return True
            except SQLAlchemyError as e:
                byte_log.error(f"db delete_one data fail. exception message: {e}")
                return False
            except Exception as e:
                byte_log.error(f"db delete_one data fail. General exception message: {e}")
                return False

    def delete_more(self, model_query):
        """
        批量删除
        @param model_query:
        @return:
        """
        with self.get_db() as session:
            if not model_query:
                byte_log.debug(f"db delete_more data is null.")
                return
            try:
                deleted = model_query.delete(synchronize_session='fetch')
                session.commit()
                byte_log.debug(f"db delete_more data success. delete count: {deleted}")
                return deleted
            except SQLAlchemyError as e:
                byte_log.error(f"db delete_more data fail. exception message: {e}")
            except Exception as e:
                byte_log.error(f"db delete_more data fail. General exception message: {e}")


if __name__ == "__main__":
    db = SQLAlchemyDB()
