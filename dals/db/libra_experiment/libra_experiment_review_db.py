from dals.db.base import SQLAlchemyDB
from dals.db.business.business_db import *
from models.libra_experiment.libra_experiment_review_model import *
from schemas.inner_model.libra_experiment_review_inner_model import *
from schemas.page import PageSchema
from schemas.request.libra_experiment.libra_experiment_review_req import *
from copy import deepcopy
import json

from utils.common.bytelog import byte_log
# from utils.common.log import custom_logger


from utils.common.time_util import *

class LibraExperimentReviewDB(SQLAlchemyDB):
    def __init__(self):
        super(LibraExperimentReviewDB, self).__init__()

    def get_value_from_experiment_filter_rule_json(self, experiment_filter_rule_json):
        key_device_platform = "device_platform"
        key_version_code_list = ["version_code","_version_code","_version_code_normal"]
        key_priority_region = "priority_region"

        list_device_platform = []
        list_version_code = []
        list_priority_region = []

        for i in range(len(experiment_filter_rule_json)):
            for j in range(len(experiment_filter_rule_json[i]["conditions"])):
                key = experiment_filter_rule_json[i]["conditions"][j]["condition"]["key"]
                if key == key_device_platform:
                    list_device_platform.append(experiment_filter_rule_json[i]["conditions"][j]["condition"])
                    continue
                if key in key_version_code_list:
                    list_version_code.append(experiment_filter_rule_json[i]["conditions"][j]["condition"])
                    continue
                if key == key_priority_region:
                    list_priority_region.append(experiment_filter_rule_json[i]["conditions"][j]["condition"])
                    continue
        return json.dumps(list_device_platform),json.dumps(list_version_code),json.dumps(list_priority_region)

    def insert_experiment(self,params:AddLibraExperimentParams):
        # 数据处理
        data = vars(deepcopy(params))

        # 业务id
        business_id = business_db.find_business_id_by_business_name(data["business_name"])
        # 数据库里没找到指定的业务id
        if not business_id or business_id <= 0:
            byte_log.error(f'insert_experiment dont find business id by business name = {data["business_name"]}')
            return None
        data["business_id"] = business_id

        # experiment_reviewer
        experiment_reviewer_result_json = json.loads(data["experiment_reviewer_result"].replace("'", "\""))
        data["experiment_reviewer"] = json.dumps([keys for dictionary in experiment_reviewer_result_json for keys in dictionary.keys()])

        # experiment_filter_rule_device_platform, experiment_filter_rule_version_code, experiment_filter_rule_priority_region
        experiment_filter_rule_json = json.loads(data["experiment_filter_rule"].replace("'", "\""))
        data["experiment_filter_rule_device_platform"], data["experiment_filter_rule_version_code"], data["experiment_filter_rule_priority_region"] = self.get_value_from_experiment_filter_rule_json(experiment_filter_rule_json)

        # meego
        experiment_meego_info_json = json.loads(data["experiment_meego_info"].replace("'", "\""))
        if 'meego_array' in experiment_meego_info_json.keys() and len(experiment_meego_info_json["meego_array"])>0:
            experiment_meego_info_0 = experiment_meego_info_json["meego_array"][0]
            data["experiment_meego_url"] = "https://meego.larkoffice.com/%s/story/detail/%s" %(experiment_meego_info_0["meego_project"],experiment_meego_info_0["meego_story"])

        # 插入数据
        data.pop("business_name")
        item = LibraExperiment(**data)
        self.add_one(item)
        # 插入失败
        if not item.id:
            return None

        # 返回插入以后的数据
        return item

    def insert_experiment_test_report(self,data):
        item = LibraExperimentTestReport(**data)
        self.add_one(item)
        return item

    def find_experiment_and_test_report_by_id(self, libra_experiment_id):
        sql = "select libra_experiment.*, " \
              "libra_experiment_test_report.`test_report_url`, " \
              "libra_experiment_test_report.`test_report_is_pass`, " \
              "libra_experiment_test_report.`test_report_tester` " \
              "from libra_experiment " \
              "left join libra_experiment_test_report " \
              "on libra_experiment.id = libra_experiment_test_report.uniq_libra_experiment_id " \
              f"where libra_experiment.id = {libra_experiment_id};"
        with self.get_db() as session:
            try:
                result = session.execute(text(sql)).first()
                return LibraExperimentAndTestReport(*result)
            except Exception as e:
                byte_log.error(f"db find_experiment_and_test_report_by_id data fail. General exception message: {e}")
        return None

    def find_latest_rule_by_business_id(self, business_id):
        sql = "select " \
              "libra_experiment_field_meta.field_name," \
              "libra_experiment_field_meta.field_desc," \
              "libra_experiment_field_meta.field_key," \
              "libra_experiment_field_meta.field_type," \
              "libra_experiment_field_review_rule.* " \
              "from libra_experiment_field_meta,libra_experiment_field_review_rule " \
              "where libra_experiment_field_review_rule.libra_experiment_field_meta_id = libra_experiment_field_meta.id " \
              f"and libra_experiment_field_review_rule.business_id = '{business_id}' " \
              "and is_using = 1;"
        with self.get_db() as session:
            try:
                results = session.execute(text(sql)).fetchall()
                return results
            except Exception as e:
                byte_log.error(f"db find_latest_rule_by_business_id data fail. General exception message: {e}")
        return None

    def insert_experiment_review_result(self, params: AddLibraExperimentReviewResultParams):
        item = LibraExperimentReviewResult(**vars(params))
        self.add_one(item)
        return item
    def insert_experiment_review_result_rule_detail(self, params: AddLibraExperimentReviewResultRuleDetailParams):
        item = LibraExperimentReviewResultRuleDetail(**vars(params))
        self.add_one(item)
        return item

libra_experiment_review_db = LibraExperimentReviewDB()