
from dals.db.base import SQLAlchemyDB
from models.libra_experiment.libra_experiment_review_model import LibraExperimentFieldReviewRule
from schemas.request.libra_experiment.libra_experiment_review_req import AddLibraExperimentRuleParams, UpdateLibraExperimentRuleParams


class LibraExperimentFieldReviewRuleDB(SQLAlchemyDB):
    def __init__(self):
        super(LibraExperimentFieldReviewRuleDB, self).__init__()

    def add_rule(self, params: AddLibraExperimentRuleParams):

        params_dict = params.model_dump()
        item = LibraExperimentFieldReviewRule(**params_dict)
        self.add_one(item)
        return item

    def update_rule(self, params: UpdateLibraExperimentRuleParams):

        model_class = [LibraExperimentFieldReviewRule]
        conditions = [LibraExperimentFieldReviewRule.id == params.id]
        client = self.query(model_class, filters=conditions, return_first=True)
        
        if not client:
            raise ValueError(f"未找到id为{params.id}的规则")
            
        # Only update non-empty fields
        update_data = {k: v for k, v in params.model_dump().items() 
                      if v is not None and k != 'id'}
        
        if update_data:
            self.update_one(client, update_data)
            
        return client


libra_rule_db = LibraExperimentFieldReviewRuleDB()
