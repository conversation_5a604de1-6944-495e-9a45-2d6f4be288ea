from dals.db.base import SQLAlchemyDB
from models.business.business_application_model import Business
from schemas.page import PageSchema
from sqlalchemy import text
from datetime import datetime

from utils.common.bytelog import byte_log


class BusinessDB(SQLAlchemyDB):
    def __init__(self):
        super(BusinessDB, self).__init__()

    def find_business_id_by_business_name(self, business_name):
        try:
            model_class = [Business]
            conditions = [Business.business_name == business_name]
            result = self.query(model_class, filters=conditions, return_first=True)
            if result:
                return result.id
            else:
                return None
        except Exception as e:
            byte_log.error(f"db find_business_id_by_business_name data fail. General exception message: {e}")
        return None

    def fetch_all(self):
        """
        获取所有业务
        @return:
        """
        model = [Business]
        total, result = self.query(model)
        return total, result

    def find_business_by_id(self, id):
        sql = f"select * from business where id='{id}'"
        with self.get_db() as session:
            try:
                result_tuple = session.execute(text(sql)).first()
                if result_tuple:
                    return result_tuple[0]
            except Exception as e:
                byte_log.error(f"db find_business_by_id data fail. General exception message: {e}")
        return None

    def add_business(self, business_name: str, business_dir: str = None) -> int:
        """
        新增业务
        @param business_name: 业务名称
        @param business_dir: 用例目录
        @return: 业务ID
        """
        try:
            with self.get_db() as session:
                # 检查业务名称是否已存在
                existing_business = session.query(Business).filter(Business.business_name == business_name).first()
                if existing_business:
                    byte_log.error(f"Business with name '{business_name}' already exists")
                    return None

                # 创建新业务
                new_business = Business(
                    business_name=business_name,
                    business_dir=business_dir,
                    create_time=datetime.now(),
                    update_time=datetime.now()
                )
                session.add(new_business)
                session.commit()
                return new_business.id
        except Exception as e:
            byte_log.error(f"db add_business data fail. General exception message: {e}")
            return None

    def update_business(self, business_id: int, business_name: str, business_dir: str = None) -> Business:
        """
        更新业务信息
        @param business_id: 业务ID
        @param business_name: 业务名称
        @param business_dir: 用例目录（可选）
        @return: 更新后的业务对象
        """
        try:
            with self.get_db() as session:
                # 检查业务是否存在
                business = session.query(Business).filter(Business.id == business_id).first()
                if not business:
                    byte_log.error(f"Business with id '{business_id}' does not exist")
                    return None

                # 检查新业务名称是否与其他业务重复
                if business_name != business.business_name:
                    existing_business = session.query(Business).filter(
                        Business.business_name == business_name,
                        Business.id != business_id
                    ).first()
                    if existing_business:
                        byte_log.error(f"Business with name '{business_name}' already exists")
                        return None

                # 更新业务信息
                business.business_name = business_name
                if business_dir is not None:
                    business.business_dir = business_dir
                business.update_time = datetime.now()
                
                session.commit()
                
                # 在会话关闭前获取所有需要的属性值
                result = {
                    "id": business.id,
                    "business_name": business.business_name,
                    "business_dir": business.business_dir
                }
                return result
        except Exception as e:
            byte_log.error(f"db update_business data fail. General exception message: {e}")
            return None

business_db = BusinessDB()



