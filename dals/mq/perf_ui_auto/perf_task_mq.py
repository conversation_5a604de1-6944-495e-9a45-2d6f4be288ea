from typing import Dict
from dals.mq.base import RocketMQBase, RocketMQConfig
from utils.common.bytelog import byte_log

class PerfTaskMQ(RocketMQBase):
    def __init__(self):
        config = RocketMQConfig()
        super().__init__(config)

    def send_subtask(self, subtask_data: Dict) -> bool:
        """发送子任务到消息队列"""
        try:
            return self.send_message(
                message=subtask_data,
                tag=f"subtask_{subtask_data.get('subtask_id')}"
            )
        except Exception as e:
            byte_log.error(f"Failed to send subtask: {str(e)}")
            raise

    def get_next_subtask(self) -> Dict:
        """获取下一个待执行的子任务"""
        try:
            messages = self.consume_message(batch_size=1)
            if messages:
                return messages[0]
            return None
        except Exception as e:
            byte_log.error(f"Failed to get next subtask: {str(e)}")
            raise

    def complete_subtask(self, receipt_handle: str) -> Dict:
        """标记子任务完成"""
        try:
            return self.ack_message(receipt_handle)
        except Exception as e:
            byte_log.error(f"Failed to complete subtask: {str(e)}")
            raise

