from typing import Optional, Dict, List, Union, Callable
from pyrocketmq import (
    RocketMQProducer,
    RocketMQConsumer,
    Message,
    ConsumeFromWhere
)
import json
from utils.common.bytelog import byte_log
from proj_settings import settings


class RocketMQConfig:
    """RocketMQ配置类"""

    def __init__(
            self,
            psm: str = settings.ROCKETMQ_PSM,
            cluster: str = settings.ROCKETMQ_CLUSTER,
            topic: str = settings.ROCKETMQ_TOPIC,
            group: str = settings.ROCKETMQ_GROUP,
            tag: str = "*",
            produce_timeout: float = 0.1,
            consume_from_where: ConsumeFromWhere = ConsumeFromWhere.LATEST,
            auto_commit: bool = True,
            orderly: bool = True
    ):
        self.psm = psm
        self.cluster = cluster
        self.topic = topic
        self.group = group
        self.tag = tag
        self.produce_timeout = produce_timeout
        self.consume_from_where = consume_from_where
        self.auto_commit = auto_commit
        self.orderly = orderly


class RocketMQBase:
    """RocketMQ基础类"""

    def __init__(self, config: RocketMQConfig):
        self.config = config
        self._producer: Optional[RocketMQProducer] = None
        self._consumer: Optional[RocketMQConsumer] = None

    def init_producer(self):
        """初始化生产者"""
        try:
            if not self._producer:
                self._producer = RocketMQProducer(
                    self.config.psm,
                    self.config.cluster,
                    self.config.group,
                    produce_timeout=self.config.produce_timeout
                )
                byte_log.info(f"为 group 初始化的 RocketMQ 生产者: {self.config.group}")
        except Exception as e:
            byte_log.error(f"初始化生产者失败: {str(e)}")
            raise

    def init_consumer(self, message_handler: Callable):
        """初始化消费者"""
        try:
            if not self._consumer:
                consumer_configs = {
                    'consume_from_where': self.config.consume_from_where,
                    'auto_commit': self.config.auto_commit,
                    'orderly': self.config.orderly,
                }
                if self.config.tag != "*":
                    consumer_configs['sub_expr'] = self.config.tag

                self._consumer = RocketMQConsumer(
                    self.config.psm,
                    self.config.cluster,
                    self.config.topic,
                    self.config.group,
                    message_handler,
                    **consumer_configs
                )
                byte_log.info(f"为 group 初始化 RocketMQ 消费者: {self.config.group}")
        except Exception as e:
            byte_log.error(f"初始化消费者失败: {str(e)}")
            raise

    def send_message(
            self,
            message_body: Union[str, Dict],
            tag: str = None,
            order_key: str = None,
            properties: Dict = None
    ) -> bool:
        """发送消息"""
        try:
            if not self._producer:
                self.init_producer()

            if isinstance(message_body, dict):
                message_body = json.dumps(message_body)

            message_body = message_body.encode('utf-8')
            msg = Message(
                self.config.topic,
                message_body,
                order_key=order_key,
                properties=properties or {}
            )

            resp = self._producer.send(msg)
            byte_log.info(f"消息发送成功: {resp}")
            return True
        except Exception as e:
            byte_log.error(f"消息发送失败: {str(e)}")
            raise

    def send_batch_messages(self, messages: List[Dict]) -> bool:
        """批量发送消息"""
        try:
            if not self._producer:
                self.init_producer()

            msg_list = []
            for msg_data in messages:
                body = msg_data.get('body')
                if isinstance(body, dict):
                    body = json.dumps(body)

                body = body.encode('utf-8')
                msg = Message(
                    self.config.topic,
                    body,
                    order_key=msg_data.get('order_key'),
                    properties=msg_data.get('properties', {})
                )
                msg_list.append(msg)

            resp = self._producer.send_batch(msg_list)
            byte_log.info(f"批量消息发送成功: {resp}")
            return True
        except Exception as e:
            byte_log.error(f"批量消息发送失败: {str(e)}")
            raise

    def start_consuming(self):
        """开始消费消息"""
        try:
            if self._consumer:
                self._consumer.start()
                byte_log.info("消费成功")
        except Exception as e:
            byte_log.error(f"消费失败: {str(e)}")
            raise

    def stop_producer(self):
        """停止生产者"""
        try:
            if self._producer:
                self._producer.close()
            byte_log.info("停止生产者")
        except Exception as e:
            byte_log.error(f"停止生产者出错: {str(e)}")

    def stop_consumer(self):
        """停止消费者"""
        try:
            if self._consumer:
                self._consumer.close()
            byte_log.info("停止消费者")
        except Exception as e:
            byte_log.error(f"停止消费者出错: {str(e)}")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop_producer()
        self.stop_consumer()

if __name__ == '__main__':
    mq_config = RocketMQConfig()
    mq_base = RocketMQBase(mq_config)
    mq_base.init_producer()
    mq_base.send_message("test")
    mq_base.init_consumer(lambda x: print(x))
    mq_base.start_consuming()