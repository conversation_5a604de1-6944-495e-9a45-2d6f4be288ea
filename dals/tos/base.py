import os
import time

import bytedtos

from proj_settings import settings
from utils.common.bytelog import byte_log
# from utils.common.log import custom_logger


class TosClient:
    def __init__(self):
        self.client = bytedtos.Client(settings.TOS_BUCKET_NAME,
                                      settings.TOS_ACCESS_KEY,
                                      service=settings.TOS_PSM,
                                      cluster=settings.TOS_CLUSTER,
                                      idc=settings.TOS_IDC,
                                      # timeout 是可选参数，设置请求超时
                                      timeout=60,
                                      # connection_time 是可选参数，设置连接超时
                                      connect_timeout=60,
                                      )

    def content_upload(self, obj_key, content):
        """
        上传字符流和Bytes流
        @param obj_key:
        @param content: StringIO('Hello TOS')/BytesIO(b'Hello TOS')
        @return:
        """
        try:
            resp = self.client.put_object(obj_key, content)
            byte_log.debug(
                f"toc put success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            # 操作失败，捕获异常，可从返回信息中获取详细错误信息
            # request id 可定位具体问题，强烈建议日志中保存
            byte_log.error(f"toc put fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    def multipart_upload(self, obj_key, file_name):
        """
        分片上传
        @param obj_key:
        @param file_name:
        @return:
        """
        total_size = os.path.getsize(file_name)
        part_size = 5 * 1024 * 1024

        try:
            init_resp = self.client.init_upload(obj_key)
            upload_id = init_resp.upload_id
            parts_list = []
            # 上传分片数据
            with open(file_name, 'rb') as f:
                part_number = 1
                offset = 0
                while offset < total_size:
                    if total_size - offset < 2 * part_size:
                        num_to_upload = total_size - offset
                    else:
                        num_to_upload = min(part_size, total_size - offset)
                    f.seek(offset, os.SEEK_SET)
                    cur_data = f.read(num_to_upload)
                    upload_part_resp = self.client.upload_part(obj_key, upload_id, part_number, cur_data)
                    parts_list.append(upload_part_resp.part_number)
                    offset += num_to_upload
                    part_number += 1

            comp_resp = self.client.complete_upload(obj_key, upload_id, parts_list)
            byte_log.debug("toc complete_upload success. code: {}, request_id: {}".format(comp_resp.status_code,
                                                                                               comp_resp.headers[
                                                                                                   bytedtos.consts.ReqIdHeader]))
        except bytedtos.TosException as e:
            byte_log.debug(
                "toc complete_upload fail. code: {}, request_id: {}, message: {}".format(e.code, e.request_id, e.msg))

    def abort_upload(self, obj_key, upload_id):
        """
        取消分片上传
        @param obj_key:
        @param upload_id:
        @return:
        """
        try:
            resp = self.client.abort_upload(obj_key, upload_id)
            byte_log.debug("toc abort_upload success. code: {}, request_id: {}".format(resp.status_code,
                                                                                            resp.headers[
                                                                                                bytedtos.consts.ReqIdHeader]))
        except bytedtos.TosException as e:
            byte_log.debug(
                "toc abort_upload fail. code: {}, request_id: {}, message: {}".format(e.code, e.request_id, e.msg))

    def get_object(self, obj_key):
        """
        普通下载
        @param obj_key:
        @return:
        """
        try:
            resp = self.client.get_object(obj_key)
            # 下载对象到内存中
            byte_log.debug(
                f"toc get object success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            byte_log.error(f"toc get object fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    def get_object_range(self, obj_key, start, end):
        """
        范围下载
        @param obj_key:
        @param start:
        @param end:
        @return:
        """
        try:
            resp = self.client.get_object_range(obj_key, start=start, end=end)
            byte_log.debug(
                f"toc get object range success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            byte_log.error(
                f"toc get object range fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    def delete_object(self, obj_key):
        """
        删除对象
        @param obj_key:
        @return:
        """
        try:
            resp = self.client.delete_object(obj_key)
            byte_log.debug(
                f"toc delete object success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            # 操作失败，捕获异常，可从返回信息中获取详细错误信息
            # request id 可定位具体问题，强烈建议日志中保存
            byte_log.error(f"toc delete object fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")


if __name__ == "__main__":
    tos_client = TosClient()
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())))
    tos_client.get_object("abc/test.json")
    tos_client.delete_object("abc/test2.json")
    print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())))
