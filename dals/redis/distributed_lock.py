import threading
import time

from dals.redis.base import redis_client


class RedisDistributedLock:
    def __init__(self, lock_name, expire_time=10):
        self.client = redis_client
        self.lock_name = lock_name
        self.lock_value = str(time.time()) + str(id(self))  # 使用唯一值作为锁的值
        self.expire_time = expire_time
        self.lock_acquired = False

    def acquire(self, blocking=True, timeout=None):
        """
        获取锁
        @param blocking:
        @param timeout:
        @return:
        """
        end_time = time.time() + timeout if timeout is not None else None
        while True:
            if self.client.set(self.lock_name, self.lock_value, nx=True, px=self.expire_time):
                self.lock_acquired = True
                return True
            if not blocking:
                return False
            if end_time is not None and time.time() >= end_time:
                return False
            time.sleep(0.01)  # 等待一小段时间后重试

    def release(self):
        """
        释放锁
        @return:
        """
        if self.lock_acquired:
            script = """  
            if redis.call("get", KEYS[1]) == ARGV[1] then  
                return redis.call("del", KEYS[1])  
            else  
                return 0  
            end  
            """
            result = self.client.eval(script, 1, self.lock_name, self.lock_value)
            if result:
                self.lock_acquired = False

    def renew(self, expire_time):
        if self.lock_acquired:
            self.client.expire(self.lock_name, expire_time)

    def __enter__(self):
        self.acquire(blocking=True)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def run_with_lock(self, task, renew_interval=5):
        if self.acquire(blocking=True):
            try:
                # 启动守护线程来续期锁
                def renew_lock():
                    while self.lock_acquired:
                        time.sleep(renew_interval)
                        self.renew(self.expire_time)

                renew_thread = threading.Thread(target=renew_lock, daemon=True)
                renew_thread.start()
                # 执行任务
                task()
            finally:
                self.release()
