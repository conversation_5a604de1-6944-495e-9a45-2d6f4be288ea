from datetime import datetime
from typing import Dict, Optional, List
import json
import re

from dals.redis.base import RedisClient
from utils.common.bytelog import byte_log
from schemas.response.perf_ui_auto.perf_task_res import PerfSubTaskRes

class PerfTaskRedis(RedisClient):
    """性能测试任务Redis管理类"""

    # Redis Key前缀定义
    SUB_TASK_QUEUE_KEY = "perf:subtask:queue:{}"  # 子任务队列，按client_id区分

    def _validate_sub_task(self, task_data: Dict) -> bool:
        """验证子任务数据的合法性"""
        try:
            # 确保task_id是整数
            if isinstance(task_data.get('task_id'), str):
                task_data['task_id'] = int(task_data['task_id'])
            
            # 验证video_url格式
            video_url = task_data.get('video_url')
            if video_url:
                # 检查是否是合法的URL格式
                if not isinstance(video_url, str) or not video_url.startswith(('http://', 'https://')):
                    byte_log.error(f"非法的video_url格式: {video_url}")
                    return False
                
            # 使用PerfSubTaskRes验证数据
            PerfSubTaskRes(**task_data)
            return True
        except Exception as e:
            byte_log.error(f"子任务数据验证失败: {e}")
            return False

    def _clean_queue(self, client_id: int):
        """清理队列中的非法数据"""
        try:
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            all_tasks = self.lrange(queue_key, 0, -1)
            valid_tasks = []
            
            for task_data in all_tasks:
                try:
                    task = json.loads(task_data.decode())
                    if self._validate_sub_task(task):
                        valid_tasks.append(task_data)
                except json.JSONDecodeError:
                    byte_log.error(f"无效的JSON数据: {task_data}")
                    continue
                    
            # 清空并重建队列
            self.delete(queue_key)
            if valid_tasks:
                self.rpush(queue_key, *valid_tasks)
                
            removed_count = len(all_tasks) - len(valid_tasks)
            if removed_count > 0:
                byte_log.warning(f"从队列{client_id}中清理了{removed_count}条无效数据")
                
        except Exception as e:
            byte_log.error(f"清理队列失败: {e}")

    def get_queue_length(self, client_id: int) -> int:
        """获取队列长度"""
        try:
            self._clean_queue(client_id)  # 先清理队列
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            return self.llen(queue_key)
        except Exception as e:
            byte_log.error(f"获取队列长度失败: {e}")
            return 0

    def get_head_sub_task(self, client_id: int) -> Optional[Dict]:
        """获取队列头部的子任务（不移除）"""
        try:
            self._clean_queue(client_id)  # 先清理队列
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            data = self.lrange(queue_key, 0, 0)
            if data:
                task = json.loads(data[0].decode())
                if self._validate_sub_task(task):
                    return task
            return None
        except Exception as e:
            byte_log.error(f"获取队头子任务失败: {e}")
            return None

    def get_all_sub_tasks(self, client_id: int) -> List[Dict]:
        """获取指定客户端的所有子任务"""
        try:
            self._clean_queue(client_id)  # 先清理队列
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            data = self.lrange(queue_key, 0, -1)
            tasks = []
            for item in data:
                try:
                    task = json.loads(item.decode())
                    if self._validate_sub_task(task):
                        tasks.append(task)
                except Exception as e:
                    byte_log.error(f"解析子任务数据失败: {e}")
                    continue
            return tasks
        except Exception as e:
            byte_log.error(f"获取所有子任务失败: {e}")
            return []

    def check_sub_task_exists(self, client_id: int, sub_task_id: int) -> bool:
        """检查队列中是否已存在指定ID的子任务"""
        try:
            # 通过调用get_all_sub_tasks方法获取所有子任务
            all_sub_tasks = self.get_all_sub_tasks(client_id)

            # 遍历返回的子任务列表，检查是否存在匹配的sub_task_id
            for task in all_sub_tasks:
                if task.get('id') == sub_task_id:
                    return True

            return False
        except Exception as e:
            byte_log.error(f"检查子任务是否存在失败: {e}")
            return False

    def push_sub_task(self, client_id: int, sub_task: Dict) -> bool:
        """将子任务添加到队列尾部"""
        try:
            if not self._validate_sub_task(sub_task):
                return False

            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            sub_task_copy = sub_task.copy()

            # 处理datetime对象
            for key, value in sub_task_copy.items():
                if isinstance(value, datetime):
                    sub_task_copy[key] = value.isoformat()

            self.rpush(queue_key, json.dumps(sub_task_copy))
            return True
        except Exception as e:
            byte_log.error(f"添加子任务失败: {e}")
            return False

    def remove_head_sub_task(self, client_id: int) -> bool:
        """移除队列头部的子任务"""
        try:
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            result = self.lpop(queue_key)
            return result is not None
        except Exception as e:
            byte_log.error(f"移除队头子任务失败: {e}")
            return False

    def move_to_tail(self, client_id: int) -> bool:
        """将队头子任务移到队尾"""
        try:
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            # 获取队头数据
            head_data = self.lrange(queue_key, 0, 0)
            # 如果队头没有数据,直接返回
            if not head_data:
                return True
                
            # 将队头数据移到队尾
            self.lpop(queue_key)
            self.rpush(queue_key, head_data[0])
            return True
        except Exception as e:
            byte_log.error(f"移动子任务到队尾失败: {e}")
            return False

    def clear_queue(self, client_id: int) -> bool:
        """清空指定客户端的队列"""
        try:
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            self.delete(queue_key)
            return True
        except Exception as e:
            byte_log.error(f"清空队列失败: {e}")
            return False

    def remove_sub_tasks_by_task_id(self, client_id: int, task_id: int) -> bool:
        """
        从队列中删除指定任务ID的所有子任务数据
        
        Args:
            client_id (int): 客户端ID
            task_id (int): 要删除的任务ID
        
        Returns:
            bool: 操作是否成功
        """
        try:
            queue_key = self.SUB_TASK_QUEUE_KEY.format(client_id)
            # 获取所有任务
            all_tasks = self.lrange(queue_key, 0, -1)
            if not all_tasks:
                return True
            
            # 过滤出需要保留的任务
            retained_tasks = []
            removed_count = 0
            
            for task_data in all_tasks:
                try:
                    task = json.loads(task_data.decode())
                    # 只保留不属于指定task_id的任务
                    if task.get('task_id') != task_id:
                        retained_tasks.append(task_data)
                    else:
                        removed_count += 1
                except json.JSONDecodeError:
                    byte_log.error(f"无效的JSON数据: {task_data}")
                    # 保留无法解析的数据，留待清理队列时处理
                    retained_tasks.append(task_data)
                
            # 使用事务原子性地更新队列
            with self.pipeline() as pipe:
                self.delete(queue_key)
                if retained_tasks:
                    self.rpush(queue_key, *retained_tasks)
            
            if removed_count > 0:
                byte_log.info(f"从队列{client_id}中删除了{removed_count}个task_id为{task_id}的子任务")
            return True
            
        except Exception as e:
            byte_log.error(f"删除任务{task_id}的子任务失败: {e}")
            return False

perf_task_redis = PerfTaskRedis()
# if __name__ == "__main__":
#     print(perf_task_redis.get_all_sub_tasks(4))