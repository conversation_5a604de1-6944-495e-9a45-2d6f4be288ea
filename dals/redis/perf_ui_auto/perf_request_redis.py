from datetime import datetime
from typing import Dict, Optional, List
import json
import re

from dals.redis.base import RedisClient
from utils.common.bytelog import byte_log


class PerfRequestRedis(RedisClient):
    """性能测试任务Redis管理类"""

    # Redis Key前缀定义
    SUB_REQUEST_QUEUE_KEY = "perf:request:queue:{}"  # 请求队列，按client_id区分

    def get_all_sub_requests(self, client_id: int) -> List[Dict]:
        """
        获取指定客户端的所有请求
        
        Args:
            client_id (int): 客户端ID
            
        Returns:
            List[Dict]: 所有请求的列表
        """
        try:
            # 获取队列key
            queue_key = self.SUB_REQUEST_QUEUE_KEY.format(client_id)
            
            # 获取所有请求
            data = self.lrange(queue_key, 0, -1)
            
            # 处理数据
            requests = []
            for item in data:
                try:
                    request = json.loads(item.decode())
                    requests.append(request)
                except Exception as e:
                    byte_log.error(f"解析请求数据失败: {e}")
                    continue
                    
            byte_log.info(f"成功获取客户端{client_id}的请求队列，共{len(requests)}条请求")
            return requests
            
        except Exception as e:
            byte_log.error(f"获取请求队列失败: {e}")
            return []

    def push_request(self, client_id: int, request: Dict) -> bool:
        """
        将请求添加到队列尾部
        
        Args:
            client_id (int): 客户端ID
            request (Dict): 请求数据
            
        Returns:
            bool: 操作是否成功
        """
        try:
            queue_key = self.SUB_REQUEST_QUEUE_KEY.format(client_id)
            request_copy = request.copy()
            
            # 处理datetime对象
            for key, value in request_copy.items():
                if isinstance(value, datetime):
                    request_copy[key] = value.isoformat()
                    
            self.rpush(queue_key, json.dumps(request_copy))
            byte_log.info(f"成功添加请求到客户端{client_id}的队列")
            return True
        except Exception as e:
            byte_log.error(f"添加请求失败: {e}")
            return False

    def clear_queue(self, client_id: int) -> bool:
        """
        清空指定客户端的请求队列
        
        Args:
            client_id (int): 客户端ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            queue_key = self.SUB_REQUEST_QUEUE_KEY.format(client_id)
            self.delete(queue_key)
            byte_log.info(f"成功清空客户端{client_id}的请求队列")
            return True
        except Exception as e:
            byte_log.error(f"清空请求队列失败: {e}")
            return False

perf_request_redis = PerfRequestRedis()

# if __name__ == "__main__":
#     print(perf_request_redis.get_all_sub_requests(4))