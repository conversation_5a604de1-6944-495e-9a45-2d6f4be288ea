import bytedredis
from proj_settings import settings


class RedisClient:
    """Redis客户端封装类，提供常用的Redis操作方法"""

    def __init__(self):
        """初始化Redis客户端"""
        self.redis_client = None
        self.connect()

    def connect(self):
        """创建Redis连接
        使用settings中的REDIS_URL创建连接
        """
        self.redis_client = bytedredis.Client.from_url(settings.REDIS_URL)

    def pipeline(self):
        """创建并返回pipeline对象
        Returns:
            Pipeline对象，用于批量执行命令
        """
        return self.redis_client.pipeline()

    def set(self, key, value, ex=None, px=None, nx=False, xx=False):
        """设置键值对
        Args:
            key: 键名
            value: 值
            ex: 过期时间(秒)
            px: 过期时间(毫秒)
            nx: 如果设置为True，则只有key不存在时才执行操作
            xx: 如果设置为True，则只有key存在时才执行操作
        Returns:
            操作是否成功
        """
        return self.redis_client.set(key, value, ex=ex, px=px, nx=nx, xx=xx)

    def get(self, key):
        """获取键值对
        Args:
            key: 键名
        Returns:
            值，不存在则返回None
        """
        return self.redis_client.get(key)

    def delete(self, key):
        """删除键值对
        Args:
            key: 键名
        Returns:
            被删除key的数量
        """
        return self.redis_client.delete(key)

    def expire(self, key, time):
        """设置键的过期时间
        Args:
            key: 键名
            time: 过期时间(秒)
        Returns:
            操作是否成功
        """
        return self.redis_client.expire(key, time)

    def hset(self, name, key, value):
        """在哈希表中设置字段的值
        Args:
            name: 哈希表名
            key: 字段名
            value: 值
        Returns:
            如果字段是新的，返回1；如果字段已存在，返回0
        """
        return self.redis_client.hset(name, key, value)

    def hget(self, name, key):
        """获取哈希表中字段的值
        Args:
            name: 哈希表名
            key: 字段名
        Returns:
            字段值，不存在则返回None
        """
        return self.redis_client.hget(name, key)

    def hgetall(self, name):
        """获取哈希表中所有字段及其值
        Args:
            name: 哈希表名
        Returns:
            包含所有字段及其值的字典
        """
        return self.redis_client.hgetall(name)

    def hdel(self, name, *keys):
        """删除哈希表中的字段
        Args:
            name: 哈希表名
            *keys: 要删除的字段名列表
        Returns:
            被成功删除的字段数量
        """
        return self.redis_client.hdel(name, *keys)

    def rpush(self, name, *values):
        """在列表的右侧插入一个或多个值
        Args:
            name: 列表名
            *values: 要插入的值列表
        Returns:
            插入后列表的长度
        """
        return self.redis_client.rpush(name, *values)

    def lpop(self, name):
        """移除并获取列表的第一个元素
        Args:
            name: 列表名
        Returns:
            第一个元素的值，列表为空时返回None
        """
        return self.redis_client.lpop(name)

    def llen(self, name):
        """获取列表长度
        Args:
            name: 列表名
        Returns:
            列表的长度
        """
        return self.redis_client.llen(name)

    def sadd(self, name, *values):
        """向集合添加一个或多个成员
        Args:
            name: 集合名
            *values: 要添加的成员列表
        Returns:
            添加成功的成员数量
        """
        return self.redis_client.sadd(name, *values)

    def srem(self, name, *values):
        """移除集合中的一个或多个成员
        Args:
            name: 集合名
            *values: 要移除的成员列表
        Returns:
            成功移除的成员数量
        """
        return self.redis_client.srem(name, *values)

    def smembers(self, name):
        """获取集合的所有成员
        Args:
            name: 集合名
        Returns:
            包含所有成员的集合
        """
        return self.redis_client.smembers(name)

    def zadd(self, name, *args, **kwargs):
        """在有序集合中添加一个或多个成员，或更新已存在成员的分数
        Args:
            name: 有序集合名
            *args, **kwargs: 成员和分数
        Returns:
            新添加成员的数量
        """
        return self.redis_client.zadd(name, *args, **kwargs)

    def zrem(self, name, *values):
        """移除有序集合中的一个或多个成员
        Args:
            name: 有序集合名
            *values: 要移除的成员列表
        Returns:
            成功移除的成员数量
        """
        return self.redis_client.zrem(name, *values)

    def zrange(self, name, start, num, desc=False, withscores=False, score_cast_func=float):
        """获取有序集合指定区间内的成员
        Args:
            name: 有序集合名
            start: 起始位置
            num: 结束位置
            desc: 是否降序排列
            withscores: 是否返回分数
            score_cast_func: 分数转换函数
        Returns:
            指定区间内的成员列表，如果withscores为True，返回(成员, 分数)元组的列表
        """
        return self.redis_client.zrange(name, start, num, desc=desc, withscores=withscores,
                                      score_cast_func=score_cast_func)

    def eval(self, script, numkeys, *keys_and_args):
        """执行Lua脚本
        Args:
            script: Lua脚本字符串
            numkeys: 键名参数的数量
            *keys_and_args: 键名参数和附加参数列表
        Returns:
            脚本的返回值
        """
        return self.redis_client.eval(script, numkeys, *keys_and_args)

    def scan(self, cursor=0, match=None, count=None):
        """使用scan命令迭代获取键
        Args:
            cursor: 游标
            match: 匹配模式
            count: 每次扫描的键数量
        Returns:
            (新游标, 匹配的键列表)的元组
        """
        return self.redis_client.scan(cursor=cursor, match=match, count=count)

    def lrange(self, name: str, start: int, end: int):
        """获取列表指定范围内的元素
        Args:
            name: 列表名
            start: 起始位置
            end: 结束位置
        Returns:
            指定范围内的元素列表
        """
        return self.redis_client.lrange(name, start, end)

    def incr(self, name, amount=1):
        """将key中储存的数字值增一或指定步长
        Args:
            name: 键名
            amount: 增加的步长，默认为1
        Returns:
            增加后的值
        """
        return self.redis_client.incr(name, amount)

    def decr(self, name, amount=1):
        """将key中储存的数字值减一或指定步长
        Args:
            name: 键名
            amount: 减少的步长，默认为1
        Returns:
            减少后的值
        """
        return self.redis_client.decr(name, amount)

    def exists(self, *names):
        """检查一个或多个key是否存在
        Args:
            *names: 一个或多个键名
        Returns:
            存在的key的数量
        """
        return self.redis_client.exists(*names)

    def ttl(self, name):
        """获取key的剩余生存时间
        Args:
            name: 键名
        Returns:
            剩余生存时间(秒)，-1表示永久，-2表示不存在
        """
        return self.redis_client.ttl(name)

    def hmset(self, name, mapping):
        """批量设置哈希表中的值
        Args:
            name: 哈希表名
            mapping: 包含键值对的字典
        Returns:
            操作是否成功
        """
        return self.redis_client.hmset(name, mapping)

    def hmget(self, name, keys):
        """批量获取哈希表中的值
        Args:
            name: 哈希表名
            keys: 要获取的字段列表
        Returns:
            按请求顺序返回的值列表
        """
        return self.redis_client.hmget(name, keys)

    def zincrby(self, name, amount, value):
        """有序集合中对指定成员的分数加上增量
        Args:
            name: 有序集合名
            amount: 增量
            value: 成员
        Returns:
            member的新分数
        """
        return self.redis_client.zincrby(name, amount, value)

    def zrank(self, name, value):
        """获取有序集合中成员的排名(从小到大)
        Args:
            name: 有序集合名
            value: 成员
        Returns:
            排名，不存在返回None
        """
        return self.redis_client.zrank(name, value)

    def zrevrank(self, name, value):
        """获取有序集合中成员的排名(从大到小)
        Args:
            name: 有序集合名
            value: 成员
        Returns:
            排名，不存在返回None
        """
        return self.redis_client.zrevrank(name, value)

    def zscore(self, name, value):
        """获取有序集合中成员的分数
        Args:
            name: 有序集合名
            value: 成员
        Returns:
            分数，不存在返回None
        """
        return self.redis_client.zscore(name, value)

    def persist(self, name):
        """移除key的过期时间
        Args:
            name: 键名
        Returns:
            操作是否成功
        """
        return self.redis_client.persist(name)

    def type(self, name):
        """返回key所储存的值的类型
        Args:
            name: 键名
        Returns:
            none (key不存在)
            string (字符串)
            list (列表)
            set (集合)
            zset (有序集)
            hash (哈希表)
        """
        return self.redis_client.type(name)


# 创建全局Redis客户端实例
redis_client = RedisClient()