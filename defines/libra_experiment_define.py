# 期望值
IS_NONE = 0
IS_NOT_NONE = 1
# 期望值
IS_NOT_IGNORE_CASE = 0
IS_IGNORE_CASE = 1

# 字段数据类型
FIELD_TYPE_INT = 1      # int
FIELD_TYPE_STRING = 2   # string
FIELD_TYPE_FLOAT = 3    # float
FIELD_TYPE_LIST = 4     # list
FIELD_TYPE_MAP = 5      # map
FIELD_TYPE_BOOL = 6     # bool

# 检查规则类型
OPERATOR_TYPE_EQUAL = 1                             # =
OPERATOR_TYPE_GT = 2                                # >
OPERATOR_TYPE_GE = 3                                # >=
OPERATOR_TYPE_LT = 4                                # <
OPERATOR_TYPE_LQ = 5                                # <=
OPERATOR_TYPE_REGEX = 6                             # 正则
OPERATOR_TYPE_SET_SUBSET = 7                        # 是某个列表的子集
OPERATOR_TYPE_SET_EQUAL = 8                         # 与某个列表完全匹配
OPERATOR_TYPE_SET_PARENT_SET = 9                    # 是某个列表的父集
OPERATOR_TYPE_IS_NONE = 10                          # 是否为空
OPERATOR_TYPE_IGNORE_CASE = 11                      # 忽略大小写
OPERATOR_TYPE_REVIEWER_HAS_X_RD = 12                # 评审人里至少有n个是RD
OPERATOR_TYPE_SET_HAS_ONE_AT_LEAST = 13             # 至少包含其一
OPERATOR_TYPE_EXPERIMENT_GROUP_KEY_IN_LIST = 14     # 实验组的key要在特定取值内

# 检查字段方式
CHECK_VALUE = 0     # 检查值本身
CHECK_NUM = 1       # 检查值个数
CHECK_RULE = 2      # 检查是否符合某规则

# 实验组类型
EXPERIMENT_TYPE_CONTROL_GROUP = 0       # 对照组
EXPERIMENT_TYPE_EXPERIMENTAL_GROUP = 1  # 实验组

# 期望值/实际值的长度限制（超长则不在消息卡片里展示）
VALUE_LENGTH_LIMIT = 160

# 检查结果文本描述
CHECK_RESULT_STR_PASS = "<text_tag color='green'>通过</text_tag>"
CHECK_RESULT_STR_FAIL = "<text_tag color='red'>不通过</text_tag>"
CHECK_RESULT_STR_SKIP = "<text_tag color='neutral'>未检查</text_tag>"
CHECK_TYPE_STR_MUST = "<text_tag color='orange'>必须类</text_tag>"
CHECK_TYPE_STR_NOT_MUST = "<text_tag color='lime'>建议类</text_tag>"

IGNORE_CASE_LOWER = "lower"

# 飞书卡片通知内容(按顺序依次是不通过-QA、通过-QA、不通过-RD、通过-RD)
LARK_CARD_OPERATION_MSG_LIST = [
    [
        '请及时点击[实验链接](%s)，将审批结果置为不通过，并关注【必须】类型的异常字段修正情况',
        '请及时点击[实验链接](%s)，完成审批'
    ],
    [
        '请及时点击[实验链接](%s)，确认并修正【必须】类型的异常字段',
        '请及时关注[实验](%s)审批情况'
    ]
]
# 飞书卡片通知结果（不通过、通过）
LARK_CARD_CONST_LIST = [
    {
        "result_color": "red",
        "result_str": "不通过"
    },
    {
        "result_color": "green",
        "result_str": "通过"
    }
]

# 测试环境飞书消息接收者
LARK_CARD_TEST_REMINDER_LIST = ["wuzhihao.wzh"]

# 实验meego节点相关信息
# 1-点播 2-直播 3-RTC
MEEGO_INFO_MAP = {
    1: {
        "MEEGO_TEST_NODE_NAME": '',
        "MEEGO_TEST_IS_PASS_FILED_KEY": '',
        "MEEGO_TEST_IS_PASS_FILED_VALUE": '是',
        "MEEGO_TEST_REPORT_URL_FILED_KEY": '',
        "MEEGO_NODE_FINISH_STATUS": 3,
    },
    2: {
        "MEEGO_TEST_NODE_NAME": '',
        "MEEGO_TEST_IS_PASS_FILED_KEY": '',
        "MEEGO_TEST_IS_PASS_FILED_VALUE": '是',
        "MEEGO_TEST_REPORT_URL_FILED_KEY": '',
        "MEEGO_NODE_FINISH_STATUS": 3,
    },
    3: {
        "MEEGO_TEST_NODE_NAME": '实验前验证',
        "MEEGO_TEST_IS_PASS_FILED_KEY": 'field_7916f6',
        "MEEGO_TEST_IS_PASS_FILED_VALUE": '是',
        "MEEGO_TEST_REPORT_URL_FILED_KEY": 'field_cee323',
        "MEEGO_NODE_FINISH_STATUS": 3,
    }
}

# 实验类型
EXPERIMENT_TYPE_SERVICE = 'strategy'
# 实验field_type
FIELD_TYPE_VERSION_CODE = 'experiment_filter_rule_version_code'