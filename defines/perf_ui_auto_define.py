'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/defines/perf_ui_auto_define.py
Description: 
'''
DEVICE_SYS_TYPE_UNKNOWN = 0
DEVICE_SYS_TYPE_ANDROID = 1
DEVICE_SYS_TYPE_IOS = 2
DEVICE_SYS_TYPE_WINDOWS = 3
DEVICE_SYS_TYPE_MACOS = 4
DEVICE_SYS_TYPE_LINUX = 5
DEVICE_SYS_TYPE_MAP = {
    DEVICE_SYS_TYPE_UNKNOWN: ['Unknown', '', None, False],
    DEVICE_SYS_TYPE_ANDROID: ['Android', 'android'],
    DEVICE_SYS_TYPE_IOS: ['iOS', 'ios'],
    DEVICE_SYS_TYPE_WINDOWS: ['Windows', 'windows'],
    DEVICE_SYS_TYPE_MACOS: ['MacOS', 'macos'],
    DEVICE_SYS_TYPE_LINUX: ['Linux', 'linux']
}

# 设备连接状态 connect_state
DEVICE_STATE_OFFLINE = 0
DEVICE_STATE_ONLINE = 1
DEVICE_STATE_MAP = {
    DEVICE_STATE_OFFLINE: ['Offline', 'fail', 'unauthorized'],
    DEVICE_STATE_ONLINE: ['Online', 'success', 'device']
}

# 设备连接类型 connect_type
DEVICE_CONNECT_TYPE_UNKNOWN = 0
DEVICE_CONNECT_TYPE_USB = 1
DEVICE_CONNECT_TYPE_WIFI = 2
DEVICE_CONNECT_TYPE_MAP = {
    DEVICE_CONNECT_TYPE_UNKNOWN: ['Unknown', '', None, False],
    DEVICE_CONNECT_TYPE_USB: ['USB', 'wired', 'usb'],
    DEVICE_CONNECT_TYPE_WIFI: ['WiFi', 'wireless', 'network'],
}

# 团队名称 team
TEAM_NAME_GLOBAL_RTC = 1
TEAM_NAME_MAP = {
    TEAM_NAME_GLOBAL_RTC: ['GLOBAL_RTC', 'GlobalRTC', 'global_rtc']
}

PERF_CASE_REPO_URL = "******************:bytertc_i18n/global_business_perf.git"  # 性能用例仓库地址


# app平台
PLATFORM_ANDROID = 1
PLATFORM_IOS = 2
PLATFORM_MAP = {
    PLATFORM_ANDROID: "Android",
    PLATFORM_IOS: "ios",
}

# app类型
PERF_APP_TYPE_PERF = 1
PERF_APP_TYPE_STANDARD = 2
PERF_APP_TYPE_ASSIST = 3
PERF_APP_TYPE_MAP = {
    PERF_APP_TYPE_PERF: "性能包",
    PERF_APP_TYPE_STANDARD: "基准包",
    PERF_APP_TYPE_ASSIST: "辅助包",
}

# 用例执行状态
CASE_RUN_STATUS_PENDING = 0
CASE_RUN_STATUS_RUNNING = 1
CASE_RUN_STATUS_RETRYING = 2
CASE_RUN_STATUS_SUCCESS = 3
CASE_RUN_STATUS_FAILED = 4
CASE_RUN_STATUS_CANCELED = 5
CASE_RUN_STATUS_TIMEOUT = 6


CASE_RUN_STATUS_MAP = {
    CASE_RUN_STATUS_PENDING: "等待执行",
    CASE_RUN_STATUS_RUNNING: "执行中", 
    CASE_RUN_STATUS_RETRYING: "重试中",
    CASE_RUN_STATUS_SUCCESS: "执行成功",
    CASE_RUN_STATUS_FAILED: "执行失败",
    CASE_RUN_STATUS_CANCELED: "已取消",
    CASE_RUN_STATUS_TIMEOUT: "超时",
}

VERSION_TYPE_EXPERIMENT = 1  # 实验组
VERSION_TYPE_CONTROL = 2     # 对照组
VERSION_TYPE_MAP = {
    VERSION_TYPE_EXPERIMENT: "实验组",
    VERSION_TYPE_CONTROL: "对照组"
}


# 任务类型
TASK_TYPE_VERSION_REGRESSION = 1  # 版本回归
TASK_TYPE_LIBRA_EXPERIMENT = 2  # Libra实验
TASK_TYPE_MAP = {
    TASK_TYPE_VERSION_REGRESSION: "版本回归",
    TASK_TYPE_LIBRA_EXPERIMENT: "Libra实验"
}

# 性能采集工具类型
PERF_TOOL_TYPE_DS = 1  # DS性能采集工具
PERF_TOOL_TYPE_GAMEPERF = 2  # GamePerf性能采集工具
PERF_TOOL_TYPE_MAP = {
    PERF_TOOL_TYPE_DS: "DS",
    PERF_TOOL_TYPE_GAMEPERF: "GamePerf"
}

# Libra命中类型
LIBRA_HIT_TYPE_DID = 1  # DID命中
LIBRA_HIT_TYPE_UID = 2  # UID命中
LIBRA_HIT_TYPE_MAP = {
    LIBRA_HIT_TYPE_DID: "DID命中",
    LIBRA_HIT_TYPE_UID: "UID命中"
}

# 任务状态
TASK_STATUS_NOT_START = 0  # 未开始
TASK_STATUS_PENDING = 1  # 等待中
TASK_STATUS_RUNNING = 2  # 执行中
TASK_STATUS_SUCCESS = 3  # 执行成功
TASK_STATUS_FAILED = 4  # 执行失败
TASK_STATUS_RETRY_PENDING = 5  # 重试等待中
TASK_STATUS_RETRY_RUNNING = 6  # 重试执行中
TASK_STATUS_CANCELED = 7  # 已取消
TASK_STATUS_TIMEOUT = 8  # 已超时
TASK_STATUS_MAP = {
    TASK_STATUS_NOT_START: "未开始",
    TASK_STATUS_PENDING: "等待中", 
    TASK_STATUS_RUNNING: "执行中",
    TASK_STATUS_SUCCESS: "执行成功",
    TASK_STATUS_FAILED: "执行失败",
    TASK_STATUS_RETRY_PENDING: "重试等待中",
    TASK_STATUS_RETRY_RUNNING: "重试执行中",
    TASK_STATUS_CANCELED: "已取消",
    TASK_STATUS_TIMEOUT: "已超时",
}

# 任务状态对应的背景色
TASK_STATUS_COLOR_MAP = {
    TASK_STATUS_NOT_START: "#8C8C8C",  # 深灰色
    TASK_STATUS_PENDING: "#D9D9D9",    # 浅灰色
    TASK_STATUS_RUNNING: "#1890FF",    # 明亮的蓝色
    TASK_STATUS_SUCCESS: "#52C41A",    # 翠绿色
    TASK_STATUS_FAILED: "#F5222D",     # 鲜红色
    TASK_STATUS_RETRY_PENDING: "#FAAD14",  # 暖橙色
    TASK_STATUS_RETRY_RUNNING: "#FA8C16",  # 深橙色
    TASK_STATUS_CANCELED: "#595959",    # 深灰色
    TASK_STATUS_TIMEOUT: "#FF7A45",     # 橘红色
}

# 任务执行结果飞书消息-子任务模板
SUB_TASK_INFO_TEMPLATE = '**子任务%d**：%s<br>**子任务结果**：%s<br>**子任务执行时间**：%s<br><br>'

# 测试环境可以进行飞书消息通知者
LARK_CARD_TEST_REMINDER_LIST = ["maweijia.1211", "hejiabei.oxep", "yinyanting.2022"]

# 指标分类
METRIC_CATEGORY_DS = 1  # DS基础性能
METRIC_CATEGORY_GAMEPERF = 2  # GamePerf基础性能指标
METRIC_CATEGORY_TRACE = 3  # Trace指标
METRIC_CATEGORY_BYTEIO = 4  # ByteIO指标
METRIC_CATEGORY_MAP = {
    METRIC_CATEGORY_DS: "DS基础性能",
    METRIC_CATEGORY_GAMEPERF: "GamePerf基础性能指标",
    METRIC_CATEGORY_TRACE: "Trace指标",
    METRIC_CATEGORY_BYTEIO: "ByteIO指标"
}

# 指标类型
METRIC_TYPE_COMMON = 1  # 通用
METRIC_TYPE_ANDROID = 2  # Android专用
METRIC_TYPE_IOS = 3  # iOS专用
METRIC_TYPE_MAP = {
    METRIC_TYPE_COMMON: "通用",
    METRIC_TYPE_ANDROID: "Android",
    METRIC_TYPE_IOS: "iOS"
}
