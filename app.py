import uvicorn

from fastapi import Request
from __init__ import create_app
from proj_settings import settings
from utils.common.bytelog import byte_log

app = create_app()

# 响应requests前的公共拦截方法，此处目的为刷新logid + 打印request基本信息
@app.middleware("http")
async def before_handle_request(request: Request, call_next):
    body = await request.body()
    byte_log.info(f"{request.method} {request.url} \nheader={request.headers}\n params={request.query_params}\n body={body.decode()}\n", refresh=True)
    return await call_next(request)

if __name__ == "__main__":
    byte_log.info(f"app start: {settings.HOST}:{settings.PORT}")
    uvicorn.run(app="app:app", host=settings.HOST, port=settings.PORT, reload=settings.RELOAD)
