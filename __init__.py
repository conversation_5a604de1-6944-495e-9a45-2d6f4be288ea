from contextlib import asynccontextmanager

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from starlette import status
from starlette.middleware.cors import CORSMiddleware

from apis import api_router
from apis.test_manager.ci_daily_router import get_ci_daily
from handlers.tool_manager.ffmpeg_stream_handler import <PERSON><PERSON><PERSON><PERSON>
from configs import *
from proj_settings import deploy_cluster_env, DeployCluster
from proj_settings import settings
from schemas.request.test_manager.ci_daily_req import GetCiDailyDataParams
from apis.version_manager.version_detail_router import get_todo_version_checlist_scheduler_task


def create_app() -> FastAPI:
    # 等待其他组件启动完成
    app = FastAPI(description=settings.PROJECT_DESC, version=settings.PROJECT_VERSION, lifespan=lifespan)
    app.include_router(api_router, prefix=settings.API_PREFIX)
    register_middleware(app)
    register_static(app)
    register_exception(app)
    return app


def register_middleware(app):
    """
    跨域请求 -- https://fastapi.tiangolo.com/zh/tutorial/cors/
    """
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )


def register_static(app):
    """
    挂载静态文件 -- https://fastapi.tiangolo.com/zh/tutorial/static-files/
    """
    backend = os.path.dirname(os.path.abspath(__file__))
    app.mount("/static", StaticFiles(directory=os.path.join(backend, "static")))

    @app.get("/")
    async def read_index():
        return FileResponse(os.path.join(backend, "static", "index.html"))

    @app.exception_handler(404)
    async def not_found(request: Request, exc):
        accept = request.headers.get("accept")
        if not accept:
            return JSONResponse(content={"error": "Not found"}, status_code=exc.status_code)
        if exc.status_code == 404 and "text/html" in accept:
            return FileResponse(os.path.join(backend, "static", "index.html"))
        else:
            return JSONResponse(content={"error": "Not found"}, status_code=exc.status_code)


def register_exception(app):
    """
    全局异常捕获 -- https://fastapi.tiangolo.com/zh/tutorial/handling-errors/
    :param app:
    :return:
    """

    # 全局异常处理器
    @app.exception_handler(Exception)  # 这里捕获所有 Exception 类型的异常
    async def exception_handler(request, exc: Exception):
        # 根据需要，你可以在这里添加更多的逻辑，比如记录日志等
        return JSONResponse(
            status_code=exc.status_code if hasattr(exc, "status_code") else status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": str(exc)},
        )


@asynccontextmanager
async def lifespan(app):
    if deploy_cluster_env in [None, DeployCluster.CN_BOE, DeployCluster.CN_ONLINE]:
        # 启动调度器
        scheduler = AsyncIOScheduler()
        scheduler.add_job(get_ci_daily, "cron",
                          hour=12, minute=0, args=[GetCiDailyDataParams(pipeline_type=[0, 1], get_data_days=1)])
        scheduler.add_job(get_todo_version_checlist_scheduler_task, "cron",
                          hour=11, minute=0, day_of_week='0-4')
        scheduler.start()
        
    try:
        yield
    finally:
        # 应用关闭时，停止调度器
        scheduler.shutdown()