auto_attribution:
  - result_code: 8
    description: '日志丢失'
    fail_reason_type: 4
    fail_reason_str: '日志缺失'
    improvement_measure_str: '重试或观察'
  - result_code: 10
    description: 'rtt峰值高'
    fail_reason_type: 6
    fail_reason_str: 'rtt峰值超过2000'
    improvement_measure_str: '重试或观察'
  - result_code: 11
    description: 'rtt均值高'
    fail_reason_type: 6
    fail_reason_str: 'rtt均值超过500'
    improvement_measure_str: '重试或观察'
  - result_code: 16
    description: '丢包率高'
    fail_reason_type: 6
    fail_reason_str: '丢包70%大于2次或丢包50%大于3次'
    improvement_measure_str: '重试或观察'
  - result_code: 30
    description: '前提条件未达到'
    fail_reason_type: 4
    fail_reason_str: '用例前提条件未达到，终止测试'
    improvement_measure_str: '重试或观察'
  - result_code: 31
    description: 'vpaas配置设置存在冲突'
    fail_reason_type: 4
    fail_reason_str: 'vpaas配置设置存在冲突，终止测试'
    improvement_measure_str: '重试或观察'
  - result_code: 201
    description: '进房失败，未收到OnRoomStateChanged回调'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 202
    description: '进房时间久，joinRoom与OnRoomStateChanged间隔超过30s'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 203
    description: 'ICE暂时断开次数>=3'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 204
    description: '未收到远端进房成功回调onUserJoined'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 205
    description: '预期的编码方式与拉到的配置不一致'
    fail_reason_type: 4
    fail_reason_str: '环境问题'
    improvement_measure_str: '重试或观察'
  - result_code: 206
    description: '预期的解码方式与拉到的配置不一致'
    fail_reason_type: 4
    fail_reason_str: '环境问题'
    improvement_measure_str: '重试或观察'
  - result_code: 207
    description: '预期的svc配置与拉到的配svc置不一致'
    fail_reason_type: 4
    fail_reason_str: '环境问题'
    improvement_measure_str: '重试或观察'
  - result_code: 208
    description: '预期的codec与拉到的配置不一致'
    fail_reason_type: 4
    fail_reason_str: '环境问题'
    improvement_measure_str: '重试或观察'
  - result_code: 209
    description: '无视频上行流'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 210
    description: '无视频下行流'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 211
    description: '网络不好时，性能用例被网络的升降级影响'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 212
    description: '视频上行流上报，相邻的两次时间，间隔超过10s'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 213
    description: '视频下行流上报，相邻的两次时间，间隔超过10s'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 214
    description: '视频output，从realx decode pipeline接收到的帧=0数量 占比超过90%'
    fail_reason_type: 5
    fail_reason_str: '机器问题'
    improvement_measure_str: '重试或观察'
  - result_code: 215
    description: '视频下行流，bandwidth上报一直=0'
    fail_reason_type: 6
    fail_reason_str: '网络问题'
    improvement_measure_str: '重试或观察'
  - result_code: 216
    description: '设备评分小于8 不支持bytevc1 硬编'
    fail_reason_type: 5
    fail_reason_str: '机器问题'
    improvement_measure_str: '重试或观察'