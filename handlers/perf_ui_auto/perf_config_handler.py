'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-30 19:49:16
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_config_handler.py
Description: 性能配置处理器
'''
from typing import Optional
from dals.db.perf_ui_auto.perf_config_db import perf_config_db
from models.perf_ui_auto.perf_config_model import PerformanceConfig
from schemas.request.perf_ui_auto.perf_config_req import (
    CreateConfigRequest, UpdateConfigRequest, ConfigListRequest
)
from schemas.response.perf_ui_auto.perf_config_res import (
    PerfConfigListResponse, PerfConfigItem
)

class PerformanceConfigHandler:
    """性能配置处理器"""
    
    def __init__(self):
        self.db = perf_config_db
        
    def _convert_to_config_item(
        self,
        config: Optional[PerformanceConfig] = None
    ) -> Optional[PerfConfigItem]:
        """转换为配置项"""
        if not config:
            return None

        # 获取指标详情并创建映射
        metric_details = {
            m.id: {
                'metric_id': m.id,
                'metric_key': m.metric_key,
                'metric_name': m.metric_name,
                'metric_type': m.metric_type,
                'metric_category': m.metric_category,
                'metric_unit': m.metric_unit,
                'metric_desc': m.metric_desc,
                'query_conditions': m.query_conditions,
            } for m in self.db.get_metrics_by_ids([m.get('metric_id') for m in config.metrics])
        }

        # 使用模型验证和转换
        config_dict = {
            "id": config.id,
            "business_id": config.business_id,
            "config_name": config.config_name,
            "metrics": [{
                **metric_details[m['metric_id']],
                'is_important': m.get('is_important'),
                'comparison_type': m.get('comparison_type'),
                'value_type': m.get('value_type'),
                'threshold_value': m.get('threshold_value')
            } for m in config.metrics if m['metric_id'] in metric_details],
            "creator": config.creator,
            "create_time": config.create_time,
            "update_time": config.update_time
        }

        return PerfConfigItem.model_validate(config_dict)
        
    def get_all_configs(self, params: ConfigListRequest) -> PerfConfigListResponse:
        """
        获取配置列表
        Args:
            params: 查询参数
        Returns:
            PerfConfigListResponse: 配置列表响应
        """
        try:
            # 查询配置列表
            total, configs = self.db.get_all_configs(params)

            # 转换配置列表，使用完整的配置信息
            config_list = []
            for config in configs:
                # 获取指标详情
                metric_details = {
                    m.id: {
                        'metric_id': m.id,
                        'metric_key': m.metric_key,
                        'metric_name': m.metric_name,
                        'metric_type': m.metric_type,
                        'metric_category': m.metric_category,
                        'metric_unit': m.metric_unit,
                        'metric_desc': m.metric_desc,
                        'query_conditions': m.query_conditions,
                    } for m in self.db.get_metrics_by_ids([m.get('metric_id') for m in config.metrics])
                }

                config_dict = {
                    "id": config.id,
                    "business_id": config.business_id,
                    "config_name": config.config_name,
                    "metrics": [{
                        **metric_details[m['metric_id']],
                        'is_important': m.get('is_important'),
                        'comparison_type': m.get('comparison_type'),
                        'value_type': m.get('value_type'),
                        'threshold_value': m.get('threshold_value')
                    } for m in config.metrics if m['metric_id'] in metric_details],
                    "creator": config.creator,
                    "create_time": config.create_time,
                    "update_time": config.update_time
                }
                config_list.append(PerfConfigItem.model_validate(config_dict))

            return PerfConfigListResponse(
                page=params.page,
                page_size=params.page_size,
                total=total,
                items=config_list
            )
        except Exception as e:
            return PerfConfigListResponse(
                page=params.page,
                page_size=params.page_size,
                total=0,
                items=[]
            )
            
    def get_config(self, config_id: int) -> Optional[PerfConfigItem]:
        """
        获取配置详情
        Args:
            config_id: 配置ID
        Returns:
            Optional[PerfConfigItem]: 配置项
        """
        try:
            config = self.db.get_config(config_id)
            return self._convert_to_config_item(config=config)
        except Exception as e:
            return None

    def create_config(self, params: CreateConfigRequest) -> Optional[PerfConfigItem]:
        """
        创建配置
        Args:
            params: 创建配置请求
        Returns:
            Optional[PerfConfigItem]: 配置项
        """
        try:
            config = self.db.create_config(params)
            return self._convert_to_config_item(config=config)
        except Exception as e:
            return None

    def update_config(self, params: UpdateConfigRequest) -> Optional[PerfConfigItem]:
        """
        更新配置
        Args:
            params: 更新配置请求
        Returns:
            Optional[PerfConfigItem]: 配置项
        """
        try:
            config = self.db.update_config(params)
            return self._convert_to_config_item(config=config)
        except Exception as e:
            return None

    def delete_config(self, config_id: int) -> bool:
        """
        删除配置
        Args:
            config_id: 配置ID
        Returns:
            bool: 是否删除成功
        """
        try:
            return self.db.delete_config(config_id)
        except Exception as e:
            return False

# 创建处理器实例
perf_config_handler = PerformanceConfigHandler()
