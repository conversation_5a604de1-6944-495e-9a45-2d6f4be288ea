'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_device_handler.py
Description: 
'''
def upload_perf_device(data):
    """
    处理注册设备
    @param data: 注册设备数据列表
    @return: 注册设备数据
    """
    if not data:
        raise Exception("注册设备数据不能为空")
    
    # 确保输入数据是列表类型
    if not isinstance(data, list):
        raise Exception("注册设备数据必须是列表格式")
    
    # 验证每个设备数据的必要字段
    required_fields = ['device_id', 'device_name', 'brand', 'model', 'sys_type', 'sys_version']
    for device in data:
        missing_fields = [field for field in required_fields if field not in device]
        if missing_fields:
            raise Exception(f"设备数据缺少必要字段: {', '.join(missing_fields)}")
    
    return data


def get_device_list(page: int, page_size: int, total: int, items: list):
    """
    处理设备列表
    @param page: 页码
    @param page_size: 每页数量
    @param total: 总数
    @param items: 设备列表
    @return: 设备列表响应数据
    """
    # 将设备对象转换为字典
    device_items = []
    for item in items:
        device_dict = {
            'id': item.id,
            'client_id': item.client_id,
            'name': item.name,
            'udid': item.udid,
            'model': item.model,
            'sys_type': item.sys_type,
            'sys_version': item.sys_version,
            'brand': item.brand,
            'resolution': item.resolution,
            'connect_type': item.connect_type,
            'state': item.state,
            'ip': item.ip,
            'serial_port': item.serial_port,
            'is_occupied': item.is_occupied,
            'user': item.user or '',
            'owner': item.owner or '',
            'create_time': item.create_time,
            'update_time': item.update_time
        }
        device_items.append(device_dict)

    data = {
        "page": page,
        "page_size": page_size,
        "total": total,
        "items": device_items,
    }
    return data


def get_device_detail(device_info):
    """
    处理设备详情数据
    :param device_info: 原始设备信息
    :return: 处理后的设备详情
    """
    if not device_info:
        return None
        
    # 保持原有的数据处理逻辑
    return device_info


def get_available_devices(devices):
    """
    处理可用设备列表
    @param devices: 设备列表
    @return: 设备列表
    """
    if not devices:
        raise Exception("没有找到符合条件的可用设备")
    return devices

