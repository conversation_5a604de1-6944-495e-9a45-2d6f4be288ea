'''
Author: hejiabei.oxep <EMAIL>
Date: 2025-01-16 19:31:54
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_stats_handler.py
Description: 性能统计业务逻辑处理
'''
from typing import List, Dict, Any, Optional
from datetime import date

from dals.db.perf_ui_auto.perf_stats_db import PerfStatsDB
from schemas.request.perf_ui_auto.perf_stats_req import (
    BusinessCaseStatsRequest,
    TaskExecutionStatsRequest,
    CaseExecutionStatsRequest,
    BusinessCaseTrendRequest,
    TaskExecutionTrendRequest,
    CaseExecutionTrendRequest
)
from schemas.response.perf_ui_auto.perf_stats_res import (
    BusinessCaseStatsResponse,
    TaskExecutionStatsResponse,
    CaseExecutionStatsResponse,
    BusinessCaseTrendResponse,
    TaskExecutionTrendResponse,
    CaseExecutionTrendResponse
)
from utils.common.bytelog import byte_log


class PerfStatsHandler:
    def __init__(self):
        self.perf_stats_db = PerfStatsDB()

    def handle_business_case_stats(self, request: BusinessCaseStatsRequest) -> Dict[str, Any]:
        """
        处理业务用例统计请求
        @param request: 请求参数
        @return: 处理后的响应数据
        """
        try:
            # 获取统计数据
            stats_data = self.perf_stats_db.get_business_case_stats(
                business_id=request.business_id,
                start_date=request.start_date,
                end_date=request.end_date
            )
            
            # 格式化响应数据
            response_data = {
                "total": len(stats_data),
                "items": stats_data
            }
            
            return response_data
        except Exception as e:
            byte_log.error(f"处理业务用例统计失败: {str(e)}")
            return {"total": 0, "items": []}

    def handle_task_execution_stats(self, request: TaskExecutionStatsRequest) -> Dict[str, Any]:
        """
        处理任务执行统计请求
        @param request: 请求参数
        @return: 处理后的响应数据
        """
        try:
            # 获取统计数据
            stats_data = self.perf_stats_db.get_task_execution_stats(
                business_id=request.business_id,
                start_date=request.start_date,
                end_date=request.end_date
            )
            
            # 格式化响应数据
            response_data = {
                "total": len(stats_data),
                "items": stats_data
            }
            
            return response_data
        except Exception as e:
            byte_log.error(f"处理任务执行统计失败: {str(e)}")
            return {"total": 0, "items": []}

    def handle_case_execution_stats(self, request: CaseExecutionStatsRequest) -> Dict[str, Any]:
        """
        处理用例执行统计请求
        @param request: 请求参数
        @return: 处理后的响应数据
        """
        try:
            # 获取统计数据
            stats_data = self.perf_stats_db.get_case_execution_stats(
                business_id=request.business_id,
                case_id=request.case_id,
                start_date=request.start_date,
                end_date=request.end_date
            )
            
            # 格式化响应数据
            response_data = {
                "total": len(stats_data),
                "items": stats_data
            }
            
            return response_data
        except Exception as e:
            byte_log.error(f"处理用例执行统计失败: {str(e)}")
            return {"total": 0, "items": []}

    def handle_business_case_trend(self, request: BusinessCaseTrendRequest) -> Dict[str, Any]:
        """
        处理业务用例趋势请求
        @param request: 请求参数
        @return: 处理后的响应数据
        """
        try:
            # 获取趋势数据
            trend_data = self.perf_stats_db.get_business_case_trend(
                business_id=request.business_id,
                days=request.days
            )
            
            # 格式化响应数据
            response_data = {
                "total": len(trend_data),
                "items": trend_data
            }
            
            return response_data
        except Exception as e:
            byte_log.error(f"处理业务用例趋势失败: {str(e)}")
            return {"total": 0, "items": []}

    def handle_task_execution_trend(self, request: TaskExecutionTrendRequest) -> Dict[str, Any]:
        """
        处理任务执行趋势请求
        @param request: 请求参数
        @return: 处理后的响应数据
        """
        try:
            # 获取趋势数据
            trend_data = self.perf_stats_db.get_task_execution_trend(
                business_id=request.business_id,
                days=request.days
            )
            
            # 格式化响应数据
            response_data = {
                "total": len(trend_data),
                "items": trend_data
            }
            
            return response_data
        except Exception as e:
            byte_log.error(f"处理任务执行趋势失败: {str(e)}")
            return {"total": 0, "items": []}

    def handle_case_execution_trend(self, request: CaseExecutionTrendRequest) -> Dict[str, Any]:
        """
        处理用例执行趋势请求
        @param request: 请求参数
        @return: 处理后的响应数据
        """
        try:
            # 获取趋势数据
            trend_data = self.perf_stats_db.get_case_execution_trend(
                business_id=request.business_id,
                case_id=request.case_id,
                days=request.days
            )
            
            # 格式化响应数据
            response_data = {
                "total": len(trend_data),
                "items": trend_data
            }
            
            return response_data
        except Exception as e:
            byte_log.error(f"处理用例执行趋势失败: {str(e)}")
            return {"total": 0, "items": []}

    def get_stats_summary(self, business_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取统计概览数据
        @param business_id: 业务线ID
        @return: 概览数据
        """
        try:
            # 获取各类统计数据
            case_stats = self.perf_stats_db.get_business_case_stats(business_id=business_id)
            task_stats = self.perf_stats_db.get_task_execution_stats(business_id=business_id)
            
            # 计算汇总数据
            total_cases = sum(item['total_cases'] for item in case_stats)
            total_active_cases = sum(item['active_cases'] for item in case_stats)
            total_task_executions = sum(item['total_executions'] for item in task_stats)
            total_success_executions = sum(item['success_executions'] for item in task_stats)
            
            overall_success_rate = 0.0
            if total_task_executions > 0:
                overall_success_rate = round(total_success_executions * 100.0 / total_task_executions, 2)
            
            summary_data = {
                "total_cases": total_cases,
                "total_active_cases": total_active_cases,
                "total_task_executions": total_task_executions,
                "total_success_executions": total_success_executions,
                "overall_success_rate": overall_success_rate,
                "business_count": len(case_stats)
            }
            
            return summary_data
        except Exception as e:
            byte_log.error(f"获取统计概览失败: {str(e)}")
            return {
                "total_cases": 0,
                "total_active_cases": 0,
                "total_task_executions": 0,
                "total_success_executions": 0,
                "overall_success_rate": 0.0,
                "business_count": 0
            }


# 创建全局实例
perf_stats_handler = PerfStatsHandler()
