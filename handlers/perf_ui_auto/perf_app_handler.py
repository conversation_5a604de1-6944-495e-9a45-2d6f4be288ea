import json

from dals.db.perf_ui_auto.perf_app_db import perf_app_db
from models.perf_ui_auto.perf_app_model import PerfUiAutoAppGroup
from schemas.request.perf_ui_auto.perf_app_req import GetAppListParams
from schemas.page import PageSchema


def handle_app_list_data(page, total, items):
    new_items = []
    for row in items:
        row_value = {}
        for i in range(len(row._fields)):
            field = row._fields[i]
            data = row._data[i]
            row_value[field] = data
        new_items.append(row_value)
        
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "items": new_items
    }
    return data

def handle_insert_app_data(item):
    data = item
    return data

def handle_update_app_data(item):
    data = item
    return data

def handle_delete_app(success: bool):
    """
    处理删除应用的返回数据
    @param success: 删除是否成功
    @return: 处理后的结果
    """
    if not success:
        raise Exception("删除应用失败")
    return True

def handle_app_group_list_data(page, total, items):
    new_items = []
    for row in items:
        row_value = {}
        for i in range(len(row._fields)):
            field = row._fields[i]
            data = row._data[i]
            row_value[field] = data
        new_items.append(row_value)
    
    for item in new_items:
        item["android_perf_app_id_list"] = json.loads(item["android_perf_app_id_list"])
        item["android_assist_app_id_list"] = json.loads(item["android_assist_app_id_list"])
        item["ios_perf_app_id_list"] = json.loads(item["ios_perf_app_id_list"])
        item["ios_assist_app_id_list"] = json.loads(item["ios_assist_app_id_list"])
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "items": new_items
    }
    return data

def handle_insert_app_group_data(item):
    data = item
    return data

def handle_update_app_group_data(item):
    data = item
    return data

def handle_app_group_detail_data(item:PerfUiAutoAppGroup):
    tmp_map = {
        "android_perf_app_id_list": json.loads(item.android_perf_app_id_list),
        "android_assist_app_id_list": json.loads(item.android_assist_app_id_list),
        "ios_perf_app_id_list": json.loads(item.ios_perf_app_id_list),
        "ios_assist_app_id_list": json.loads(item.ios_assist_app_id_list)
    }
    data = {
        "id": item.id,
        "business_id": item.business_id,
        "name": item.name,
        "version": item.version,
        "create_time": item.create_time,
        "update_time": item.update_time
    }
    for key, value in tmp_map.items():
        if len(value) > 0:
            params = GetAppListParams()
            params.id = value
            page = PageSchema(page=1, page_size=len(value))
            total, result = perf_app_db.get_app_list(page=page, params=params)
            result_dict = {row[0]: row for row in result}  # row[0]是id
            ordered_result = [result_dict[app_id] for app_id in value if app_id in result_dict]
            data[key] = ordered_result
        else:
            data[key] = []
    return data

def get_version_list_data(page, total, items):
    """
    处理版本列表数据
    @param page: 分页参数
    @param total: 总数
    @param items: 版本列表项
    @return: 格式化后的数据
    """
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "version_list": items
    }
    return data

def handle_delete_app_group(success: bool):
    """
    处理删除应用组的返回数据
    @param success: 删除是否成功
    @return: 处理后的结果
    """
    if not success:
        raise Exception("删除应用组失败")
    return True