import ast
import os
import re
import subprocess
import shutil
from dals.db.business.business_db import business_db
from dals.db.perf_ui_auto.perf_case_db import perf_case_db
from proj_settings import deploy_cluster_env, DeployCluster

from utils.common.bytelog import byte_log


def get_case_list_handle(params,page,page_size):  # 读取数据表，并处理返回case列表

    total,case_list=perf_case_db.get_case_list_by_page(params,page=page,page_size=page_size)
    return {
        'cases': case_list
    }, True


def handle_update_case():
    current_path = os.getcwd()
    temp_path = os.path.join(current_path, "temp")
    repo_url = "https://yinyanting.2022:<EMAIL>/bytertc_i18n/global_business_perf.git"
    
    # 根据环境选择不同的分支
    git_branch = "master"  # 默认使用master分支
    if deploy_cluster_env == DeployCluster.CN_BOE:
        git_branch = "boe"
        byte_log.info(f"当前环境为CN_BOE，使用boe分支")
    elif deploy_cluster_env == DeployCluster.CN_ONLINE:
        git_branch = "master"
        byte_log.info(f"当前环境为CN_ONLINE，使用master分支")
    else:
        git_branch = "boe"
        byte_log.info(f"当前环境为其他环境，默认使用boe分支")
    
    try:
        # 如果 temp 目录存在，删除
        if os.path.exists(temp_path):
            shutil.rmtree(temp_path)
        os.makedirs(temp_path)
        # 克隆仓库，指定分支
        result = subprocess.run([
            "git", "clone", "-b", git_branch, repo_url
        ], cwd=temp_path, capture_output=True, text=True)
        if result.returncode != 0:
            byte_log.info(f"git clone 分支 {git_branch} 失败，错误码为{result.returncode}, 输出: {result.stdout}, 错误: {result.stderr}")
            return {
                "message": f"git clone 分支 {git_branch} 失败，错误码为{result.returncode}"
            }, False
        byte_log.info(f"拉取用例仓库 {git_branch} 分支成功")
    except Exception as e:
        byte_log.info(f"拉取用例仓库失败: {e}")
        return {
            "message": f"拉取用例仓库失败: {e}"
        }, False

    case_info_list = list()
    # 常量定义地址,获取常量
    file_path_const_define = f"{current_path}/temp/global_business_perf/defines.py"
    constants = get_named_constants(file_path_const_define)

    for root, dirs, files in os.walk(f"{current_path}/temp/global_business_perf/business"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if "cases" not in file_path:
                    continue
                try:
                    node = get_ast_tree(file_path=file_path)
                    for class_element in ast.walk(node):
                        case_info = dict()
                        # 对于每个类
                        if isinstance(class_element, ast.ClassDef):
                            # 获取类的基类和装饰器，判断是否是拥挤
                            base_classes = [base.id if isinstance(base, ast.Name) else ast.dump(base)
                                            for base in class_element.bases]
                            decorators = get_decorator_names(class_element=class_element)

                            if judge_class_test(decorators=decorators, base_classes=base_classes):
                                case_info["base_classes"] = base_classes
                                case_info["decorators"] = decorators
                                # 类的名称
                                case_info["case_name"] = class_element.name
                                # 类的地址
                                case_info["case_dir"] = re.sub(f"{current_path}/temp",'', os.path.join(root, file))
                                # 类 静态参数
                                static_params = get_static_params(class_element=class_element,constants=constants)
                                case_info["static_params"] = static_params
                                # 最后把该类的信息append进list
                                case_info_list.append(case_info)
                except Exception as e:
                    byte_log.info(f"Error parsing {file_path}: {e}")
                    case_info_list.append({
                        "error_case":f'{file_path}',
                        "error_msg":f'{e}'
                    })
    try:
        final_case_info_list = deal_case_list(case_info_list)
        perf_case_db.update_case_list_db(new_case_info_list=final_case_info_list)
        return {
            'cases': final_case_info_list  # 调试时临时加上，后续可能删除
        }, True
    except Exception as e:
        byte_log.info(f"处理失败， {e}")
        return {
            "message": f"用例信息处理失败，{e}"
        }, False


def deal_case_list(origin_case_info_list):
    """
    用于处理case 格式，
    """
    after_case_info_list = list()

    business_dict = get_business_id_dict()
    count = 1
    for case_info in origin_case_info_list:
        if case_info.get("error_case"):
            continue
        temp_case_info = dict()
        dir_name = case_info.get("case_dir").split("/")[3]
        temp_case_info["business_id"] = business_dict.get(dir_name,0)
        temp_case_info["case_name"] = case_info.get("case_name","none_name")
        temp_case_info["case_level"] = case_info.get("case_level","none_case_level")
        temp_case_info["owner"] = case_info.get("static_params").get("owner","none_owner")
        temp_case_info["timeout"] = case_info.get("static_params").get("timeout",-1)
        temp_case_info["platform"] = get_str_by_list(case_info.get("static_params").get("platform",""))
        temp_case_info["device_count"] = case_info.get("static_params").get("device_count",-1)
        temp_case_info["title"] = case_info.get("static_params").get("title","")
        temp_case_info["description"] = case_info.get("static_params").get("description","")
        temp_case_info["tags"] = get_str_by_list(case_info.get("static_params").get("tags",[]))
        temp_case_info["dir"] = case_info.get("case_dir")+"/"+case_info.get("case_name")
        after_case_info_list.append(temp_case_info)
        count += 1
    return after_case_info_list

def get_business_id_dict():
    """
    获取business dir> business_id的dict
    """
    business_dict = dict()
    total, items = business_db.fetch_all()
    for item in items:
        if item.business_dir:
            business_dict[item.business_dir] = item.id
    return business_dict



def get_str_by_list(value_list):
    re_str = ""
    for value in value_list:
        re_str += str(value)
    return re_str

def judge_class_test(decorators,base_classes):  # 判断类是否为case
    # for decorator in decorators:
    #     if decorator == "DataDriven":
    for base_class in base_classes:
        if "TestBase" in base_class:
            return True
    return False
def get_decorator_names(class_element):
    decorators = list()
    for decorator in class_element.decorator_list:
        if isinstance(decorator, ast.Name):
            decorators.append(decorator.id)
        elif isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Name):
                decorators.append(decorator.func.id)
    return decorators


def get_static_params(class_element,constants):   # 获取类的静态参数
    static_params = dict()
    for stmt in class_element.body:
        if isinstance(stmt, ast.Assign):
            for target in stmt.targets:
                if isinstance(target, ast.Name):
                    value = stmt.value
                    if isinstance(value, ast.Constant):
                        static_params[target.id] = ast.literal_eval(value)
                    elif isinstance(value, ast.Name):
                        static_params[target.id] = constants.get(value.id,value.id)
                    elif isinstance(value, ast.List):
                        static_params[target.id] = parse_ast_list(value,constants)
                    # else:
                    #     static_params[target.id] = None
    return static_params

def parse_ast_list(value_node,constants):  # 解析value中为list的部分
    value_list = list()
    for element in value_node.elts:

        if isinstance(element, ast.Constant):
            value_list.append(element.value)
        elif isinstance(element, ast.Name):
            value_list.append(constants.get(element.id,element.id))
        elif isinstance(element, ast.Attribute):
            value_list.append(constants.get(f"{element.value.id}.{element.attr}",str(element.attr)))
        elif isinstance(element, ast.BinOp):
            value_list.append(ast.dump(element))
        value_list.append(",")
    return value_list[0:-1]
def get_ast_tree(file_path):
    with open(file_path, 'r') as file:
        source_code = file.read()
        return ast.parse(source_code)

def get_named_constants(file_path):  # 获取define 文件中的常量情况
    tree = get_ast_tree(file_path)
    named_constants = {}
    for node in ast.walk(tree):
        if isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Name) and target.id.isupper():
                    value = node.value
                    if isinstance(value, ast.Constant):
                        named_constants[target.id] = ast.literal_eval(value)
        # 如果是类的话,获取类的静态成员，也是常量
        if isinstance(node, ast.ClassDef):
            for stmt in node.body:
                if isinstance(stmt, ast.Assign):
                    for target in stmt.targets:
                        if isinstance(target, ast.Name):
                            value = stmt.value
                            if isinstance(value, ast.Constant):
                                named_constants[target.id] = ast.literal_eval(value)
                                named_constants[f"{node.name}.{target.id}"] = ast.literal_eval(value)
                            else:
                                named_constants[target.id] = None
                                named_constants[f"{node.name}.{target.id}"] = None
                    # if isinstance(value, ast.Dict):
                    #     named_constants[target.id] = ast.literal_eval(value)
    return named_constants

def get_case_detail_handle(case_id: int):
    case_detail = perf_case_db.get_case_detail(case_id=case_id)
    if case_detail:
        return {
            'case': case_detail
        }, True
    return None, False