from copy import deepcopy

def handle_account_list_data(page, total, items):
    """
    处理账号列表数据
    @param page: 分页参数
    @param total: 总数
    @param items: 账号列表
    @return: 处理后的数据
    """
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "items": items
    }
    return data


def handle_account_detail(item):
    """
    处理单个账号详情
    @param item: 账号对象
    @return: 处理后的账号详情
    """
    if not item:
        raise Exception("账号不存在")
    return item

def handle_add_account(item):
    """
    处理添加账号的返回数据
    @param item: 添加的账号对象
    @return: 处理后的账号数据
    """
    if not item:
        raise Exception("添加账号失败")
    return item


def handle_update_account(item):
    """
    处理更新账号的返回数据
    @param item: 更新后的账号对象
    @return: 处理后的账号数据
    """
    if not item:
        raise Exception("更新账号失败")
    return item

def handle_delete_account(success: bool):
    """
    处理删除账号的返回数据
    @param success: 删除是否成功
    @return: 处理后的结果
    """
    if not success:
        raise Exception("删除账号失败")
    return True
