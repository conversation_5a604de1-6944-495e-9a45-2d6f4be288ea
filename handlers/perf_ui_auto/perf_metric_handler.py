'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_metric_handler.py
Description: 性能指标处理模块
'''
from typing import List, Optional
from utils.common.bytelog import byte_log

from dals.db.perf_ui_auto.perf_metric_db import perf_metric_db
from schemas.request.perf_ui_auto.perf_metric_req import CreateMetricRequest, UpdateMetricRequest, MetricListRequest, BatchCreateMetricRequest
from schemas.response.perf_ui_auto.perf_metric_res import PerfMetricInfo, MetricResponse, MetricListResponse, BatchMetricResponse

class PerformanceMetricHandler:
    def __init__(self):
        self.db = perf_metric_db

    def create_metric(self, params: CreateMetricRequest) -> MetricResponse:
        """
        创建性能指标
        @param params: 创建指标参数
        @return: MetricResponse
        """
        try:
            metric = self.db.create_metric(params)
            return MetricResponse(metric=PerfMetricInfo.model_validate(metric))
        except Exception as e:
            byte_log.error(f"创建性能指标失败: {str(e)}")
            return MetricResponse(metric=None)

    def batch_create_metrics(self, params: BatchCreateMetricRequest) -> BatchMetricResponse:
        """
        批量创建性能指标
        @param params: 批量创建指标参数
        @return: BatchMetricResponse
        """
        try:
            success_metrics, failed_metrics = self.db.batch_create_metrics(params)
            return BatchMetricResponse(
                success_count=len(success_metrics),
                failed_count=len(failed_metrics),
                success_metrics=[PerfMetricInfo.model_validate(m) for m in success_metrics],
                failed_metrics=failed_metrics
            )
        except Exception as e:
            byte_log.error(f"批量创建性能指标失败: {str(e)}")
            return BatchMetricResponse(
                success_count=0,
                failed_count=len(params.metrics),
                success_metrics=[],
                failed_metrics=[{"metric_key": m.metric_key, "error_msg": str(e)} for m in params.metrics]
            )

    def get_metric(self, metric_id: int) -> MetricResponse:
        """
        获取单个性能指标
        @param metric_id: 指标ID
        @return: MetricResponse
        """
        try:
            metric = self.db.get_metric(metric_id)
            if not metric:
                return MetricResponse(metric=None)
            return MetricResponse(metric=PerfMetricInfo.model_validate(metric))
        except Exception as e:
            byte_log.error(f"获取性能指标失败: {str(e)}")
            return MetricResponse(metric=None)

    def get_metrics(self, params: MetricListRequest) -> MetricListResponse:
        """
        获取性能指标列表
        @param params: 查询参数
        @return: MetricListResponse
        """
        try:
            total, metrics = self.db.get_metrics(params)
            return MetricListResponse(
                page=params.page,
                page_size=params.page_size,
                total=total,
                items=[PerfMetricInfo.model_validate(m) for m in metrics]
            )
        except Exception as e:
            byte_log.error(f"获取性能指标列表失败: {str(e)}")
            return MetricListResponse(
                page=params.page,
                page_size=params.page_size,
                total=0,
                items=[]
            )

    def update_metric(self, params: UpdateMetricRequest) -> MetricResponse:
        """
        更新性能指标
        @param params: 更新参数，包含指标ID和更新内容
        @return: MetricResponse
        """
        try:
            metric = self.db.update_metric(params)
            if not metric:
                return MetricResponse(metric=None)
            return MetricResponse(metric=PerfMetricInfo.model_validate(metric))
        except Exception as e:
            byte_log.error(f"更新性能指标失败: {str(e)}")
            return MetricResponse(metric=None)

    def delete_metric(self, metric_id: int) -> MetricResponse:
        """
        删除性能指标
        @param metric_id: 指标ID
        @return: MetricResponse
        """
        try:
            success = self.db.delete_metric(metric_id)
            if not success:
                return MetricResponse(metric=None)
            return MetricResponse(metric=None)
        except Exception as e:
            byte_log.error(f"删除性能指标失败: {str(e)}")
            return MetricResponse(metric=None)

perf_metric_handler = PerformanceMetricHandler()
