'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-02 16:03:02
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_task_handler.py
Description: 
'''
from datetime import datetime

from dals.db.perf_ui_auto.perf_data_db import get_decimal_to_float
from dals.db.perf_ui_auto.perf_task_db import perf_task_db
from dals.db.perf_ui_auto.perf_case_db import perf_case_db
from dals.db.perf_ui_auto.perf_config_db import perf_config_db
from handlers.perf_ui_auto.perf_config_handler import perf_config_handler
from dals.redis.perf_ui_auto.perf_request_redis import perf_request_redis
from dals.redis.perf_ui_auto.perf_task_redis import perf_task_redis
from defines.perf_ui_auto_define import *
from schemas.request.perf_ui_auto.perf_task_req import UpdateTaskStatusParams, UpdateSubTaskStatusParams
from utils.common.bytelog import byte_log
from utils.common.response import response_success

from copy import deepcopy
from typing import List, Any
from schemas.response.perf_ui_auto.perf_task_res import (
    UploadPerfCaseRunDetailItem,
    PerfSubTaskRes,
    PerfTaskListRes, PerfTaskRes, PerfRequestRes
)

def get_upload_perf_case_run_detail(item):
    # 支持version_type字段
    return UploadPerfCaseRunDetailItem.model_validate(item).model_dump(exclude_none=True)

def handle_task_list(page, total, items):
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "items": items
    }
    return PerfTaskListRes.model_validate(data).model_dump()

def handle_task_data(task_item: Any, sub_task_items: List[Any]) -> dict:
    """处理任务数据，使用Pydantic模型进行验证和转换"""
    # 将子任务列表转换为字典列表
    sub_tasks = [PerfSubTaskRes.model_validate(sub_task).model_dump() for sub_task in sub_task_items]
    
    # 构造完整的任务数据
    task_data = PerfTaskRes.model_validate({
        **task_item.__dict__,
        "sub_tasks": sub_tasks
    })
    
    return task_data.model_dump()

def handle_task_data_all(task_item_tuple, sub_task_item_tuple_list):
    task_item, business_item, client_item, config_item, experiment_item = task_item_tuple
    new_task_item = vars(deepcopy(task_item))
    new_task_item["business"] = business_item
    new_task_item["client"] = client_item

    # 转换配置为完整的配置信息
    if config_item:
        full_config = perf_config_handler._convert_to_config_item(config_item)
        new_task_item["config"] = full_config
    new_task_item["experiment"] = experiment_item
    app_group = None
    sub_tasks = []
    for sub_task_item_tuple in sub_task_item_tuple_list:
        sub_task_item, device_item, app_group_item, account_item = sub_task_item_tuple
        new_sub_task_item = vars(deepcopy(sub_task_item))
        new_sub_task_item["perf_device"] = device_item
        new_sub_task_item["app_group"] = app_group_item
        new_sub_task_item["perf_account"] = account_item
        case_id_list = new_sub_task_item.get("case_id_list", [])
        case_list = perf_case_db.get_cases_by_ids(case_id_list)
        new_sub_task_item["case_list"] = case_list

        app_group = app_group_item
        sub_tasks.append(new_sub_task_item)

    new_task_item["sub_tasks"] = sub_tasks
    new_task_item["app_group"] = app_group
    return new_task_item


def handle_sub_task_data(item: Any) -> dict:
    """处理子任务数据"""
    return PerfSubTaskRes.model_validate(item).model_dump()

def handle_sub_tasks_for_queue(sub_task_items: List[Any]) -> List[dict]:
    """处理子任务数据用于队列存储"""
    return [handle_sub_task_data(item) for item in sub_task_items]

def handle_request_data(item: Any) -> dict:
    """处理请求数据"""
    return PerfRequestRes.model_validate(item).model_dump()

def handle_requests_for_queue(request_items: List[Any]) -> List[dict]:
    """处理多个请求数据用于请求队列存储"""
    return [handle_request_data(item) for item in request_items]

def get_request_items_by_task_id(task_ids: List[int]) -> List[dict]:
    """根据任务ID转化为请求数据"""
    request_items = []
    for task_id in task_ids:
        request_items.append({
            "event_key": "perf_request_cancel_task",
            "request_body": {
                "task_id": task_id
            },
            "request_time": datetime.now().isoformat()
        })
    return request_items

def handle_task_status_data(item: Any) -> dict:
    """处理任务状态数据"""
    return PerfTaskRes.model_validate(item).model_dump(exclude={"sub_tasks"})




def handle_sub_task_case_run_detail_list(page, total, item_tuple_list):
    case_run_detail_list = []
    for item_tuple in item_tuple_list:
        case_run_detail_item, case_item, app_item, device_item = item_tuple
        new_item = vars(deepcopy(case_run_detail_item))
        new_item["case"] = case_item
        new_item["app"] = app_item
        new_item["device"] = device_item
        case_run_detail_list.append(new_item)
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "items": case_run_detail_list
    }
    return data

def handle_task_cancle_run(task_id,client_id):
    """ 如果是等待中的任务，把任务状态修改为未开始，并从队列中移除子任务；
    如果是运行中的任务，把任务状态修改为取消，并发送取消状态给请求队列，并从子任务队列中移除任务id符合的子任务；"""

    # 更新主任务状态为已取消
    task_item = perf_task_db.update_task_status_by_id(
        UpdateTaskStatusParams(
            id=task_id,
            status=TASK_STATUS_CANCELED
        )
    )
    # 获取所有需要改成已取消的的子任务（只要不是运行成功/失败,或者超时的子任务，统统改成已取消状态
    sub_task_items = perf_task_db.get_sub_tasks_by_task_id(task_id)
    failed_sub_tasks = [
        task for task in sub_task_items
        if task.status not in [TASK_STATUS_SUCCESS, TASK_STATUS_FAILED, TASK_STATUS_TIMEOUT]
    ]

    # 更新需要更改的子任务状态为已取消状态
    updated_sub_tasks = []
    for sub_task in failed_sub_tasks:
        updated_sub_task = perf_task_db.update_sub_task_status_by_id(
            UpdateSubTaskStatusParams(id=sub_task.id, status=TASK_STATUS_CANCELED)
        )
        if updated_sub_task:
            updated_sub_tasks.append(updated_sub_task)

    # 把所有的子任务从性能任务队列中移除
    perf_task_redis.remove_sub_tasks_by_task_id(client_id=client_id, task_id=task_id)
    # 把取消当前任务的消息发送给请求队列
    requests_items = get_request_items_by_task_id([task_id])
    # [PerfRequestRes(**requests_item) for requests_item in requests_items]
    requests_data = handle_requests_for_queue(requests_items)
    data = all([perf_request_redis.push_request(client_id=client_id, request=request) for request in requests_data])
    return response_success(data=data)


def get_task_status_by_task_id(task_id):
    """
    Get task status by task id  根据taskid获取任务的db内状态
    :param task_id:任务id
    """
    single_result =  perf_task_db.get_task(task_id)
    data_dict = {k: get_decimal_to_float(v) for k, v in single_result.__dict__.items() if not k.startswith('_')}
    return data_dict.get('status')