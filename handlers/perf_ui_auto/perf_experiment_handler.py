'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-23 16:32:10
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_experiment_handler.py
Description: 实验处理模块
'''
from typing import List, Optional, Tuple
from utils.common.bytelog import byte_log
import asyncio

from dals.db.perf_ui_auto.perf_experiment_db import perf_experiment_db
from schemas.request.perf_ui_auto.perf_experiment_req import (
    CreateExperimentRequest,
    ListExperimentsRequest,
    UpdateExperimentRequest,
    DeleteExperimentRequest,
    GetExperimentDetailRequest,
    ViewFlightByVidRequest,
)
from schemas.response.perf_ui_auto.perf_experiment_res import (
    ExperimentItem,
    ExperimentListResponse,
)
from utils.openapi.libra_api import libra

class PerfExperimentHandler:
    def __init__(self):
        self.db = perf_experiment_db

    def create_experiment(self, params: CreateExperimentRequest) -> ExperimentItem:
        """
        创建实验
        @param params: 创建实验参数
        @return: ExperimentItem
        @raise: Exception 创建失败时抛出异常
        """
        try:
            experiment = self.db.create_experiment(params)
            if not experiment or not experiment.id:
                raise Exception("创建实验失败")
            return ExperimentItem.model_validate(experiment)
        except Exception as e:
            byte_log.error(f"创建实验失败: {str(e)}")
            raise Exception(f"创建实验失败: {str(e)}")

    def get_experiment_list(self, params: ListExperimentsRequest) -> Tuple[int, List[ExperimentItem]]:
        """
        获取实验列表
        @param params: 查询参数
        @return: 总数和实验列表
        @raise: Exception 查询失败时抛出异常
        """
        try:
            total, items = self.db.get_experiment_list(params)
            if items is None:
                raise Exception("获取实验列表失败")
            return total, [ExperimentItem.model_validate(item) for item in items]
        except Exception as e:
            byte_log.error(f"获取实验列表失败: {str(e)}")
            raise Exception(f"获取实验列表失败: {str(e)}")

    def update_experiment(self, params: UpdateExperimentRequest) -> ExperimentItem:
        """
        更新实验
        @param params: 更新参数
        @return: ExperimentItem
        @raise: Exception 更新失败时抛出异常
        """
        try:
            experiment = self.db.update_experiment(params)
            if not experiment or not experiment.id:
                raise Exception("实验不存在或更新失败")
            return ExperimentItem.model_validate(experiment)
        except Exception as e:
            byte_log.error(f"更新实验失败: {str(e)}")
            raise Exception(f"更新实验失败: {str(e)}")

    def delete_experiment(self, params: DeleteExperimentRequest) -> bool:
        """
        删除实验
        @param params: 删除参数
        @return: 是否成功
        @raise: Exception 删除失败时抛出异常
        """
        try:
            success = self.db.delete_experiment(params)
            if not success:
                raise Exception("实验不存在或删除失败")
            return True
        except Exception as e:
            byte_log.error(f"删除实验失败: {str(e)}")
            raise Exception(f"删除实验失败: {str(e)}")

    def get_experiment_detail(self, params: GetExperimentDetailRequest) -> ExperimentItem:
        """
        获取实验详情
        @param params: 查询参数
        @return: ExperimentItem
        @raise: Exception 查询失败时抛出异常
        """
        try:
            experiment = self.db.get_experiment_detail(params)
            if not experiment or not experiment.id:
                raise Exception("实验不存在")
            return ExperimentItem.model_validate(experiment)
        except Exception as e:
            byte_log.error(f"获取实验详情失败: {str(e)}")
            raise Exception(f"获取实验详情失败: {str(e)}")

    def handle_experiment_list(self, page: ListExperimentsRequest, total: int, items: List[ExperimentItem]) -> ExperimentListResponse:
        """
        处理实验列表数据
        @param page: 分页参数
        @param total: 总数
        @param items: 实验列表
        @return: ExperimentListResponse
        """
        return ExperimentListResponse(
            page=page.page,
            page_size=page.page_size,
            total=total,
            items=items
        )

    async def _async_view_flight_by_vid(self, vid):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, libra.view_flight_by_vid, vid)

    async def view_flight_by_vid(self, params: ViewFlightByVidRequest) -> list:
        """
        根据vid列表并发查询实验信息
        @param params: ViewFlightByVidRequest
        @return: list
        """
        tasks = [self._async_view_flight_by_vid(vid) for vid in params.vids]
        results = await asyncio.gather(*tasks)
        output = []
        for vid, result in zip(params.vids, results):
            if result and isinstance(result, dict) and "data" in result:
                output.append(result["data"])
            else:
                output.append({"vid": vid, "error": "未查到实验信息"})
        return output

perf_experiment_handler = PerfExperimentHandler()
