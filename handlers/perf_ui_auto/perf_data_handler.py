'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-10-23 19:31:54
FilePath: /global_rtc_test_platform/handlers/perf_ui_auto/perf_data_handler.py
Description: 性能数据处理模块
'''
from typing import Dict, Any, List

from dals.db.perf_ui_auto.perf_data_db import perf_data_db
from schemas.request.perf_ui_auto.perf_data_req import PerfDataReportParams

def get_report_data(params: PerfDataReportParams) -> List[Dict[str, Any]]:
    """
    获取任务报告数据
    @param params: 请求参数
    @return: List[Dict]
    """    
    data = perf_data_db.get_data_by_task(task_id=params.id)
    # version_type透传，无需特殊处理
    return data

def get_perf_data_tos_url(items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    获取性能数据TOS URL
    @param items: 数据项列表
    @return: List[Dict]
    """
    return items

def get_version_list_data(page, total, items) -> Dict[str, Any]:
    """
    获取版本列表数据
    @param page: 分页信息
    @param total: 总数
    @param items: 数据项列表
    @return: Dict
    """
    return {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "version_list": items
    }

def upload_perf_data(data: str) -> Dict[str, str]:
    """
    处理上传的性能数据
    @param data: 处理结果信息
    @return: Dict
    """
    return data


