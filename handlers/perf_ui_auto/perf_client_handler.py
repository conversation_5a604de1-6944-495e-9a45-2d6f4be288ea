from copy import deepcopy

def register_perf_client(data):
    """
    处理客户端注册响应
    
    @param data: 响应信息
    @return: 处理后的响应信息
    """
    return data


def get_client_list(total, items):
    client_items = []
    for client_item_tuple in items:
        client_item, business_item = client_item_tuple
        new_client_item = vars(deepcopy(client_item))
        new_client_item["business"] = business_item
        client_items.append(new_client_item)
    data = {
        "total": total,
        "client_items": client_items,
    }
    return data

def get_client_id_by_mac(mac_address: str):
    return mac_address

def update_client_detail(client):
    """
    处理更新后的客户端信息
    
    @param client: 更新后的客户端对象
    @return: 处理后的客户端信息
    """
    if not client:
        return None
    
    # 转换为字典并移除SQLAlchemy状态
    client_dict = vars(deepcopy(client))
    
    # 转换datetime为字符串
    if 'create_time' in client_dict:
        client_dict['create_time'] = client_dict['create_time'].strftime('%Y-%m-%d %H:%M:%S')
    if 'update_time' in client_dict:
        client_dict['update_time'] = client_dict['update_time'].strftime('%Y-%m-%d %H:%M:%S')
    
    return "更新客户端成功"

def get_client_detail(client_tuple):
    """
    处理客户端详情数据
    
    @param client_tuple: (PerfUiAutoClient, Business) 元组
    @return: 处理后的客户端详情数据
    """
    if not client_tuple:
        return None
    
    client_item, business_item = client_tuple
    client_data = vars(deepcopy(client_item))
    client_data["business"] = business_item
    return client_data