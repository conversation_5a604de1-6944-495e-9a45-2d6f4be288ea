import ast
from models.version_manager_list_model import *
from models.version_manager_detail_model import *
from copy import deepcopy
from utils.common.bytelog import byte_log
from dals.db.version_manager.version_manager_detail_db import version_manager_detail_db

def get_list_data(page, total, items):
    try:
        byte_log.info(f"开始处理版本列表数据，items类型: {type(items)}")
        new_items = []
        for item in items:
            try:
                byte_log.info(f"处理单个item，类型: {type(item)}，内容: {item}")
                
                # 创建新的 Version 对象并复制属性
                version_data = {
                    'id': item['id'],
                    'version_name': item['version_name'],
                    'version_qa_owner': item['version_qa_owner'],
                    'version_rd_owner': item['version_rd_owner'],
                    'platforms': item['platforms'],
                    'start_time': item['start_time'],
                    'end_time': item['end_time'],
                    'meego_epic_url': item['meego_epic_url'],
                    'meego_version_url': item['meego_version_url'],
                    'submit_to_test_doc_url': item['submit_to_test_doc_url'],
                    'api_change_doc_url': item['api_change_doc_url'],
                    'release_note_doc_url': item['release_note_doc_url'],
                    'create_time': item['create_time'],
                    'update_time': item['update_time']
                }
                
                new_version = Version(**version_data)
                
                # 处理 platforms 字段
                new_version.platforms = ast.literal_eval(new_version.platforms) \
                    if (new_version.platforms != '' and new_version.platforms is not None) else ast.literal_eval('[]')
                
                # 获取当前阶段信息
                current_stage = version_manager_detail_db.get_current_stage_by_version_id(version_id=item['id'])
                if current_stage:
                    setattr(new_version, 'current_stage', current_stage)
                else:
                    setattr(new_version, 'current_stage', None)
                
                new_items.append(new_version)
            except Exception as e:
                byte_log.error(f"处理单个版本数据失败，错误详情: {str(e)}")
                continue
            
        data = {
            'page': page.page,
            'page_size': page.page_size,
            'total': total,
            'items': new_items
        }
        return data
    except Exception as e:
        byte_log.error(f"处理版本列表数据失败: {str(e)}")
        return {
            'page': page.page,
            'page_size': page.page_size,
            'total': 0,
            'items': []
        }