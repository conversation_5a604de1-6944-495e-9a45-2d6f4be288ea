import ast
from copy import deepcopy

from schemas.inner_model.version_manager_todo_checklist import VersionManagerTodoChecklist
from typing import List
# from utils.common.log import custom_logger
from utils.common import time_util
from proj_settings import settings
from utils.common.bytelog import byte_log


def get_detail_data(item,stage_list,current_stage):
    item.stage_list = stage_list
    item.current_stage = current_stage
    new_item = deepcopy(item)
    new_item.platforms = ast.literal_eval(new_item.platforms) \
        if (new_item.platforms != '' and new_item.platforms is not None) else ast.literal_eval('[]')
    return new_item

def get_version_checklist_data(items):
    new_items =[]
    for item in items:
        checklist_item, version_checklist_item = item
        new_checklist_item = deepcopy(checklist_item)
        new_checklist_item.reminder = ast.literal_eval(new_checklist_item.reminder) \
            if (new_checklist_item.reminder != '' and new_checklist_item.reminder is not None) else ast.literal_eval('[]')
        version_checklist_item.checklist = new_checklist_item
        new_items.append(version_checklist_item)
    result = {
        'items': new_items
    }
    return result

def update_version_checklist(item):
    checklist_item, version_checklist_item = item
    new_checklist_item = deepcopy(checklist_item)
    new_checklist_item.reminder = ast.literal_eval(new_checklist_item.reminder) \
        if (new_checklist_item.reminder != '' and new_checklist_item.reminder is not None) else ast.literal_eval('[]')

    version_checklist_item.checklist = new_checklist_item
    return version_checklist_item

def get_todo_checklist_before_today(items):
    new_items = []
    for item in items:
        new_item = VersionManagerTodoChecklist(*item)
        new_items.append(new_item)
    return new_items


def add_todo_checklist(reminder_variable_map,date,reminder,item):
    tmp_todo_checklist = {
        "desc": "%s阶段-%s" %(item.stage_name,item.checklist_desc),
        "update_info": {
            "comment": "",
            "id": item.version_checklist_id
        }
    }
    host = settings.FRONT_END_HOST
    if item.remind_type == 2:   # 每日提醒的checklist
        tmp_todo_checklist["desc"] += " (%s)" % item.version_checklist_start_time.strftime("%Y-%m-%d")
    if reminder in reminder_variable_map:  # 已有该提醒人
        if item.version_name in reminder_variable_map.get(reminder):  # 已有该版本
            reminder_variable_map.get(reminder).get(item.version_name)["todo_checklist"].append(
                tmp_todo_checklist)
        else:
            reminder_variable_map.get(reminder)[item.version_name] = {
                "version": item.version_name,
                "date": date,
                "todo_checklist": [tmp_todo_checklist],
                "user_open_id":"",
                "version_detail_url":"%s/versionManager/versionDetail?version_id=%d" %(host,item.version_id)
            }
    else:
        reminder_variable_map[reminder] = {
            item.version_name: {
                "version": item.version_name,
                "date": date,
                "todo_checklist": [tmp_todo_checklist],
                "user_open_id":"",
                "version_detail_url":"%s/versionManager/versionDetail?version_id=%d" %(host,item.version_id)
            }
        }
def get_todo_checklist_card_variable_map(todo_checklist_list:List[VersionManagerTodoChecklist]):
    reminder_variable_map = {}
    date = time_util.get_current_date() #今天的日期
    for item in todo_checklist_list:
        if item.reminder_type == 2: # 提醒人不是版本负责人
            for tmp_reminder in item.reminder:
                add_todo_checklist(reminder_variable_map, date, tmp_reminder, item)
        else:   # 提醒人是版本负责人
            add_todo_checklist(reminder_variable_map, date, item.version_qa_owner, item)
    byte_log.info("get_todo_checklist_card_variable_map reminder_variable_map=%s" %reminder_variable_map)
    return reminder_variable_map




def checklist_card_callback():
    item = {
        'toast': {
            'type': 'info',
            'content': '接收成功'
        }
    }
    return item

def test_callback(params):
    item = {
        'challenge': params.challenge
    }
    return item

def card_callback(type,content_cn,content_us):
    result = {
        'toast': {
            "type": type,
            "content": content_cn,
            "i18n": {
                "zh_cn": content_cn,
                "en_us": content_us
            }
        }
    }
    return result