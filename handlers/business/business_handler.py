from models.libra_experiment.libra_experiment_review_model import LibraExperiment
from models.business.business_application_model import Business
from utils.common.bytelog import byte_log


def handle_fetch_all_data(total, items):
    data = {
        "total": total,
        "items": items
    }
    return data


def handle_add_business(business_id: int) -> dict:
    """
    处理新增业务响应数据
    @param business_id: 业务ID
    @return: 响应数据
    """
    if not business_id:
        byte_log.error("Failed to add business")
        return None
    
    data = {
        "id": business_id
    }
    return data


def handle_update_business(business: dict) -> dict:
    """
    处理更新业务响应数据
    @param business: 业务数据字典
    @return: 响应数据
    """
    if not business:
        byte_log.error("Failed to update business")
        return None
    
    return business