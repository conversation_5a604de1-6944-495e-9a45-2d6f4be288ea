import asyncio
import os
import aiohttp
from typing import Dict, Any, List, Optional
from utils.common.bytelog import byte_log
from proj_settings import settings

class StreamHandler:
    """简单的视频推流处理器"""
    
    _TEMP_DIR = os.path.join(settings.BASEDIR, "temp")  # 临时目录用于存储下载的视频
    _MAX_RETRIES = 3  # 最大重试次数
    _RETRY_DELAY = 5  # 重试延迟（秒）
    
    # 视频源URL映射
    _VIDEO_URL_MAP = {
        "1080P": "https://tosv.byted.org/obj/global-rtc-test-platform/tool_manager/push_video/1080P.mp4",
        "720P": "https://tosv.byted.org/obj/global-rtc-test-platform/tool_manager/push_video/720P.mp4"
    }

    # 预配置的推流目标 URL
    _STREAM_URLS = {
        "stage_720P": "rtmp://push-rtmp-f5-sg01.tiktokrow-cdn.com/stage/stream-2999232483470345717?c=unknown&expire=1802853831&l_region=US-East&sign=19cafc54f51f8eadb4ccb9e4ac003183&volcSecret=19cafc54f51f8eadb4ccb9e4ac003183&volcTime=1802853831",
        "stage_1080P": "rtmp://push-rtmp-f5-sg01.tiktokrow-cdn.com/stage/stream-2999232491951490549?c=unknown&expire=1802853912&l_region=US-East&sign=aa8076f6c048101614b356f216b2967c&volcSecret=aa8076f6c048101614b356f216b2967c&volcTime=1802853912",
        "game_720P": "rtmp://push-rtmp-f5-sg01.tiktokrow-cdn.com/game/stream-2999232619783128571?c=unknown&expire=1802855835&l_region=US-East&sign=4c08616712d20dd460d7c2808b9f38a8&volcSecret=4c08616712d20dd460d7c2808b9f38a8&volcTime=1802855835",
        "game_1080P": "rtmp://push-rtmp-f5-sg01.tiktokrow-cdn.com/game/stream-2999232634158056955?c=unknown&expire=1802856084&l_region=US-East&sign=b16088b87b46d252c40f1b802e95482e&volcSecret=b16088b87b46d252c40f1b802e95482e&volcTime=1802856084"
    }

    def __init__(self):
        self._processes: Dict[int, asyncio.subprocess.Process] = {}
        self._monitor_tasks: Dict[int, asyncio.Task] = {}

    async def _monitor_process(self, process: asyncio.subprocess.Process, video_name: str, target_url: str) -> None:
        """
        监控推流进程的状态
        
        Args:
            process: ffmpeg 进程
            video_name: 视频名称
            target_url: 推流目标地址
        """
        try:
            # 监控进程输出
            while True:
                line = await process.stderr.readline()
                if not line:
                    break
                    
                line = line.decode().strip()
                if line:
                    byte_log.info(f"[ffmpeg] {video_name} 输出: {line}")
                    
            # 等待进程结束
            return_code = await process.wait()
            if return_code != 0:
                byte_log.error(f"[ffmpeg] {video_name} 推流进程异常退出，返回码：{return_code}")
                # 触发重试
                await self._handle_stream_error(video_name, target_url)
            else:
                byte_log.info(f"[ffmpeg] {video_name} 推流进程正常退出")
                
        except Exception as e:
            byte_log.error(f"[ffmpeg] {video_name} 监控进程时发生错误：{str(e)}")
            # 触发重试
            await self._handle_stream_error(video_name, target_url)
        finally:
            # 清理资源
            if process.pid in self._processes:
                del self._processes[process.pid]
            if process.pid in self._monitor_tasks:
                del self._monitor_tasks[process.pid]

    async def _handle_stream_error(self, video_name: str, target_url: str) -> None:
        """
        处理推流错误，进行重试
        
        Args:
            video_name: 视频名称
            target_url: 推流目标地址
        """
        retry_count = 0
        while retry_count < self._MAX_RETRIES:
            retry_count += 1
            byte_log.warning(f"[ffmpeg] {video_name} 推流失败，正在进行第 {retry_count} 次重试...")
            
            # 等待一段时间后重试
            await asyncio.sleep(self._RETRY_DELAY)
            
            # 重新启动推流
            result = await self.push_stream(video_name, target_url)
            if result["status"] == "success":
                byte_log.info(f"[ffmpeg] {video_name} 重试成功")
                return
                
        byte_log.error(f"[ffmpeg] {video_name} 推流失败，已达到最大重试次数")

    async def _download_video(self, video_url: str, video_name: str) -> str:
        """
        下载视频到临时目录
        
        Args:
            video_url: 视频URL
            video_name: 视频名称
            
        Returns:
            str: 下载后的视频文件路径
        """
        # 确保临时目录存在
        os.makedirs(self._TEMP_DIR, exist_ok=True)
        
        # 构建本地文件路径
        file_extension = video_url.split('.')[-1]
        local_file_path = os.path.join(self._TEMP_DIR, f"{video_name}.{file_extension}")
        temp_file_path = f"{local_file_path}.tmp"
        
        # 如果文件已存在且大小正常，直接返回路径
        if os.path.exists(local_file_path):
            try:
                if os.path.getsize(local_file_path) > 0:
                    byte_log.info(f"[ffmpeg] 视频文件已存在且完整，跳过下载。路径：{local_file_path}")
                    return local_file_path
            except OSError:
                # 如果文件损坏或无法访问，删除它
                try:
                    os.remove(local_file_path)
                except OSError:
                    pass
        
        # 获取已下载的大小（用于断点续传）
        resume_size = 0
        if os.path.exists(temp_file_path):
            try:
                resume_size = os.path.getsize(temp_file_path)
            except OSError:
                resume_size = 0
        
        # 下载文件
        byte_log.info(f"[ffmpeg] 开始下载视频。URL：{video_url}")
        
        try:
            headers = {}
            if resume_size > 0:
                headers['Range'] = f'bytes={resume_size}-'
            
            async with aiohttp.ClientSession() as session:
                async with session.get(video_url, headers=headers) as response:
                    if response.status not in [200, 206]:
                        raise Exception(f"下载视频失败: HTTP {response.status}")
                    
                    # 使用临时文件
                    mode = 'ab' if resume_size > 0 else 'wb'
                    with open(temp_file_path, mode) as f:
                        async for chunk in response.content.iter_chunked(8192):
                            if not chunk:
                                break
                            f.write(chunk)
                            
            # 下载完成后，将临时文件重命名为正式文件
            os.replace(temp_file_path, local_file_path)
            byte_log.info(f"[ffmpeg] 视频下载完成。路径：{local_file_path}")
            return local_file_path
            
        except Exception as e:
            byte_log.error(f"[ffmpeg] 下载视频失败：{str(e)}")
            raise

    async def push_stream(self, video_name: str, target_url: str) -> Dict[str, Any]:
        """
        将视频推流到指定目标URL
        
        Args:
            video_name: 视频名称 ("720P" 或 "1080P")
            target_url: 推流目标地址
            
        Returns:
            Dict[str, Any]: 推流结果
        """
        try:
            # 获取视频URL
            video_url = self._VIDEO_URL_MAP.get(video_name)
            if not video_url:
                raise ValueError(f"不支持的视频类型: {video_name}")

            # 下载视频到本地
            local_video_path = await self._download_video(video_url, video_name)

            # 构建ffmpeg命令
            command = (
                f'ffmpeg -stream_loop -1 -re -i "{local_video_path}" '
                f'-c copy -f flv -loglevel warning "{target_url}"'
            )
            
            byte_log.info(f"[ffmpeg] 开始推流。视频：{video_name}，目标：{target_url}")
            
            # 执行推流命令
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            if not process or not process.pid:
                raise RuntimeError("启动推流进程失败")
                
            byte_log.info(f"[ffmpeg] 推流启动成功。进程ID：{process.pid}")
            
            # 保存进程引用
            self._processes[process.pid] = process
            
            # 启动监控任务
            monitor_task = asyncio.create_task(
                self._monitor_process(process, video_name, target_url)
            )
            self._monitor_tasks[process.pid] = monitor_task
            
            return {
                "status": "success",
                "message": "推流任务已启动",
                "pid": process.pid
            }
            
        except Exception as e:
            error_msg = f"推流失败：{str(e)}"
            byte_log.error(f"[ffmpeg] {error_msg}")
            return {
                "status": "error",
                "message": error_msg,
                "pid": None
            }

    async def startup_streaming(self) -> Dict[str, Any]:
        """
        启动所有预配置的视频推流
        
        Returns:
            Dict: 启动结果，包含所有推流进程的 ID
        """
        try:
            byte_log.info("[ffmpeg] 正在启动自动视频推流...")
            
            success_pids = []
            error_messages = []
            
            # 推流配置列表
            stream_configs = [
                ("720P", self._STREAM_URLS["stage_720P"]),
                ("1080P", self._STREAM_URLS["stage_1080P"]),
                ("720P", self._STREAM_URLS["game_720P"]),
                ("1080P", self._STREAM_URLS["game_1080P"])
            ]
            
            for video_name, target_url in stream_configs:
                result = await self.push_stream(
                    video_name=video_name,
                    target_url=target_url
                )
                
                if result["status"] == "success":
                    byte_log.info(f"[ffmpeg] {video_name} 视频推流启动成功，进程ID：{result['pid']}")
                    success_pids.append(result["pid"])
                else:
                    error_msg = f"{video_name} 视频推流启动失败：{result['message']}"
                    byte_log.error(f"[ffmpeg] {error_msg}")
                    error_messages.append(error_msg)
                
                # 在启动下一个推流之前稍作等待
                await asyncio.sleep(1)
                
            # 构建响应消息
            if error_messages:
                message = f"部分推流成功。错误信息：{'; '.join(error_messages)}"
            else:
                message = "所有视频推流启动成功"
                
            return {
                "pids": success_pids,
                "message": message
            }
                
        except Exception as e:
            error_msg = f"推流启动过程发生错误：{str(e)}"
            byte_log.error(f"[ffmpeg] {error_msg}")
            return {
                "pids": [],
                "message": error_msg
            }

ffmpeg_stream_handler = StreamHandler()