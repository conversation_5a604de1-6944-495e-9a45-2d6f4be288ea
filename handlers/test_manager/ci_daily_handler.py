import ast
from copy import deepcopy

from utils.ci_daily_util import Ci_Daily


def get_case_list_data(page, total, items):
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "items": items
    }
    return data


def get_branch_list_data(page, total, items):
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "branch_list": items
    }
    return data


def get_result_code_list_data(page, total, items):
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "result_code_list": items
    }
    return data


def get_attribute_type_list_data(page, total, items):
    data = {
        "page": page.page,
        "page_size": page.page_size,
        "total": total,
        "attribute_type_list": items
    }
    return data


def get_case_detail_data(test_task_item, test_platform_item, test_case_detail_item):
    test_platform_item.test_task = test_task_item
    test_case_detail_item.test_platform = test_platform_item
    new_test_case_detail_item = deepcopy(test_case_detail_item)
    new_test_case_detail_item.expect_assert = ast.literal_eval(test_case_detail_item.expect_assert)
    new_test_case_detail_item.actual_assert = ast.literal_eval(test_case_detail_item.actual_assert)
    new_test_case_detail_item.device_info_list = ast.literal_eval(test_case_detail_item.device_info_list)
    data = new_test_case_detail_item
    return data


def update_case_detail_data(case_detail):
    data = case_detail
    return data


def get_ci_daily_data(params):
    data = ""
    if 0 in params.pipeline_type or "0" in params.pipeline_type:
        data += "get ci data "
        ci_task = Ci_Daily(pipeline_type=0, days=params.get_data_days)
        ci_task.get_test_case_detail()
    if 1 in params.pipeline_type or "1" in params.pipeline_type:
        data += "get daily data "
        daily_task = Ci_Daily(pipeline_type=1, days=params.get_data_days)
        daily_task.get_test_case_detail()
    return data


def del_repeat_ci_daily_data(repeat_ci_daily):
    data = repeat_ci_daily
    return data
